公租房经租管理平台项目介绍
   公租房经租管理平台是一个现代化的公共租赁住房管理系统，旨在简化和优化公租房的申请、配租、合同管理、租金缴纳等全流程业务。系统采用纯HTML5+CSS+JS方式架构，一个功能一个页面的方式，响应式设计，支持本地文件直接访问页面方式，支持多终端访问。

核心功能模块

一、页面结构规划：7大核心模块与页面清单
模块	核心页面	主要功能元素	关联角色
1. 房源管理	房源总览页	地图定位筛选、VR看房入口、房源状态看板（可租/已锁/已租）	租户、住建管理员
房源详情页	360°全景展示、租金标准、配套设施清单、历史维修记录	租户、物业人员
2. 配租申请	认租申请页	资格预检提示、收入财产在线申报、意向房源多选（面积/区域）	轮候租户
摇号/选房页	实时排队号展示、倒计时选房、房源动态占用状态2	入围租户
3. 合同管理	电子签约页	合同条款分步高亮、人脸识别+身份证双重认证、共同居住人添加	租户、住建审核员
合同详情页	履约进度条（支付/交接）、同住人管理、变更历史记录	租户、物业
4. 租金管理	账单生成页	自动合并租金/水电/物业费、滞纳金计算器、历史欠费提醒	系统自动触发
支付页面	多通道聚合支付（微信/银联/代扣）、电子发票一键下载38	租户、社区代办员
5. 维修服务	报修提交页	故障分类（水电/家具/结构）、拍照/视频上传、紧急程度选择	租户
工单跟踪页	维修员实时定位、预计到达时间、完工照片对比	租户、维修员
6. 退租管理	退房申请页	退房原因选择（购房/迁离）、房屋现状视频上传、押金预结算提示49	租户
线上验房页	物业扫码打卡验房、损坏赔偿清单在线确认、押金原路退回倒计时	租户、物业
7. 动态监管	信用积分页	违规行为扣分明细（转租-20分）、优良记录奖励（优先换房）	租户、市级监管员
多部门核验看板	自动触发公安/民政/不动产数据比对、异常标签（收入超标/新增房产）	住建管理员


二、详细页面设计规范（以关键流程为例）
1. 配租申请流程页面
认租申请页

字段设计：

身份证OCR自动识别（减少手动输入）

意向房源选择器：支持按“面积区间≤10㎡”“租金档位”“学区需求”筛选2

经济状况声明：嵌入《承诺和授权书》电子签名2

交互规则：

未完成收入核验时，房源选择区置灰并提示“先提交财产核对”

提交后生成认租编码，短信同步推送

在线看房页

组件设计：

左侧：3D户型模型（支持旋转/缩放）

右侧：同小区房源对比表（租金/朝向/楼层）2

底部：预约看房按钮（线下需物业授权）

2. 合同管理页面
电子签约页

关键交互：

条款分步确认：每滚动至关键条款（如转租罚则）强制停留5秒

人脸识别：活体检测+公安库比对，失败时转人工审核

共同居住人添加：需上传身份证并关联人脸库（用于门禁授权）6

3. 租金支付页面
支付流程：
graph TD
  A[账单生成] --> B{支付方式选择}
  B -->|微信/支付宝| C[调取聚合支付SDK]
  B -->|线下代缴| D[生成缴费二维码]
  C --> E{支付成功？}
  E -->|是| F[自动开具电子发票]
  E -->|否| G[提示失败原因+重试入口]


设计要点：

代缴场景：社区代办员扫描租户二维码，现金支付后系统标记“代办人ID”3

滞纳金提示：欠费超30天时，支付按钮显示红色角标“+滞纳金XX元”



4. 退租流程页面
线上验房组件：

物业人员端：

扫码触发验房任务 → 上传水电气表底数照片 → 勾选设施完好项（墙面/门窗/厨卫）

系统自动比对入住时存档照片，标定损坏差异4

租户端：

实时查看验房进度 → 在线签署赔偿确认书 → 押金退回倒计时（72小时）



三、交互设计规范
1. 全局规则
权限控制：

物业人员仅能操作本小区数据，跨小区访问时弹出“越权警示”6

异常处理：

数据核验失败时，显示具体驳回部门（如“民政系统返回收入异常”）并提供申诉入口2

2. 页面级规则
列表页：

支持“智能排序”：欠费租户置顶、合同临期（<30天）标黄

批量操作：住建管理员可批量冻结违规账户（勾选+批量动作菜单）

表单页：

必填项验证：未完成时滚动定位至首个错误项并抖动提示

草稿自动保存：中断操作后重新进入可恢复未提交数据

3. 多端适配规范
设备类型	设计重点	案例参考
PC管理端	多标签页工作台、数据看板实时刷新	智慧公租房综合管理平台
移动租户端	语音导航缴费、拍照报修、人脸识别	新余市微信公租房平台
物业平板端	离线验房模式、扫码枪集成、手写签名	孝南退房流程



四、特殊场景设计
无障碍设计：

视力障碍：账单支持语音朗读（金额/截止日期）

老年人模式：字体放大+流程向导式导航（如退房分步指引）

离线应急场景：

物业验房无网络时，本地存储验房记录，联网后自动同步

支付中断时生成临时缴费码，24小时内有效

安全风控：

敏感操作（合同终止/资格冻结）需二级审批（经办人+主管）

数据修改留痕：任何房源/租金变更强制填写变更理由



五、原型交付物建议
流程图：配租、退房等核心流程的泳道图（区分租户/物业/住建角色）

组件库：

专用图标集（如“信用积分”“VR看房”）

表单校验规则文档（如身份证/银行卡正则表达式）

交互说明文档：

标注所有异常状态（如摇号服务器拥堵时的排队动画）

权限变更触发机制（如租户新增车辆自动触发财产复核）

设计验证要点：

通过用户角色矩阵（见前表）验证页面权限闭环性6；

关键路径（如支付）需覆盖断网/支付失败/并发冲突等场景；

与物联网设备联动：人脸门禁授权需与合同状态实时同步。