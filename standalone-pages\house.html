<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公租房经租管理平台</title>
    <link rel="stylesheet" href="assets/element-ui.css">
    <link rel="stylesheet" href="element-ui.css">
    <style>
        body {
            font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            overflow-x: hidden;
        }
        
        #app {
            min-height: 100vh;
            display: flex;
        }

        .header {
            background-color: #001529;
            color: white;
            padding: 12px 16px;
        }

        .logo {
            display: flex;
            align-items: center;
        }

        .logo i {
            font-size: 18px;
            margin-right: 6px;
        }

        .logo span {
            font-size: 14px;
            font-weight: bold;
            white-space: nowrap;
        }

        .sidebar {
            width: 200px !important;
            min-width: 200px !important;
            max-width: 200px !important;
            background-color: #001529;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
        }

        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: #f5f7fa;
            min-width: 0;
            overflow-x: hidden;
        }

        .top-header {
            background: white;
            padding: 8px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .content {
            flex: 1;
            padding: 24px 24px 24px 24px;
            overflow-y: auto;
            overflow-x: hidden;
            min-width: 0;
        }

        .tab-content h2 {
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
        
        .filter-section {
            margin-bottom: 20px;
        }
        
        .tab-content {
            padding-top: 0;
        }

        /* 导航栏样式 */
        .sidebar-nav {
            flex: 1;
            padding: 0;
            margin-top: 10px;
        }

        .sidebar-nav .el-menu {
            background-color: #001529;
            border: none;
        }

        .sidebar-nav .el-menu-item {
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            height: 48px;
            line-height: 48px;
            border: none;
            margin: 2px 8px;
            border-radius: 6px;
            padding: 0 16px !important;
            position: relative;
        }

        .sidebar-nav .el-menu-item i {
            margin-right: 8px;
            font-size: 16px;
            width: 16px;
            text-align: center;
        }

        .sidebar-nav .el-menu-item:hover {
            background-color: rgba(24, 144, 255, 0.1);
            color: #1890ff;
        }

        .sidebar-nav .el-menu-item.is-active {
            background-color: #1890ff;
            color: white;
            font-weight: 500;
        }

        .sidebar-nav .el-menu-item.is-active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background-color: white;
            border-radius: 0 2px 2px 0;
        }

        .el-table {
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .el-table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
        }

        .el-table .el-table__row:hover {
            background: #f8f9fa;
        }

        .el-button {
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .el-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .el-card {
            border-radius: 12px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .el-card:hover {
            box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
        }

        .el-form-item__label {
            font-weight: 500;
            color: #495057;
        }

        .el-input__inner, .el-select .el-input__inner, .el-textarea__inner {
            border-radius: 6px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .el-input__inner:focus, .el-select .el-input__inner:focus, .el-textarea__inner:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .el-pagination {
            text-align: center;
            margin-top: 20px;
        }

        .el-pagination .el-pager li {
            border-radius: 6px;
            margin: 0 2px;
        }

        .el-tag {
            border-radius: 12px;
            font-weight: 500;
        }

        .el-dialog {
            border-radius: 12px;
            overflow: hidden;
        }

        .el-dialog__header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
        }

        .el-dialog__title {
            font-weight: 600;
            font-size: 18px;
        }

        .el-dialog__headerbtn .el-dialog__close {
            color: white;
            font-size: 20px;
        }

        .el-dialog__body {
            padding: 30px;
        }

        .el-dialog__footer {
            padding: 20px 30px;
            background: #f8f9fa;
        }

        /* 面包屑导航样式 */
        .breadcrumb-container {
            margin-bottom: 15px;
            padding: 10px 0;
        }

        .el-breadcrumb {
            font-size: 14px;
        }

        .el-breadcrumb__item {
            color: #909399;
        }

        .el-breadcrumb__item:last-child {
            color: #409EFF;
            font-weight: 500;
        }

        /* 防止水平滚动条的样式 */
        .table-container {
            overflow-x: auto;
            margin: -1px;
        }

        .el-table {
            min-width: 800px;
        }

        .el-card {
            overflow: hidden;
        }

        .tab-content {
            max-width: 100%;
            overflow-x: hidden;
        }

        .el-row {
            max-width: 100%;
        }

        .el-col {
            min-width: 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <!-- 页面头部 -->
            <div class="header">
                <div class="logo">
                    <i class="el-icon-office-building"></i>
                    <span>公租房经租管理平台</span>
                </div>
            </div>

            <!-- 导航菜单 -->
            <div class="sidebar-nav">
                <el-menu mode="vertical" default-active="house" @select="handleTabChange" :collapse="false">
                    <el-menu-item index="home">
                        <i class="el-icon-s-home"></i>
                        <span>首页</span>
                    </el-menu-item>
                    <el-menu-item index="house">
                        <i class="el-icon-house"></i>
                        <span>房源管理</span>
                    </el-menu-item>
                    <el-menu-item index="person">
                        <i class="el-icon-user-solid"></i>
                        <span>人员管理</span>
                    </el-menu-item>
                    <el-menu-item index="apply">
                        <i class="el-icon-document-add"></i>
                        <span>配租申请</span>
                    </el-menu-item>
                    <el-menu-item index="contract">
                        <i class="el-icon-document"></i>
                        <span>合同管理</span>
                    </el-menu-item>
                    <el-menu-item index="payment">
                        <i class="el-icon-money"></i>
                        <span>租金管理</span>
                    </el-menu-item>
                    <el-menu-item index="repair">
                        <i class="el-icon-setting"></i>
                        <span>维修服务</span>
                    </el-menu-item>
                    <el-menu-item index="exit">
                        <i class="el-icon-switch-button"></i>
                        <span>退租管理</span>
                    </el-menu-item>
                    <el-menu-item index="monitor">
                        <i class="el-icon-view"></i>
                        <span>动态监管</span>
                    </el-menu-item>
                </el-menu>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-container">
            <!-- 顶部标题栏 -->
            <div class="top-header">
                <!-- 面包屑导航 -->
                <div class="breadcrumb-container">
                    <el-breadcrumb separator="/">
                        <el-breadcrumb-item>
                            <i class="el-icon-s-home"></i> 首页
                        </el-breadcrumb-item>
                        <el-breadcrumb-item>
                            房源管理
                        </el-breadcrumb-item>
                    </el-breadcrumb>
                </div>

                <div class="user-info">
                    <el-dropdown>
                        <span class="el-dropdown-link" style="color: #606266; cursor: pointer;">
                            <i class="el-icon-user"></i> 张三 <i class="el-icon-arrow-down"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item>个人中心</el-dropdown-item>
                            <el-dropdown-item>退出登录</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
            </div>

            <!-- 页面内容 -->
            <div class="content">
                <!-- 房源管理内容 -->
                <div class="tab-content">
                    
                    <!-- 搜索筛选区域 -->
                    <el-card class="filter-section">
                        <el-form :inline="true" :model="houseFilterForm" size="small">
                            <el-form-item label="小区名称">
                                <el-input v-model="houseFilterForm.community" placeholder="请输入小区名称" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="幢">
                                <el-input v-model="houseFilterForm.building" placeholder="请输入幢号" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="单元">
                                <el-input v-model="houseFilterForm.unit" placeholder="请输入单元号" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="房间">
                                <el-input v-model="houseFilterForm.room" placeholder="请输入房间号" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="状态">
                                <el-select v-model="houseFilterForm.status" placeholder="选择状态" clearable>
                                    <el-option label="全部状态" value=""></el-option>
                                    <el-option label="可租" value="可租"></el-option>
                                    <el-option label="已租" value="已租"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="searchHouses">
                                    <i class="el-icon-search"></i> 搜索
                                </el-button>
                                <el-button @click="resetHouseFilter">
                                    <i class="el-icon-refresh-left"></i> 重置
                                </el-button>
                            </el-form-item>
                            <el-form-item style="float: right;">
                                <el-button type="primary" @click="showHouseDialog = true">
                                    <i class="el-icon-plus"></i> 新增房源
                                </el-button>
                                <el-button type="success" @click="showBatchGenerateDialog = true">
                                    <i class="el-icon-document-add"></i> 批量生成
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </el-card>
                    
                    <!-- 房源列表 -->
                    <el-card style="margin-top: 20px;">
                        <el-table
                            :data="houses"
                            style="width: 100%">
                            <el-table-column prop="community" label="小区名称" width="150"></el-table-column>
                            <el-table-column prop="building" label="幢" width="80"></el-table-column>
                            <el-table-column prop="unit" label="单元" width="80"></el-table-column>
                            <el-table-column prop="room" label="房间" width="80"></el-table-column>
                            <el-table-column prop="status" label="状态" width="100">
                                <template slot-scope="scope">
                                    <el-tag :type="scope.row.status === '可租' ? 'success' : 'info'">
                                        {{ scope.row.status }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="area" label="面积(㎡)" width="100"></el-table-column>
                            <el-table-column prop="roomType" label="户型" width="120"></el-table-column>
                            <el-table-column prop="price" label="月租金(元)" width="120">
                                <template slot-scope="scope">
                                    <span>{{ scope.row.price.toLocaleString() }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="150" fixed="right">
                                <template slot-scope="scope">
                                    <el-button size="mini" type="primary" @click="editHouse(scope.row)">编辑</el-button>
                                    <el-button size="mini" type="danger" @click="deleteHouse(scope.row)">删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>

                        <!-- 分页 -->
                        <div style="text-align: center; margin-top: 20px;">
                            <el-pagination
                                @current-change="handleHouseCurrentChange"
                                :current-page.sync="houseCurrentPage"
                                :page-size="housePageSize"
                                layout="prev, pager, next, jumper"
                                :total="totalHouses">
                            </el-pagination>
                        </div>
                    </el-card>

                    <!-- 房源新增/编辑对话框 -->
                    <el-dialog
                        :title="getHouseDialogTitle()"
                        :visible.sync="showHouseDialog"
                        width="60%"
                        @close="resetHouseForm">
                        <el-form :model="houseForm" :rules="houseRules" ref="houseForm" label-width="120px">
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="小区名称" prop="community">
                                        <el-input v-model="houseForm.community" placeholder="请输入小区名称"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="幢" prop="building">
                                        <el-input v-model="houseForm.building" placeholder="请输入幢号"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="单元" prop="unit">
                                        <el-input v-model="houseForm.unit" placeholder="请输入单元号"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="房间" prop="room">
                                        <el-input v-model="houseForm.room" placeholder="请输入房间号"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="户型" prop="roomType">
                                        <el-select v-model="houseForm.roomType" style="width: 100%">
                                            <el-option label="一室一厅" value="一室一厅"></el-option>
                                            <el-option label="两室一厅" value="两室一厅"></el-option>
                                            <el-option label="三室一厅" value="三室一厅"></el-option>
                                            <el-option label="三室两厅" value="三室两厅"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="面积" prop="area">
                                        <el-input-number v-model="houseForm.area" :min="10" :max="200" style="width: 100%"></el-input-number>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="月租金" prop="price">
                                        <el-input-number v-model="houseForm.price" :min="100" :step="50" style="width: 100%"></el-input-number>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="状态" prop="status">
                                        <el-select v-model="houseForm.status" style="width: 100%">
                                            <el-option label="可租" value="可租"></el-option>
                                            <el-option label="已租" value="已租"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                        <div slot="footer">
                            <el-button @click="showHouseDialog = false">取消</el-button>
                            <el-button type="primary" @click="saveHouse">确定</el-button>
                        </div>
                    </el-dialog>

                    <!-- 批量生成对话框 -->
                    <el-dialog
                        title="批量生成房源"
                        :visible.sync="showBatchGenerateDialog"
                        width="50%"
                        @close="resetBatchForm">
                        <el-form :model="batchForm" :rules="batchRules" ref="batchForm" label-width="120px">
                            <el-form-item label="小区名称" prop="community">
                                <el-input v-model="batchForm.community" placeholder="请输入小区名称"></el-input>
                            </el-form-item>
                            <el-form-item label="幢号范围" prop="buildingRange">
                                <el-input-number v-model="batchForm.buildingStart" :min="1" placeholder="起始幢号" style="width: 120px;"></el-input-number>
                                <span style="margin: 0 10px;">至</span>
                                <el-input-number v-model="batchForm.buildingEnd" :min="1" placeholder="结束幢号" style="width: 120px;"></el-input-number>
                            </el-form-item>
                            <el-form-item label="单元数量" prop="unitCount">
                                <el-input-number v-model="batchForm.unitCount" :min="1" :max="10" style="width: 200px;"></el-input-number>
                            </el-form-item>
                            <el-form-item label="每层房间数" prop="roomsPerFloor">
                                <el-input-number v-model="batchForm.roomsPerFloor" :min="1" :max="20" style="width: 200px;"></el-input-number>
                            </el-form-item>
                            <el-form-item label="楼层数" prop="floors">
                                <el-input-number v-model="batchForm.floors" :min="1" :max="50" style="width: 200px;"></el-input-number>
                            </el-form-item>
                            <el-form-item label="户型" prop="roomType">
                                <el-select v-model="batchForm.roomType" style="width: 200px;">
                                    <el-option label="一室一厅" value="一室一厅"></el-option>
                                    <el-option label="两室一厅" value="两室一厅"></el-option>
                                    <el-option label="三室一厅" value="三室一厅"></el-option>
                                    <el-option label="三室两厅" value="三室两厅"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="面积" prop="area">
                                <el-input-number v-model="batchForm.area" :min="10" :max="200" style="width: 200px;"></el-input-number>
                            </el-form-item>
                            <el-form-item label="月租金" prop="price">
                                <el-input-number v-model="batchForm.price" :min="100" :step="50" style="width: 200px;"></el-input-number>
                            </el-form-item>
                        </el-form>
                        <div slot="footer">
                            <el-button @click="showBatchGenerateDialog = false">取消</el-button>
                            <el-button type="primary" @click="batchGenerateHouses">确定生成</el-button>
                        </div>
                    </el-dialog>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/vue.js"></script>
    <script src="assets/element-ui.js"></script>
    <script>
        new Vue({
            el: '#app',
            data: {
                houseFilterForm: {
                    community: '',
                    building: '',
                    unit: '',
                    room: '',
                    status: ''
                },
                houseCurrentPage: 1,
                housePageSize: 10,
                totalHouses: 50,
                showHouseDialog: false,
                showBatchGenerateDialog: false,
                houseForm: {
                    community: '',
                    building: '',
                    unit: '',
                    room: '',
                    roomType: '',
                    area: null,
                    price: null,
                    status: '可租'
                },
                batchForm: {
                    community: '',
                    buildingStart: 1,
                    buildingEnd: 1,
                    unitCount: 1,
                    roomsPerFloor: 4,
                    floors: 6,
                    roomType: '一室一厅',
                    area: 45,
                    price: 800
                },
                houses: [
                    { community: '鑫悦花园公租房小区', building: 1, unit: 1, room: 101, status: '可租', area: 45, roomType: '一室一厅', price: 800 },
                    { community: '鑫悦花园公租房小区', building: 1, unit: 1, room: 102, status: '可租', area: 60, roomType: '两室一厅', price: 1200 },
                    { community: '光明新区公租房小区', building: 2, unit: 1, room: 201, status: '可租', area: 55, roomType: '两室一厅', price: 1000 },
                    { community: '光明新区公租房小区', building: 2, unit: 2, room: 301, status: '已租', area: 50, roomType: '一室一厅', price: 950 },
                    { community: '万科城市公寓小区', building: 3, unit: 1, room: 401, status: '可租', area: 75, roomType: '三室一厅', price: 1500 },
                    { community: '万科城市公寓小区', building: 3, unit: 2, room: 501, status: '已租', area: 65, roomType: '两室一厅', price: 1100 }
                ]
            },
            methods: {
                handleTabChange(tab) {
                    // 导航到对应页面
                    if (tab !== 'house') {
                        window.location.href = tab + '.html';
                    }
                },
                searchHouses() {
                    this.$message.success('搜索功能');
                },
                resetHouseFilter() {
                    this.houseFilterForm = {
                        community: '',
                        building: '',
                        unit: '',
                        room: '',
                        status: ''
                    };
                },
                editHouse(row) {
                    this.$message.info('编辑房源: ' + row.community);
                },
                deleteHouse(row) {
                    this.$confirm('确认删除该房源吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.$message.success('删除成功');
                    });
                },
                handleHouseCurrentChange(page) {
                    this.houseCurrentPage = page;
                },
                getHouseDialogTitle() {
                    return '新增房源';
                },
                resetHouseForm() {
                    this.houseForm = {
                        community: '',
                        building: '',
                        unit: '',
                        room: '',
                        roomType: '',
                        area: null,
                        price: null,
                        status: '可租'
                    };
                },
                saveHouse() {
                    this.$message.success('保存成功');
                    this.showHouseDialog = false;
                },
                resetBatchForm() {
                    this.batchForm = {
                        community: '',
                        buildingStart: 1,
                        buildingEnd: 1,
                        unitCount: 1,
                        roomsPerFloor: 4,
                        floors: 6,
                        roomType: '一室一厅',
                        area: 45,
                        price: 800
                    };
                },
                batchGenerateHouses() {
                    this.$message.success('批量生成成功');
                    this.showBatchGenerateDialog = false;
                }
            }
        });
    </script>
</body>
</html>
