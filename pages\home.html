<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 公租房智慧管理平台</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 240px;
            background-color: #001529;
            color: white;
            padding: 12px 16px;
        }

        .logo {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            font-size: 18px;
            font-weight: bold;
        }

        .logo i {
            font-size: 24px;
            margin-right: 8px;
            color: #1890ff;
        }

        .sidebar-nav {
            flex: 1;
        }

        .sidebar-nav .el-menu {
            background-color: #001529;
            border: none;
        }

        .sidebar-nav .el-menu-item {
            color: rgba(255, 255, 255, 0.65);
            line-height: 48px;
            border: none;
            margin: 2px 8px;
            border-radius: 6px;
            padding: 0 16px !important;
            transition: all 0.3s;
        }

        .sidebar-nav .el-menu-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar-nav .el-menu-item.is-active {
            background-color: #1890ff;
            color: white;
        }

        .sidebar-nav .el-menu-item i {
            margin-right: 8px;
            font-size: 16px;
        }

        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-width: 0;
            overflow-x: hidden;
        }

        .top-header {
            background: white;
            padding: 8px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
        }

        .breadcrumb-container {
            margin-bottom: 15px;
            padding: 10px 0;
        }

        .el-breadcrumb {
            font-size: 14px;
        }

        .el-breadcrumb__item {
            color: #909399;
        }

        .el-breadcrumb__item:last-child {
            color: #409EFF;
            font-weight: 500;
        }

        .content-area {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }

        .statistics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .stat-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 24px;
            color: white;
        }

        .stat-icon.blue { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-icon.green { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stat-icon.orange { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stat-icon.purple { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

        .stat-info h3 {
            font-size: 14px;
            color: #666;
            margin: 0;
            font-weight: normal;
        }

        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            margin: 8px 0;
        }

        .stat-trend {
            font-size: 12px;
            color: #52c41a;
        }

        .footer {
            background: white;
            text-align: center;
            padding: 15px;
            color: #909399;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 左侧导航栏 -->
            <div class="sidebar">
                <div class="logo">
                    <i class="el-icon-s-home"></i>
                    公租房智慧管理平台
                </div>

                <!-- 导航菜单 -->
                <div class="sidebar-nav">
                    <el-menu mode="vertical" default-active="home" :collapse="false">
                        <el-menu-item index="home" @click="navigateTo('home.html')">
                            <i class="el-icon-s-home"></i>
                            <span>首页</span>
                        </el-menu-item>
                        <el-menu-item index="house" @click="navigateTo('house.html')">
                            <i class="el-icon-house"></i>
                            <span>房源管理</span>
                        </el-menu-item>
                        <el-menu-item index="person" @click="navigateTo('person.html')">
                            <i class="el-icon-user-solid"></i>
                            <span>人员管理</span>
                        </el-menu-item>
                        <el-menu-item index="apply" @click="navigateTo('apply.html')">
                            <i class="el-icon-document-add"></i>
                            <span>配租申请</span>
                        </el-menu-item>
                        <el-menu-item index="contract" @click="navigateTo('contract.html')">
                            <i class="el-icon-document"></i>
                            <span>合同管理</span>
                        </el-menu-item>
                        <el-menu-item index="payment" @click="navigateTo('payment.html')">
                            <i class="el-icon-money"></i>
                            <span>租金管理</span>
                        </el-menu-item>
                        <el-menu-item index="repair" @click="navigateTo('repair.html')">
                            <i class="el-icon-setting"></i>
                            <span>维修服务</span>
                        </el-menu-item>
                        <el-menu-item index="exit" @click="navigateTo('exit.html')">
                            <i class="el-icon-switch-button"></i>
                            <span>退租管理</span>
                        </el-menu-item>
                        <el-menu-item index="monitor" @click="navigateTo('monitor.html')">
                            <i class="el-icon-view"></i>
                            <span>动态监管</span>
                        </el-menu-item>
                    </el-menu>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-container">
                <!-- 顶部标题栏 -->
                <div class="top-header">
                    <!-- 面包屑导航 -->
                    <div class="breadcrumb-container">
                        <el-breadcrumb separator="/">
                            <el-breadcrumb-item>首页</el-breadcrumb-item>
                        </el-breadcrumb>
                    </div>
                    
                    <!-- 用户信息 -->
                    <div class="user-info">
                        <span>张三</span>
                        <el-button type="text" size="small" @click="logout">退出</el-button>
                    </div>
                </div>

                <!-- 内容区域 -->
                <div class="content-area">
                    <!-- 统计卡片 -->
                    <div class="statistics-grid">
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon blue">
                                    <i class="el-icon-house"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>房源总数</h3>
                                    <div class="stat-value">1,234</div>
                                    <div class="stat-trend">↑ 12% 较上月</div>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon green">
                                    <i class="el-icon-user"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>租户总数</h3>
                                    <div class="stat-value">856</div>
                                    <div class="stat-trend">↑ 8% 较上月</div>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon orange">
                                    <i class="el-icon-document"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>合同总数</h3>
                                    <div class="stat-value">642</div>
                                    <div class="stat-trend">↑ 15% 较上月</div>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon purple">
                                    <i class="el-icon-money"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>本月收入</h3>
                                    <div class="stat-value">¥128万</div>
                                    <div class="stat-trend">↑ 5% 较上月</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 页面底部 -->
                <div class="footer">
                    © 2023 公租房智慧社区云平台
                </div>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            methods: {
                navigateTo(page) {
                    window.location.href = page;
                },
                logout() {
                    this.$confirm('确定要退出登录吗?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        window.location.href = '../index.html';
                    });
                }
            }
        });
    </script>
</body>
</html>
