<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公租房经租管理平台</title>
    <link rel="stylesheet" href="assets/element-ui.css">
    <link rel="stylesheet" href="element-ui.css">
    <style>
        body { font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f7fa; overflow-x: hidden; }
        #app { min-height: 100vh; display: flex; }
        .header { background-color: #001529; color: white; padding: 12px 16px; }
        .logo { display: flex; align-items: center; }
        .logo i { font-size: 18px; margin-right: 6px; }
        .logo span { font-size: 14px; font-weight: bold; white-space: nowrap; }
        .sidebar { width: 200px !important; min-width: 200px !important; max-width: 200px !important; background-color: #001529; min-height: 100vh; display: flex; flex-direction: column; box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1); flex-shrink: 0; }
        .main-container { flex: 1; display: flex; flex-direction: column; background-color: #f5f7fa; min-width: 0; overflow-x: hidden; }
        .top-header { background: white; padding: 8px 24px; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08); }
        .user-info { display: flex; align-items: center; }
        .content { flex: 1; padding: 24px 24px 24px 24px; overflow-y: auto; overflow-x: hidden; min-width: 0; }
        .tab-content h2 { margin-top: 0; margin-bottom: 20px; font-size: 18px; font-weight: 600; color: #262626; }
        .filter-section { margin-bottom: 20px; }
        .tab-content { padding-top: 0; }
        .sidebar-nav { flex: 1; padding: 0; margin-top: 10px; }
        .sidebar-nav .el-menu { background-color: #001529; border: none; }
        .sidebar-nav .el-menu-item { color: rgba(255, 255, 255, 0.65); font-size: 14px; height: 48px; line-height: 48px; border: none; margin: 2px 8px; border-radius: 6px; padding: 0 16px !important; position: relative; }
        .sidebar-nav .el-menu-item i { margin-right: 8px; font-size: 16px; width: 16px; text-align: center; }
        .sidebar-nav .el-menu-item:hover { background-color: rgba(24, 144, 255, 0.1); color: #1890ff; }
        .sidebar-nav .el-menu-item.is-active { background-color: #1890ff; color: white; font-weight: 500; }
        .sidebar-nav .el-menu-item.is-active::before { content: ''; position: absolute; left: 0; top: 50%; transform: translateY(-50%); width: 3px; height: 20px; background-color: white; border-radius: 0 2px 2px 0; }
        .el-table { box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); border-radius: 8px; overflow: hidden; }
        .el-table th { background: #f8f9fa; color: #495057; font-weight: 600; }
        .el-table .el-table__row:hover { background: #f8f9fa; }
        .el-button { border-radius: 6px; font-weight: 500; transition: all 0.3s ease; }
        .el-button:hover { transform: translateY(-1px); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); }
        .el-card { border-radius: 12px; box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); transition: all 0.3s ease; }
        .el-card:hover { box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15); }
        .el-form-item__label { font-weight: 500; color: #495057; }
        .el-input__inner, .el-select .el-input__inner, .el-textarea__inner { border-radius: 6px; border: 1px solid #e9ecef; transition: all 0.3s ease; }
        .el-input__inner:focus, .el-select .el-input__inner:focus, .el-textarea__inner:focus { border-color: #667eea; box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2); }
        .el-pagination { text-align: center; margin-top: 20px; }
        .el-pagination .el-pager li { border-radius: 6px; margin: 0 2px; }
        .el-tag { border-radius: 12px; font-weight: 500; }
        .el-dialog { border-radius: 12px; overflow: hidden; }
        .el-dialog__header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; }
        .el-dialog__title { font-weight: 600; font-size: 18px; }
        .el-dialog__headerbtn .el-dialog__close { color: white; font-size: 20px; }
        .el-dialog__body { padding: 30px; }
        .el-dialog__footer { padding: 20px 30px; background: #f8f9fa; }
        .breadcrumb-container { margin-bottom: 15px; padding: 10px 0; }
        .el-breadcrumb { font-size: 14px; }
        .el-breadcrumb__item { color: #909399; }
        .el-breadcrumb__item:last-child { color: #409EFF; font-weight: 500; }
        .table-container { overflow-x: auto; margin: -1px; }
        .el-table { min-width: 800px; }
        .el-card { overflow: hidden; }
        .tab-content { max-width: 100%; overflow-x: hidden; }
        .el-row { max-width: 100%; }
        .el-col { min-width: 0; }
    </style>
</head>
<body>
    <div id="app">
        <div class="sidebar">
            <div class="header">
                <div class="logo"><i class="el-icon-office-building"></i><span>公租房经租管理平台</span></div>
            </div>
            <div class="sidebar-nav">
                <el-menu mode="vertical" default-active="monitor" @select="handleTabChange" :collapse="false">
                    <el-menu-item index="home"><i class="el-icon-s-home"></i><span>首页</span></el-menu-item>
                    <el-menu-item index="house"><i class="el-icon-house"></i><span>房源管理</span></el-menu-item>
                    <el-menu-item index="person"><i class="el-icon-user-solid"></i><span>人员管理</span></el-menu-item>
                    <el-menu-item index="apply"><i class="el-icon-document-add"></i><span>配租申请</span></el-menu-item>
                    <el-menu-item index="contract"><i class="el-icon-document"></i><span>合同管理</span></el-menu-item>
                    <el-menu-item index="payment"><i class="el-icon-money"></i><span>租金管理</span></el-menu-item>
                    <el-menu-item index="repair"><i class="el-icon-setting"></i><span>维修服务</span></el-menu-item>
                    <el-menu-item index="exit"><i class="el-icon-switch-button"></i><span>退租管理</span></el-menu-item>
                    <el-menu-item index="monitor"><i class="el-icon-view"></i><span>动态监管</span></el-menu-item>
                </el-menu>
            </div>
        </div>
        <div class="main-container">
            <div class="top-header">
                <div class="breadcrumb-container">
                    <el-breadcrumb separator="/"><el-breadcrumb-item><i class="el-icon-s-home"></i> 首页</el-breadcrumb-item><el-breadcrumb-item>动态监管</el-breadcrumb-item></el-breadcrumb>
                </div>
                <div class="user-info">
                    <el-dropdown>
                        <span class="el-dropdown-link" style="color: #606266; cursor: pointer;"><i class="el-icon-user"></i> 张三 <i class="el-icon-arrow-down"></i></span>
                        <el-dropdown-menu slot="dropdown"><el-dropdown-item>个人中心</el-dropdown-item><el-dropdown-item>退出登录</el-dropdown-item></el-dropdown-menu>
                    </el-dropdown>
                </div>
            </div>
            <div class="content">
                <div class="tab-content">
                    <!-- 监管统计卡片 -->
                    <el-row :gutter="20" style="margin-bottom: 20px;">
                        <el-col :span="6">
                            <el-card shadow="hover">
                                <div style="display: flex; align-items: center;">
                                    <i class="el-icon-warning" style="font-size: 40px; margin-right: 15px; color: #F56C6C;"></i>
                                    <div>
                                        <div style="font-size: 24px; font-weight: bold;">{{ alerts.filter(alert => alert.level === '高风险').length }}</div>
                                        <div>高风险预警</div>
                                    </div>
                                </div>
                            </el-card>
                        </el-col>
                        <el-col :span="6">
                            <el-card shadow="hover">
                                <div style="display: flex; align-items: center;">
                                    <i class="el-icon-info" style="font-size: 40px; margin-right: 15px; color: #E6A23C;"></i>
                                    <div>
                                        <div style="font-size: 24px; font-weight: bold;">{{ alerts.filter(alert => alert.level === '中风险').length }}</div>
                                        <div>中风险预警</div>
                                    </div>
                                </div>
                            </el-card>
                        </el-col>
                        <el-col :span="6">
                            <el-card shadow="hover">
                                <div style="display: flex; align-items: center;">
                                    <i class="el-icon-success" style="font-size: 40px; margin-right: 15px; color: #67C23A;"></i>
                                    <div>
                                        <div style="font-size: 24px; font-weight: bold;">{{ credits.filter(credit => credit.level === '良好').length }}</div>
                                        <div>信用良好</div>
                                    </div>
                                </div>
                            </el-card>
                        </el-col>
                        <el-col :span="6">
                            <el-card shadow="hover">
                                <div style="display: flex; align-items: center;">
                                    <i class="el-icon-view" style="font-size: 40px; margin-right: 15px; color: #409EFF;"></i>
                                    <div>
                                        <div style="font-size: 24px; font-weight: bold;">{{ inspections.filter(inspection => inspection.status === '待检查').length }}</div>
                                        <div>待检查项目</div>
                                    </div>
                                </div>
                            </el-card>
                        </el-col>
                    </el-row>

                    <!-- 风险预警列表 -->
                    <el-card class="filter-section">
                        <div slot="header" style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-weight: 600;">风险预警</span>
                            <el-button type="primary" size="small" @click="refreshAlerts">刷新</el-button>
                        </div>
                        <el-table :data="alerts" style="width: 100%">
                            <el-table-column prop="id" label="预警编号" width="120"></el-table-column>
                            <el-table-column prop="tenant" label="租户" width="100"></el-table-column>
                            <el-table-column prop="houseAddress" label="房源地址" width="200"></el-table-column>
                            <el-table-column prop="type" label="预警类型" width="120"></el-table-column>
                            <el-table-column prop="description" label="预警内容" min-width="200"></el-table-column>
                            <el-table-column prop="level" label="风险等级" width="100">
                                <template slot-scope="scope">
                                    <el-tag :type="getAlertLevelType(scope.row.level)">{{ scope.row.level }}</el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="createTime" label="预警时间" width="150"></el-table-column>
                            <el-table-column label="操作" width="150" fixed="right">
                                <template slot-scope="scope">
                                    <el-button size="mini" @click="viewAlert(scope.row)">详情</el-button>
                                    <el-button size="mini" type="primary" @click="handleAlert(scope.row)">处理</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-card>

                    <!-- 信用记录 -->
                    <el-card style="margin-top: 20px;">
                        <div slot="header" style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-weight: 600;">信用记录</span>
                            <el-button type="success" size="small" @click="refreshCredits">刷新</el-button>
                        </div>
                        <el-table :data="credits" style="width: 100%">
                            <el-table-column prop="tenant" label="租户" width="100"></el-table-column>
                            <el-table-column prop="houseAddress" label="房源地址" width="200"></el-table-column>
                            <el-table-column prop="score" label="信用分数" width="100"></el-table-column>
                            <el-table-column prop="level" label="信用等级" width="100">
                                <template slot-scope="scope">
                                    <el-tag :type="getCreditLevelType(scope.row.level)">{{ scope.row.level }}</el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="paymentRecord" label="缴费记录" width="120"></el-table-column>
                            <el-table-column prop="violationCount" label="违规次数" width="100"></el-table-column>
                            <el-table-column prop="lastUpdate" label="更新时间" width="150"></el-table-column>
                            <el-table-column label="操作" width="100" fixed="right">
                                <template slot-scope="scope">
                                    <el-button size="mini" @click="viewCredit(scope.row)">详情</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-card>
                </div>
            </div>
        </div>
    </div>
    <script src="assets/vue.js"></script>
    <script src="assets/element-ui.js"></script>
    <script>
        new Vue({
            el: '#app',
            data: {
                alerts: [
                    { id: 'AL001', tenant: '张三', houseAddress: '鑫悦花园公租房小区1幢1单元101', type: '逾期缴费', description: '连续3个月未缴纳租金', level: '高风险', createTime: '2023-01-15 10:30' },
                    { id: 'AL002', tenant: '李四', houseAddress: '光明新区公租房小区2幢1单元201', type: '违规使用', description: '私自改造房屋结构', level: '中风险', createTime: '2023-02-20 14:20' },
                    { id: 'AL003', tenant: '王五', houseAddress: '万科城市公寓小区3幢1单元301', type: '噪音投诉', description: '邻居投诉噪音扰民', level: '低风险', createTime: '2023-03-10 16:45' }
                ],
                credits: [
                    { tenant: '张三', houseAddress: '鑫悦花园公租房小区1幢1单元101', score: 85, level: '良好', paymentRecord: '按时缴费', violationCount: 0, lastUpdate: '2023-01-15' },
                    { tenant: '李四', houseAddress: '光明新区公租房小区2幢1单元201', score: 72, level: '一般', paymentRecord: '偶有逾期', violationCount: 1, lastUpdate: '2023-02-20' },
                    { tenant: '王五', houseAddress: '万科城市公寓小区3幢1单元301', score: 95, level: '优秀', paymentRecord: '按时缴费', violationCount: 0, lastUpdate: '2023-03-10' }
                ],
                inspections: [
                    { status: '待检查' },
                    { status: '待检查' },
                    { status: '已完成' }
                ]
            },
            methods: {
                handleTabChange(tab) { if (tab !== 'monitor') window.location.href = tab + '.html'; },
                getAlertLevelType(level) {
                    const levelMap = { '高风险': 'danger', '中风险': 'warning', '低风险': 'info' };
                    return levelMap[level] || '';
                },
                getCreditLevelType(level) {
                    const levelMap = { '优秀': 'success', '良好': 'primary', '一般': 'warning', '差': 'danger' };
                    return levelMap[level] || '';
                },
                refreshAlerts() { this.$message.success('预警数据已刷新'); },
                refreshCredits() { this.$message.success('信用数据已刷新'); },
                viewAlert(row) { this.$message.info('查看预警详情: ' + row.id); },
                handleAlert(row) { this.$message.info('处理预警: ' + row.id); },
                viewCredit(row) { this.$message.info('查看信用详情: ' + row.tenant); }
            }
        });
    </script>
</body>
</html>
