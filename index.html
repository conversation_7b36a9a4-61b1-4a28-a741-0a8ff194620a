<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公租房经租管理平台</title>
    <link rel="stylesheet" href="element-ui.css">
    <style>
        body {
            font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            overflow-x: hidden;
        }
        
        #app {
            min-height: 100vh;
            display: flex;
        }

        .header {
            background-color: #001529;
            color: white;
            padding: 12px 16px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
        }

        .logo i {
            font-size: 18px;
            margin-right: 6px;
        }

        .logo span {
            font-size: 14px;
            font-weight: bold;
            white-space: nowrap;
        }

        .sidebar {
            width: 200px !important;
            min-width: 200px !important;
            max-width: 200px !important;
            background-color: #001529;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
        }

        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: #f5f7fa;
            min-width: 0;
            overflow-x: hidden;
        }

        .top-header {
            background: white;
            padding: 16px 24px;
            border-bottom: 1px solid #e6e6e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #262626;
            margin: 0;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .content {
            flex: 1;
            padding: 24px 24px 24px 24px;
            overflow-y: auto;
            overflow-x: hidden;
            min-width: 0;
        }

        .tab-content h2 {
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
        
        .footer {
            background-color: #001529;
            color: white;
            text-align: center;
            padding: 15px;
        }
        
        .module-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }

        .module-card {
            text-align: center;
            padding: 24px 16px;
            cursor: pointer;
            transition: all 0.3s;
            border-radius: 8px;
        }

        .module-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .module-icon {
            font-size: 36px;
            margin-bottom: 8px;
            color: #409EFF;
        }

        .module-card .el-card__body {
            padding: 24px 16px;
        }

        /* 响应式设计优化 */
        @media (max-width: 1200px) {
            .sidebar {
                width: 180px;
            }

            .top-header {
                padding: 12px 20px;
            }

            .page-title {
                font-size: 18px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 60px;
            }

            .sidebar .header {
                padding: 8px;
                text-align: center;
            }

            .sidebar .logo span {
                display: none;
            }

            .sidebar-nav .el-menu-item {
                padding: 0 15px;
                text-align: center;
            }

            .top-header {
                padding: 8px 16px;
            }

            .page-title {
                font-size: 16px;
            }

            .content {
                padding: 16px;
            }

            .module-grid {
                grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
                gap: 12px;
            }
        }

        /* 房源卡片样式 */
        .house-card {
            height: 100%;
            position: relative;
            transition: all 0.3s;
        }
        
        .house-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .house-status {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1;
        }
        
        .house-image {
            height: 180px;
            overflow: hidden;
            position: relative;
        }
        
        .house-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .house-info {
            padding: 10px 15px;
        }
        
        .house-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .house-address {
            color: #606266;
            font-size: 14px;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .house-price {
            color: #f56c6c;
            font-size: 18px;
            font-weight: bold;
            margin: 5px 0;
        }
        
        .filter-section {
            margin-bottom: 20px;
        }
        
        .tab-content {
            padding-top: 0;
        }

        /* 导航栏样式 */
        .sidebar-nav {
            flex: 1;
            padding: 0;
            margin-top: 10px;
        }

        .sidebar-nav .el-menu {
            background-color: #001529;
            border: none;
        }

        .sidebar-nav .el-menu-item {
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            height: 48px;
            line-height: 48px;
            border: none;
            margin: 2px 8px;
            border-radius: 6px;
            padding: 0 16px !important;
            position: relative;
        }

        .sidebar-nav .el-menu-item i {
            margin-right: 8px;
            font-size: 16px;
            width: 16px;
            text-align: center;
        }

        .sidebar-nav .el-menu-item:hover {
            background-color: rgba(24, 144, 255, 0.1);
            color: #1890ff;
        }

        .sidebar-nav .el-menu-item.is-active {
            background-color: #1890ff;
            color: white;
            font-weight: 500;
        }

        .sidebar-nav .el-menu-item.is-active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background-color: white;
            border-radius: 0 2px 2px 0;
        }
        
        .empty-module {
            text-align: center;
            padding: 100px 0;
        }
        
        .empty-module img {
            width: 120px;
            margin-bottom: 20px;
        }

        /* 防止水平滚动条的样式 */
        .table-container {
            overflow-x: auto;
            margin: -1px;
        }

        .el-table {
            min-width: 800px;
        }

        .el-card {
            overflow: hidden;
        }

        .tab-content {
            max-width: 100%;
            overflow-x: hidden;
        }

        .el-row {
            max-width: 100%;
        }

        .el-col {
            min-width: 0;
        }

        /* 操作按钮布局优化 */
        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            max-width: 200px;
        }

        .action-buttons .el-button {
            flex: 0 0 calc(50% - 2.5px);
            margin: 0 !important;
            font-size: 12px;
            padding: 5px 8px;
            min-width: 0;
        }

        .action-buttons .el-button + .el-button {
            margin-left: 0 !important;
        }

        /* 响应式表格 */
        @media (max-width: 1200px) {
            .el-table .el-table__header-wrapper,
            .el-table .el-table__body-wrapper {
                overflow-x: auto;
            }
        }

        /* 合同管理样式 */
        .contract-section {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 4px;
        }
        
        .highlight-section {
            background-color: #fdf6ec;
            border-left: 4px solid #e6a23c;
        }
        
        .important-clause {
            color: #f56c6c;
            font-weight: bold;
        }
        
        .verification-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .verification-section {
            flex: 1;
            min-width: 300px;
        }
        
        .camera-placeholder {
            height: 200px;
            background-color: #f5f7fa;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border: 1px dashed #d9d9d9;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .signature-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .signature-pad {
            width: 100%;
            height: 200px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f7fa;
        }
        
        .completion-container {
            text-align: center;
            padding: 40px 0;
        }
        
        .completion-container h2 {
            margin: 20px 0;
            color: #67C23A;
        }
        
        .completion-actions {
            margin-top: 30px;
        }
        
        .no-contract-for-signing,
        .no-contract-selected {
            text-align: center;
            padding: 100px 0;
            color: #909399;
        }
        
        .no-contract-for-signing h3,
        .no-contract-selected h3 {
            margin: 20px 0 10px;
        }
        
        .no-qualification {
            text-align: center;
            padding: 100px 0;
            color: #909399;
        }
        
        .no-qualification h3 {
            margin: 20px 0 10px;
        }
        
        .no-qualification p {
            margin-bottom: 20px;
        }
        
        /* 租金管理样式 */
        .no-bill-selected {
            text-align: center;
            padding: 100px 0;
            color: #909399;
        }
        
        .no-bill-selected h3 {
            margin: 20px 0 10px;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <!-- 页面头部 -->
            <div class="header">
                <div class="logo">
                    <i class="el-icon-house"></i>
                    <span>公租房经租管理平台</span>
                </div>
            </div>

            <!-- 导航菜单 -->
            <div class="sidebar-nav">
                <el-menu mode="vertical" :default-active="activeTab" @select="handleTabChange" :collapse="false">
                    <el-menu-item index="home">
                        <i class="el-icon-s-home"></i>
                        <span>首页</span>
                    </el-menu-item>
                    <el-menu-item index="house">
                        <i class="el-icon-house"></i>
                        <span>房源管理</span>
                    </el-menu-item>
                    <el-menu-item index="apply">
                        <i class="el-icon-document-add"></i>
                        <span>配租申请</span>
                    </el-menu-item>
                    <el-menu-item index="contract">
                        <i class="el-icon-document"></i>
                        <span>合同管理</span>
                    </el-menu-item>
                    <el-menu-item index="payment">
                        <i class="el-icon-money"></i>
                        <span>租金管理</span>
                    </el-menu-item>
                    <el-menu-item index="repair">
                        <i class="el-icon-setting"></i>
                        <span>维修服务</span>
                    </el-menu-item>
                    <el-menu-item index="exit">
                        <i class="el-icon-switch-button"></i>
                        <span>退租管理</span>
                    </el-menu-item>
                    <el-menu-item index="monitor">
                        <i class="el-icon-view"></i>
                        <span>动态监管</span>
                    </el-menu-item>
                </el-menu>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-container">
            <!-- 顶部标题栏 -->
            <div class="top-header">
                <h1 class="page-title" v-if="activeTab === 'home'">欢迎使用公租房经租管理平台</h1>
                <h1 class="page-title" v-else-if="activeTab === 'house'">房源管理</h1>
                <h1 class="page-title" v-else-if="activeTab === 'apply'">配租申请</h1>
                <h1 class="page-title" v-else-if="activeTab === 'contract'">合同管理</h1>
                <h1 class="page-title" v-else-if="activeTab === 'payment'">租金管理</h1>
                <h1 class="page-title" v-else-if="activeTab === 'repair'">维修服务</h1>
                <h1 class="page-title" v-else-if="activeTab === 'exit'">退租管理</h1>
                <h1 class="page-title" v-else-if="activeTab === 'monitor'">动态监管</h1>

                <div class="user-info">
                    <el-dropdown>
                        <span class="el-dropdown-link" style="color: #606266; cursor: pointer;">
                            <i class="el-icon-user"></i> 张三 <i class="el-icon-arrow-down"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item>个人中心</el-dropdown-item>
                            <el-dropdown-item>退出登录</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
            </div>

            <!-- 页面内容 -->
            <div class="content">
            <!-- 首页内容 -->
            <div v-if="activeTab === 'home'" class="tab-content">
                
                <el-row :gutter="16" style="margin-bottom: 20px;">
                    <el-col :span="6" v-for="item in statistics" :key="item.title">
                        <el-card shadow="hover" style="border-radius: 8px;">
                            <div style="display: flex; align-items: center; padding: 8px 0;">
                                <i :class="item.icon" style="font-size: 36px; margin-right: 12px; color: #409EFF;"></i>
                                <div>
                                    <div style="font-size: 22px; font-weight: bold; color: #262626;">{{ item.value }}</div>
                                    <div style="color: #8c8c8c; font-size: 14px;">{{ item.title }}</div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
                
                <h3 style="margin: 20px 0 16px 0; font-size: 16px; font-weight: 600; color: #262626;">系统模块</h3>
                
                <div class="module-grid">
                    <el-card shadow="hover" class="module-card" v-for="module in modules" :key="module.name" @click.native="handleTabChange(module.tab)">
                        <i :class="module.icon" class="module-icon"></i>
                        <div>{{ module.name }}</div>
                    </el-card>
                </div>
            </div>
            
            <!-- 房源管理内容 -->
            <div v-if="activeTab === 'house'" class="tab-content">
                
                <!-- 筛选条件 -->
                <el-card class="filter-section">
                    <div slot="header">
                        <span>筛选条件</span>
                        <el-button style="float: right; padding: 3px 0" type="text" @click="resetFilter">重置</el-button>
                    </div>
                    
                    <el-form :inline="true" :model="filterForm" size="small">
                        <el-form-item label="区域">
                            <el-select v-model="filterForm.area" placeholder="选择区域">
                                <el-option label="全部区域" value=""></el-option>
                                <el-option label="滨海新区" value="滨海新区"></el-option>
                                <el-option label="武清区" value="武清区"></el-option>
                                <el-option label="南开区" value="南开区"></el-option>
                                <el-option label="河西区" value="河西区"></el-option>
                            </el-select>
                        </el-form-item>
                        
                        <el-form-item label="户型">
                            <el-select v-model="filterForm.roomType" placeholder="选择户型">
                                <el-option label="全部户型" value=""></el-option>
                                <el-option label="一室一厅" value="一室一厅"></el-option>
                                <el-option label="两室一厅" value="两室一厅"></el-option>
                                <el-option label="三室一厅" value="三室一厅"></el-option>
                            </el-select>
                        </el-form-item>
                        
                        <el-form-item label="租金">
                            <el-input v-model="filterForm.priceMin" placeholder="最低" style="width: 80px"></el-input>
                            <span> - </span>
                            <el-input v-model="filterForm.priceMax" placeholder="最高" style="width: 80px"></el-input>
                            <span> 元/月</span>
                        </el-form-item>
                        
                        <el-form-item>
                            <el-button type="primary" @click="filterHouses">筛选</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
                
                <!-- 房源列表 -->
                <el-row :gutter="20">
                    <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="house in houses" :key="house.id" style="margin-bottom: 20px">
                        <el-card class="house-card" shadow="hover">
                            <div class="house-status">
                                <el-tag :type="house.status === '可租' ? 'success' : house.status === '已租' ? 'info' : 'warning'">
                                    {{ house.status }}
                                </el-tag>
                            </div>
                            
                            <div class="house-image">
                                <div v-if="house.imageUrl" style="width: 100%; height: 150px; overflow: hidden; border-radius: 4px;">
                                    <img :src="house.imageUrl" :alt="house.title" style="width: 100%; height: 100%; object-fit: cover;">
                                </div>
                                <div v-else style="width: 100%; height: 150px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white;">
                                    <div style="text-align: center;">
                                        <i class="el-icon-house" style="font-size: 32px; margin-bottom: 8px; display: block;"></i>
                                        <span style="font-size: 12px;">{{ house.title }}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="house-info">
                                <div class="house-title">{{ house.title }}</div>
                                <div class="house-address">{{ house.address }}</div>
                                <div>{{ house.roomType }} | {{ house.area }}㎡</div>
                                <div class="house-price">¥{{ house.price }}/月</div>
                                
                                <div style="margin-top: 10px; text-align: right;">
                                    <el-button size="mini" type="primary" @click="viewDetail(house.id)">查看详情</el-button>
                                    <el-button 
                                        size="mini" 
                                        type="success" 
                                        @click="applyRent(house.id)"
                                        :disabled="house.status !== '可租'">
                                        申请配租
                                    </el-button>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
                
                <!-- 分页控件 -->
                <div style="text-align: center; margin-top: 20px;">
                    <el-pagination
                        @current-change="handleCurrentChange"
                        :current-page.sync="currentPage"
                        :page-size="pageSize"
                        layout="prev, pager, next, jumper"
                        :total="totalHouses">
                    </el-pagination>
                </div>

                <!-- 房源详情对话框 -->
                <el-dialog title="房源详情" :visible.sync="detailDialogVisible" width="70%">
                    <div v-if="currentHouse">
                        <div style="height: 300px; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center; margin-bottom: 20px; border-radius: 8px;">
                            <div style="text-align: center;">
                                <i class="el-icon-picture-outline" style="font-size: 48px; color: #909399;"></i>
                                <p style="margin-top: 10px; color: #909399;">房源图片展示区域</p>
                            </div>
                        </div>

                        <el-descriptions title="基本信息" :column="2" border>
                            <el-descriptions-item label="小区名称">{{ currentHouse.title }}</el-descriptions-item>
                            <el-descriptions-item label="地址">{{ currentHouse.address }}</el-descriptions-item>
                            <el-descriptions-item label="户型">{{ currentHouse.roomType }}</el-descriptions-item>
                            <el-descriptions-item label="面积">{{ currentHouse.area }}㎡</el-descriptions-item>
                            <el-descriptions-item label="月租金">{{ currentHouse.price }}元</el-descriptions-item>
                            <el-descriptions-item label="状态">
                                <el-tag :type="currentHouse.status === '可租' ? 'success' : currentHouse.status === '已租' ? 'info' : 'warning'">
                                    {{ currentHouse.status }}
                                </el-tag>
                            </el-descriptions-item>
                        </el-descriptions>

                        <el-divider content-position="left">房源描述</el-divider>
                        <p>{{ currentHouse.description || '该房源位置优越，交通便利，周边配套设施齐全，适合家庭居住。' }}</p>

                        <el-divider content-position="left">周边配套</el-divider>
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <h4>交通出行</h4>
                                <ul>
                                    <li>地铁站：步行5分钟</li>
                                    <li>公交站：步行2分钟</li>
                                    <li>停车位：充足</li>
                                </ul>
                            </el-col>
                            <el-col :span="8">
                                <h4>生活配套</h4>
                                <ul>
                                    <li>超市：步行3分钟</li>
                                    <li>医院：步行10分钟</li>
                                    <li>银行：步行5分钟</li>
                                </ul>
                            </el-col>
                            <el-col :span="8">
                                <h4>教育资源</h4>
                                <ul>
                                    <li>小学：步行8分钟</li>
                                    <li>中学：步行12分钟</li>
                                    <li>幼儿园：步行5分钟</li>
                                </ul>
                            </el-col>
                        </el-row>
                    </div>

                    <div slot="footer">
                        <el-button @click="detailDialogVisible = false">关闭</el-button>
                        <el-button type="primary" @click="applyRent(currentHouse.id)" v-if="currentHouse && currentHouse.status === '可租'">申请配租</el-button>
                    </div>
                </el-dialog>
            </div>

            <!-- 配租申请内容 -->
            <div v-if="activeTab === 'apply'" class="tab-content">
                <!-- 搜索和操作区域 -->
                <el-card class="filter-section">
                    <div slot="header">
                        <span>配租申请管理</span>
                        <el-button type="primary" size="small" style="float: right;" @click="showApplyDialog = true">
                            <i class="el-icon-plus"></i> 新增申请
                        </el-button>
                    </div>

                    <el-form :inline="true" size="small">
                        <el-form-item label="申请人身份证">
                            <el-input v-model="applySearch.idCard" placeholder="请输入身份证号码" clearable style="width: 200px;"></el-input>
                        </el-form-item>
                        <el-form-item label="房源地址">
                            <el-input v-model="applySearch.address" placeholder="请输入房源地址" clearable style="width: 200px;"></el-input>
                        </el-form-item>
                        <el-form-item label="申请状态">
                            <el-select v-model="applySearch.status" placeholder="请选择状态" clearable style="width: 120px;">
                                <el-option label="全部" value=""></el-option>
                                <el-option label="待审核" value="待审核"></el-option>
                                <el-option label="审核通过" value="审核通过"></el-option>
                                <el-option label="审核拒绝" value="审核拒绝"></el-option>
                                <el-option label="已配租" value="已配租"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="searchApplications">搜索</el-button>
                            <el-button @click="resetApplySearch">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>

                <!-- 申请列表 -->
                <el-card style="margin-top: 20px;">
                    <div slot="header">
                        <span>申请列表</span>
                    </div>

                    <div class="table-container">
                        <el-table :data="filteredApplications" stripe style="width: 100%">
                        <el-table-column prop="id" label="申请编号" width="120"></el-table-column>
                        <el-table-column prop="applicantName" label="申请人姓名" width="120"></el-table-column>
                        <el-table-column prop="idCard" label="身份证号" width="180">
                            <template slot-scope="scope">
                                {{ scope.row.idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2') }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="phone" label="联系电话" width="130"></el-table-column>
                        <el-table-column prop="houseAddress" label="申请房源" min-width="200"></el-table-column>
                        <el-table-column prop="roomType" label="户型" width="100"></el-table-column>
                        <el-table-column prop="applyDate" label="申请时间" width="120"></el-table-column>
                        <el-table-column prop="status" label="状态" width="100">
                            <template slot-scope="scope">
                                <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="160">
                            <template slot-scope="scope">
                                <div class="action-buttons">
                                    <el-button size="mini" @click="viewApplication(scope.row)">查看</el-button>
                                    <el-button size="mini" type="primary" @click="editApplication(scope.row)" v-if="scope.row.status === '待审核'">编辑</el-button>
                                    <el-button size="mini" type="danger" @click="deleteApplication(scope.row.id)" v-if="scope.row.status === '待审核'">删除</el-button>
                                </div>
                            </template>
                        </el-table-column>
                        </el-table>
                    </div>

                    <!-- 分页 -->
                    <div style="text-align: center; margin-top: 20px;">
                        <el-pagination
                            @current-change="handleApplyPageChange"
                            :current-page.sync="applyCurrentPage"
                            :page-size="applyPageSize"
                            layout="prev, pager, next, jumper"
                            :total="totalApplications">
                        </el-pagination>
                    </div>
                </el-card>

                <!-- 申请对话框 -->
                <el-dialog title="配租申请" :visible.sync="showApplyDialog" width="60%">
                    <el-form :model="applyForm" label-width="100px" ref="applyForm">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="申请人姓名" required>
                                    <el-input v-model="applyForm.applicantName" placeholder="请输入姓名"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="身份证号" required>
                                    <el-input v-model="applyForm.idCard" placeholder="请输入身份证号"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="联系电话" required>
                                    <el-input v-model="applyForm.phone" placeholder="请输入联系电话"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="家庭人口">
                                    <el-input-number v-model="applyForm.familySize" :min="1" :max="10"></el-input-number>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="意向区域">
                                    <el-select v-model="applyForm.preferredArea" placeholder="请选择意向区域" style="width: 100%;">
                                        <el-option label="滨海新区" value="滨海新区"></el-option>
                                        <el-option label="武清区" value="武清区"></el-option>
                                        <el-option label="南开区" value="南开区"></el-option>
                                        <el-option label="河西区" value="河西区"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="意向户型">
                                    <el-select v-model="applyForm.preferredRoomType" placeholder="请选择户型" style="width: 100%;">
                                        <el-option label="一室一厅" value="一室一厅"></el-option>
                                        <el-option label="两室一厅" value="两室一厅"></el-option>
                                        <el-option label="三室一厅" value="三室一厅"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-form-item label="家庭月收入">
                            <el-input v-model="applyForm.monthlyIncome" placeholder="请输入家庭月收入（元）">
                                <template slot="append">元</template>
                            </el-input>
                        </el-form-item>

                        <el-form-item label="申请理由">
                            <el-input type="textarea" v-model="applyForm.reason" :rows="3" placeholder="请简述申请公租房的理由"></el-input>
                        </el-form-item>

                        <el-form-item label="上传材料">
                            <el-upload
                                action="#"
                                list-type="picture-card"
                                :auto-upload="false"
                                :file-list="applyForm.fileList">
                                <i class="el-icon-plus"></i>
                            </el-upload>
                            <div style="color: #909399; font-size: 12px;">
                                请上传身份证、户口本、收入证明等相关材料
                            </div>
                        </el-form-item>

                        <el-form-item>
                            <el-checkbox v-model="applyForm.agreed">我已阅读并同意《公租房申请协议》和《数据授权采集协议》</el-checkbox>
                        </el-form-item>
                    </el-form>

                    <div slot="footer">
                        <el-button @click="showApplyDialog = false">取消</el-button>
                        <el-button type="primary" @click="submitApplication">提交申请</el-button>
                    </div>
                </el-dialog>

                <!-- 查看申请详情对话框 -->
                <el-dialog title="申请详情" :visible.sync="showViewApplicationDialog" width="60%">
                    <div v-if="currentApplication">
                        <el-descriptions title="申请人信息" :column="2" border>
                            <el-descriptions-item label="申请编号">{{ currentApplication.id }}</el-descriptions-item>
                            <el-descriptions-item label="申请人姓名">{{ currentApplication.applicantName }}</el-descriptions-item>
                            <el-descriptions-item label="身份证号">{{ currentApplication.idCard }}</el-descriptions-item>
                            <el-descriptions-item label="联系电话">{{ currentApplication.phone }}</el-descriptions-item>
                            <el-descriptions-item label="申请房源">{{ currentApplication.houseAddress }}</el-descriptions-item>
                            <el-descriptions-item label="户型">{{ currentApplication.roomType }}</el-descriptions-item>
                            <el-descriptions-item label="申请时间">{{ currentApplication.applyDate }}</el-descriptions-item>
                            <el-descriptions-item label="申请状态">
                                <el-tag :type="getStatusType(currentApplication.status)">{{ currentApplication.status }}</el-tag>
                            </el-descriptions-item>
                        </el-descriptions>

                        <el-divider content-position="left">审核进度</el-divider>
                        <el-steps :active="getApplicationStep(currentApplication.status)" finish-status="success">
                            <el-step title="提交申请" description="申请已提交"></el-step>
                            <el-step title="资格审核" description="审核申请资格"></el-step>
                            <el-step title="房源分配" description="分配合适房源"></el-step>
                            <el-step title="完成配租" description="配租完成"></el-step>
                        </el-steps>
                    </div>

                    <div slot="footer">
                        <el-button @click="showViewApplicationDialog = false">关闭</el-button>
                    </div>
                </el-dialog>

                <!-- 编辑申请对话框 -->
                <el-dialog title="编辑申请" :visible.sync="showEditApplicationDialog" width="60%">
                    <el-form :model="editApplicationForm" label-width="100px" ref="editApplicationForm" v-if="currentApplication">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="申请人姓名" required>
                                    <el-input v-model="editApplicationForm.applicantName" placeholder="请输入姓名"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="身份证号" required>
                                    <el-input v-model="editApplicationForm.idCard" placeholder="请输入身份证号"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="联系电话" required>
                                    <el-input v-model="editApplicationForm.phone" placeholder="请输入联系电话"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="户型需求">
                                    <el-select v-model="editApplicationForm.roomType" placeholder="请选择户型" style="width: 100%;">
                                        <el-option label="一室一厅" value="一室一厅"></el-option>
                                        <el-option label="两室一厅" value="两室一厅"></el-option>
                                        <el-option label="三室一厅" value="三室一厅"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-form-item label="申请房源">
                            <el-input v-model="editApplicationForm.houseAddress" placeholder="请输入申请房源地址"></el-input>
                        </el-form-item>
                    </el-form>

                    <div slot="footer">
                        <el-button @click="showEditApplicationDialog = false">取消</el-button>
                        <el-button type="primary" @click="updateApplication">保存修改</el-button>
                    </div>
                </el-dialog>
            </div>
            
            <!-- 合同管理内容 -->
            <div v-if="activeTab === 'contract'" class="tab-content">
                
                <div v-if="!showContractDetail && !showSignContract">
                        <!-- 筛选条件 -->
                        <el-card class="filter-section">
                            <div slot="header">
                                <span>筛选条件</span>
                                <el-button style="float: right; padding: 3px 0" type="text" @click="resetContractFilter">重置</el-button>
                            </div>
                            
                            <el-form :inline="true" :model="contractFilterForm" size="small">
                                <el-form-item label="合同状态">
                                    <el-select v-model="contractFilterForm.status" placeholder="选择状态">
                                        <el-option label="全部状态" value=""></el-option>
                                        <el-option label="待签约" value="待签约"></el-option>
                                        <el-option label="执行中" value="执行中"></el-option>
                                        <el-option label="已到期" value="已到期"></el-option>
                                        <el-option label="已终止" value="已终止"></el-option>
                                    </el-select>
                                </el-form-item>
                                
                                <el-form-item label="签约时间">
                                    <el-date-picker
                                        v-model="contractFilterForm.dateRange"
                                        type="daterange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期">
                                    </el-date-picker>
                                </el-form-item>
                                
                                <el-form-item label="承租人">
                                    <el-input v-model="contractFilterForm.tenant" placeholder="承租人姓名"></el-input>
                                </el-form-item>
                                
                                <el-form-item>
                                    <el-button type="primary" @click="filterContracts">筛选</el-button>
                                </el-form-item>
                            </el-form>
                        </el-card>
                        
                        <!-- 合同列表表格 -->
                        <div class="table-container">
                            <el-table
                                :data="contracts"
                                style="width: 100%"
                                border>
                            <el-table-column prop="id" label="合同编号" width="120"></el-table-column>
                            <el-table-column prop="houseTitle" label="房源名称" min-width="200"></el-table-column>
                            <el-table-column prop="tenant" label="承租人" width="100"></el-table-column>
                            <el-table-column prop="startDate" label="开始日期" width="120"></el-table-column>
                            <el-table-column prop="endDate" label="结束日期" width="120"></el-table-column>
                            <el-table-column prop="rent" label="月租金(元)" width="120"></el-table-column>
                            <el-table-column prop="status" label="状态" width="100">
                                <template slot-scope="scope">
                                    <el-tag :type="getContractStatusType(scope.row.status)">
                                        {{ scope.row.status }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="160">
                                <template slot-scope="scope">
                                    <div class="action-buttons">
                                        <el-button size="mini" @click="viewContract(scope.row)">查看</el-button>
                                        <el-button
                                            size="mini"
                                            type="primary"
                                            @click="signContract(scope.row)"
                                            v-if="scope.row.status === '待签约'">
                                            签约
                                        </el-button>
                                        <el-button
                                            size="mini"
                                            type="danger"
                                            @click="terminateContract(scope.row)"
                                            v-if="scope.row.status === '执行中'">
                                            终止
                                        </el-button>
                                    </div>
                                </template>
                            </el-table-column>
                            </el-table>
                        </div>

                        <!-- 分页控件 -->
                        <div style="text-align: center; margin-top: 20px;">
                            <el-pagination
                                @current-change="handleContractCurrentChange"
                                :current-page.sync="contractCurrentPage"
                                :page-size="contractPageSize"
                                layout="prev, pager, next, jumper"
                                :total="totalContracts">
                            </el-pagination>
                        </div>
                </div>

                <!-- 合同详情页面 -->
                <div v-if="showContractDetail && !showSignContract">
                    <el-card>
                        <div slot="header" style="display: flex; justify-content: space-between; align-items: center;">
                            <span>合同详情</span>
                            <el-button size="small" @click="backToContractList">
                                <i class="el-icon-back"></i> 返回列表
                            </el-button>
                        </div>

                        <div v-if="currentContract">
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-descriptions title="基本信息" :column="1" border>
                                        <el-descriptions-item label="合同编号">{{ currentContract.id }}</el-descriptions-item>
                                        <el-descriptions-item label="房源名称">{{ currentContract.houseTitle }}</el-descriptions-item>
                                        <el-descriptions-item label="承租人">{{ currentContract.tenant }}</el-descriptions-item>
                                        <el-descriptions-item label="联系电话">{{ currentContract.phone || '138****5678' }}</el-descriptions-item>
                                        <el-descriptions-item label="身份证号">{{ currentContract.idCard || '320***********1234' }}</el-descriptions-item>
                                    </el-descriptions>
                                </el-col>
                                <el-col :span="12">
                                    <el-descriptions title="合同条款" :column="1" border>
                                        <el-descriptions-item label="租赁期限">{{ currentContract.startDate }} 至 {{ currentContract.endDate }}</el-descriptions-item>
                                        <el-descriptions-item label="月租金">¥{{ currentContract.rent }}</el-descriptions-item>
                                        <el-descriptions-item label="押金">¥{{ currentContract.deposit || (currentContract.rent * 2) }}</el-descriptions-item>
                                        <el-descriptions-item label="合同状态">
                                            <el-tag :type="getContractStatusType(currentContract.status)">{{ currentContract.status }}</el-tag>
                                        </el-descriptions-item>
                                        <el-descriptions-item label="签约时间">{{ currentContract.signTime || '待签约' }}</el-descriptions-item>
                                    </el-descriptions>
                                </el-col>
                            </el-row>

                            <el-divider>合同条款详情</el-divider>
                            <div style="background: #f5f7fa; padding: 20px; border-radius: 4px;">
                                <h4>租赁合同主要条款：</h4>
                                <p>1. 租赁物业：{{ currentContract.houseTitle }}</p>
                                <p>2. 租赁期限：自{{ currentContract.startDate }}起至{{ currentContract.endDate }}止</p>
                                <p>3. 租金标准：每月人民币{{ currentContract.rent }}元整</p>
                                <p>4. 押金：人民币{{ currentContract.deposit || (currentContract.rent * 2) }}元整</p>
                                <p>5. 付款方式：按月支付，每月{{ currentContract.paymentDay || '1' }}日前支付当月租金</p>
                                <p>6. 其他约定：遵守相关法律法规，爱护房屋设施，按时缴纳租金</p>
                            </div>

                            <div style="text-align: center; margin-top: 30px;" v-if="currentContract.status === '待签约'">
                                <el-button type="primary" size="large" @click="signContract(currentContract)">
                                    <i class="el-icon-edit"></i> 立即签约
                                </el-button>
                            </div>
                        </div>
                    </el-card>
                </div>

                <!-- 电子签约页面 -->
                <div v-if="showSignContract">
                    <el-card>
                        <div slot="header" style="display: flex; justify-content: space-between; align-items: center;">
                            <span>电子签约</span>
                            <el-button size="small" @click="backToContractList">
                                <i class="el-icon-back"></i> 返回列表
                            </el-button>
                        </div>

                        <div v-if="currentContract">
                            <el-steps :active="signStep" finish-status="success" style="margin-bottom: 30px;">
                                <el-step title="确认合同信息"></el-step>
                                <el-step title="电子签名"></el-step>
                                <el-step title="签约完成"></el-step>
                            </el-steps>

                            <!-- 步骤1：确认合同信息 -->
                            <div v-if="signStep === 0">
                                <h3>请确认以下合同信息：</h3>
                                <el-table :data="[currentContract]" border style="margin: 20px 0;">
                                    <el-table-column prop="id" label="合同编号" width="120"></el-table-column>
                                    <el-table-column prop="houseTitle" label="房源名称" min-width="200"></el-table-column>
                                    <el-table-column prop="tenant" label="承租人" width="100"></el-table-column>
                                    <el-table-column prop="startDate" label="开始日期" width="120"></el-table-column>
                                    <el-table-column prop="endDate" label="结束日期" width="120"></el-table-column>
                                    <el-table-column prop="rent" label="月租金(元)" width="120"></el-table-column>
                                </el-table>

                                <div style="text-align: center; margin-top: 30px;">
                                    <el-button @click="backToContractList">取消</el-button>
                                    <el-button type="primary" @click="nextSignStep">确认信息，下一步</el-button>
                                </div>
                            </div>

                            <!-- 步骤2：电子签名 -->
                            <div v-if="signStep === 1">
                                <h3>电子签名</h3>
                                <div style="border: 2px dashed #ddd; padding: 40px; text-align: center; margin: 20px 0; background: #fafafa;">
                                    <i class="el-icon-edit" style="font-size: 48px; color: #409EFF; margin-bottom: 20px;"></i>
                                    <p>请在此区域进行电子签名</p>
                                    <p style="color: #999; font-size: 14px;">点击下方按钮完成签名</p>
                                </div>

                                <div style="text-align: center; margin-top: 30px;">
                                    <el-button @click="prevSignStep">上一步</el-button>
                                    <el-button type="primary" @click="nextSignStep">完成签名</el-button>
                                </div>
                            </div>

                            <!-- 步骤3：签约完成 -->
                            <div v-if="signStep === 2">
                                <div style="text-align: center; padding: 40px;">
                                    <i class="el-icon-success" style="font-size: 64px; color: #67C23A; margin-bottom: 20px;"></i>
                                    <h2 style="color: #67C23A; margin-bottom: 20px;">签约成功！</h2>
                                    <p>合同编号：{{ currentContract.id }}</p>
                                    <p>签约时间：{{ new Date().toLocaleString() }}</p>

                                    <div style="margin-top: 30px;">
                                        <el-button type="primary" @click="downloadContract">下载合同</el-button>
                                        <el-button @click="backToContractList">返回列表</el-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-card>
                </div>

                <!-- 添加共同居住人对话框 -->
                <el-dialog title="添加共同居住人" :visible.sync="cohabitantDialogVisible" width="50%">
                    <el-form :model="cohabitantForm" :rules="cohabitantFormRules" ref="cohabitantForm" label-width="100px">
                        <el-form-item label="姓名" prop="name">
                            <el-input v-model="cohabitantForm.name" placeholder="请输入姓名"></el-input>
                        </el-form-item>
                        
                        <el-form-item label="与承租人关系" prop="relation">
                            <el-select v-model="cohabitantForm.relation" placeholder="请选择关系" style="width: 100%;">
                                <el-option label="配偶" value="配偶"></el-option>
                                <el-option label="子女" value="子女"></el-option>
                                <el-option label="父母" value="父母"></el-option>
                                <el-option label="其他" value="其他"></el-option>
                            </el-select>
                        </el-form-item>
                        
                        <el-form-item label="身份证号" prop="idCard">
                            <el-input v-model="cohabitantForm.idCard" placeholder="请输入身份证号"></el-input>
                        </el-form-item>
                        
                        <el-form-item label="联系电话" prop="phone">
                            <el-input v-model="cohabitantForm.phone" placeholder="请输入联系电话"></el-input>
                        </el-form-item>
                        
                        <el-form-item label="身份证照片">
                            <el-upload
                                action="#"
                                list-type="picture-card"
                                :auto-upload="false">
                                <i class="el-icon-plus"></i>
                            </el-upload>
                            <div style="color: #909399; font-size: 12px;">
                                请上传身份证正反面照片，用于人脸识别授权
                            </div>
                        </el-form-item>
                    </el-form>
                    
                    <div slot="footer">
                        <el-button @click="cohabitantDialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="submitCohabitant">确认</el-button>
                    </div>
                </el-dialog>
                
                <!-- 合同详情对话框 -->
                <el-dialog title="合同详情" :visible.sync="contractDetailVisible" width="70%">
                    <div v-if="currentContract">
                        <el-descriptions title="基本信息" :column="2" border>
                            <el-descriptions-item label="合同编号">{{ currentContract.id }}</el-descriptions-item>
                            <el-descriptions-item label="签约日期">{{ currentContract.signDate }}</el-descriptions-item>
                            <el-descriptions-item label="承租人">{{ currentContract.tenant }}</el-descriptions-item>
                            <el-descriptions-item label="身份证号">{{ currentContract.idCard && (currentContract.idCard.substring(0, 6) + '********' + currentContract.idCard.substring(14)) }}</el-descriptions-item>
                            <el-descriptions-item label="联系电话">{{ currentContract.phone && (currentContract.phone.substring(0, 3) + '****' + currentContract.phone.substring(7)) }}</el-descriptions-item>
                            <el-descriptions-item label="合同状态">
                                <el-tag :type="getContractStatusType(currentContract.status)">
                                    {{ currentContract.status }}
                                </el-tag>
                            </el-descriptions-item>
                        </el-descriptions>
                        
                        <el-divider content-position="left">房源信息</el-divider>
                        
                        <el-descriptions :column="2" border>
                            <el-descriptions-item label="房源名称">{{ currentContract.houseTitle }}</el-descriptions-item>
                            <el-descriptions-item label="房源地址">{{ currentContract.address }}</el-descriptions-item>
                            <el-descriptions-item label="户型">{{ currentContract.roomType }}</el-descriptions-item>
                            <el-descriptions-item label="面积">{{ currentContract.area }}㎡</el-descriptions-item>
                            <el-descriptions-item label="月租金">{{ currentContract.rent }}元</el-descriptions-item>
                            <el-descriptions-item label="租期">{{ currentContract.startDate }} 至 {{ currentContract.endDate }}</el-descriptions-item>
                        </el-descriptions>
                    </div>
                </el-dialog>
                
                <!-- 终止合同对话框 -->
                <el-dialog title="终止合同" :visible.sync="terminateDialogVisible" width="50%">
                    <el-form :model="terminateForm" :rules="terminateFormRules" ref="terminateForm" label-width="100px">
                        <el-form-item label="终止原因" prop="reason">
                            <el-select v-model="terminateForm.reason" placeholder="请选择终止原因" style="width: 100%;">
                                <el-option label="承租人申请终止" value="承租人申请终止"></el-option>
                                <el-option label="违反合同规定" value="违反合同规定"></el-option>
                                <el-option label="不再符合保障条件" value="不再符合保障条件"></el-option>
                                <el-option label="其他原因" value="其他原因"></el-option>
                            </el-select>
                        </el-form-item>
                        
                        <el-form-item label="具体说明" prop="description">
                            <el-input type="textarea" v-model="terminateForm.description" :rows="4" placeholder="请输入具体说明"></el-input>
                        </el-form-item>
                        
                        <el-form-item label="终止日期" prop="date">
                            <el-date-picker
                                v-model="terminateForm.date"
                                type="date"
                                placeholder="选择日期"
                                style="width: 100%;">
                            </el-date-picker>
                        </el-form-item>
                        
                        <el-form-item label="上传证明材料">
                            <el-upload
                                action="#"
                                list-type="picture-card"
                                :auto-upload="false">
                                <i class="el-icon-plus"></i>
                            </el-upload>
                        </el-form-item>
                    </el-form>
                    
                    <div slot="footer">
                        <el-button @click="terminateDialogVisible = false">取消</el-button>
                        <el-button type="danger" @click="confirmTerminate">确认终止</el-button>
                    </div>
                </el-dialog>
            </div>
            
            <!-- 租金管理内容 -->
            <div v-if="activeTab === 'payment'" class="tab-content">
                
                <el-tabs v-model="paymentActiveTab" v-if="!showBillPayment">
                    <el-tab-pane label="账单列表" name="list">
                        <!-- 筛选条件 -->
                        <el-card class="filter-section">
                            <div slot="header">
                                <span>筛选条件</span>
                                <el-button style="float: right; padding: 3px 0" type="text" @click="resetPaymentFilter">重置</el-button>
                            </div>
                            
                            <el-form :inline="true" :model="paymentFilterForm" size="small">
                                <el-form-item label="账单状态">
                                    <el-select v-model="paymentFilterForm.status" placeholder="选择状态">
                                        <el-option label="全部状态" value=""></el-option>
                                        <el-option label="待支付" value="待支付"></el-option>
                                        <el-option label="已支付" value="已支付"></el-option>
                                        <el-option label="已逾期" value="已逾期"></el-option>
                                    </el-select>
                                </el-form-item>
                                
                                <el-form-item label="账单月份">
                                    <el-date-picker
                                        v-model="paymentFilterForm.month"
                                        type="month"
                                        placeholder="选择月份">
                                    </el-date-picker>
                                </el-form-item>
                                
                                <el-form-item label="承租人">
                                    <el-input v-model="paymentFilterForm.tenant" placeholder="承租人姓名"></el-input>
                                </el-form-item>
                                
                                <el-form-item>
                                    <el-button type="primary" @click="filterPayments">筛选</el-button>
                                </el-form-item>
                            </el-form>
                        </el-card>
                        
                        <!-- 账单列表表格 -->
                        <div class="table-container">
                            <el-table
                                :data="bills"
                                style="width: 100%"
                                border>
                            <el-table-column prop="id" label="账单编号" width="120"></el-table-column>
                            <el-table-column prop="houseTitle" label="房源名称" min-width="200"></el-table-column>
                            <el-table-column prop="tenant" label="承租人" width="100"></el-table-column>
                            <el-table-column prop="month" label="账单月份" width="120"></el-table-column>
                            <el-table-column prop="dueDate" label="到期日" width="120"></el-table-column>
                            <el-table-column prop="totalAmount" label="总金额(元)" width="120">
                                <template slot-scope="scope">
                                    <span>{{ scope.row.totalAmount.toFixed(2) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="status" label="状态" width="100">
                                <template slot-scope="scope">
                                    <el-tag :type="getBillStatusType(scope.row.status)">
                                        {{ scope.row.status }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="160">
                                <template slot-scope="scope">
                                    <div class="action-buttons">
                                        <el-button
                                            size="mini"
                                            type="primary"
                                            @click="payBill(scope.row)"
                                            v-if="scope.row.status === '待支付' || scope.row.status === '已逾期'">
                                            支付
                                        </el-button>
                                        <el-button
                                            size="mini"
                                            @click="viewBill(scope.row)"
                                            v-if="scope.row.status === '已支付'">
                                            查看
                                        </el-button>
                                        <el-button
                                            size="mini"
                                            type="success"
                                            @click="downloadInvoice(scope.row)"
                                            v-if="scope.row.status === '已支付'">
                                            发票
                                        </el-button>
                                    </div>
                                </template>
                            </el-table-column>
                            </el-table>
                        </div>

                        <!-- 分页控件 -->
                        <div style="text-align: center; margin-top: 20px;">
                            <el-pagination
                                @current-change="handleBillCurrentChange"
                                :current-page.sync="billCurrentPage"
                                :page-size="billPageSize"
                                layout="prev, pager, next, jumper"
                                :total="totalBills">
                            </el-pagination>
                        </div>
                    </el-tab-pane>

                    <el-tab-pane label="缴费记录" name="history">
                        <el-card>
                            <div slot="header">
                                <span>缴费记录</span>
                            </div>
                            
                            <!-- 缴费记录筛选 -->
                            <el-form :inline="true" :model="paymentHistoryFilterForm" size="small">
                                <el-form-item label="时间范围">
                                    <el-date-picker
                                        v-model="paymentHistoryFilterForm.dateRange"
                                        type="daterange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期">
                                    </el-date-picker>
                                </el-form-item>
                                
                                <el-form-item label="支付方式">
                                    <el-select v-model="paymentHistoryFilterForm.method" placeholder="选择支付方式">
                                        <el-option label="全部方式" value=""></el-option>
                                        <el-option label="微信支付" value="wechat"></el-option>
                                        <el-option label="支付宝" value="alipay"></el-option>
                                        <el-option label="银行卡" value="bank"></el-option>
                                        <el-option label="线下代缴" value="offline"></el-option>
                                    </el-select>
                                </el-form-item>
                                
                                <el-form-item>
                                    <el-button type="primary" @click="filterPaymentHistory">筛选</el-button>
                                </el-form-item>
                            </el-form>
                            
                            <!-- 缴费记录表格 -->
                            <el-table
                                :data="paymentRecords"
                                style="width: 100%"
                                border>
                                <el-table-column prop="id" label="账单编号" width="120"></el-table-column>
                                <el-table-column prop="houseTitle" label="房源名称" min-width="200"></el-table-column>
                                <el-table-column prop="month" label="账单月份" width="120"></el-table-column>
                                <el-table-column prop="paymentTime" label="支付时间" width="150"></el-table-column>
                                <el-table-column prop="amount" label="金额(元)" width="120">
                                    <template slot-scope="scope">
                                        <span>{{ scope.row.amount.toFixed(2) }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="paymentMethod" label="支付方式" width="120">
                                    <template slot-scope="scope">
                                        <span>{{ getPaymentMethodName(scope.row.paymentMethod) }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="120">
                                    <template slot-scope="scope">
                                        <el-button size="mini" type="success" @click="downloadInvoice(scope.row)">发票</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                            
                            <!-- 分页控件 -->
                            <div style="text-align: center; margin-top: 20px;">
                                <el-pagination
                                    @current-change="handleHistoryCurrentChange"
                                    :current-page.sync="historyCurrentPage"
                                    :page-size="historyPageSize"
                                    layout="prev, pager, next, jumper"
                                    :total="totalHistory">
                                </el-pagination>
                            </div>
                        </el-card>
                    </el-tab-pane>

                    <el-tab-pane label="缴费统计" name="stats">
                        <el-card>
                            <div slot="header">
                                <span>缴费统计</span>
                            </div>
                            
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <div style="display: flex; align-items: center;">
                                            <i class="el-icon-money" style="font-size: 40px; margin-right: 15px; color: #409EFF;"></i>
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold;">{{ ((paymentStats.paidBills / paymentStats.totalBills) * 100).toFixed(1) }}%</div>
                                                <div>按时缴费率</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <div style="display: flex; align-items: center;">
                                            <i class="el-icon-warning" style="font-size: 40px; margin-right: 15px; color: #E6A23C;"></i>
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold;">{{ paymentStats.overdueBills }}</div>
                                                <div>逾期账单数</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <div style="display: flex; align-items: center;">
                                            <i class="el-icon-wallet" style="font-size: 40px; margin-right: 15px; color: #67C23A;"></i>
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold;">¥{{ paymentStats.paidAmount.toLocaleString() }}</div>
                                                <div>已收金额</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <div style="display: flex; align-items: center;">
                                            <i class="el-icon-chat-dot-square" style="font-size: 40px; margin-right: 15px; color: #F56C6C;"></i>
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold;">¥{{ paymentStats.unpaidAmount.toLocaleString() }}</div>
                                                <div>待收金额</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>
                            </el-row>
                            
                            <div style="margin-top: 20px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                    <h3>月度缴费情况</h3>
                                    <el-select v-model="paymentStatsYear" placeholder="选择年份" size="small" style="width: 120px;">
                                        <el-option label="2023年" value="2023"></el-option>
                                        <el-option label="2022年" value="2022"></el-option>
                                    </el-select>
                                </div>
                                
                                <div style="height: 300px; background-color: white; border: 1px solid #e6e6e6; border-radius: 4px; padding: 20px;">
                                    <div style="display: flex; align-items: end; height: 100%; justify-content: space-around;">
                                        <div v-for="(month, index) in monthlyStats" :key="index" style="display: flex; flex-direction: column; align-items: center;">
                                            <div :style="{
                                                width: '30px',
                                                height: (month.amount / 50000 * 200) + 'px',
                                                backgroundColor: month.amount > 40000 ? '#67C23A' : month.amount > 30000 ? '#409EFF' : '#E6A23C',
                                                marginBottom: '10px',
                                                borderRadius: '2px'
                                            }"></div>
                                            <div style="font-size: 12px; color: #666;">{{ month.month }}</div>
                                            <div style="font-size: 10px; color: #999;">¥{{ (month.amount / 1000).toFixed(0) }}k</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div style="margin-top: 30px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                    <h3>支付方式占比</h3>
                                </div>
                                
                                <div style="height: 300px; background-color: white; border: 1px solid #e6e6e6; border-radius: 4px; padding: 20px;">
                                    <div style="display: flex; align-items: center; height: 100%;">
                                        <div style="flex: 1; display: flex; justify-content: center;">
                                            <div style="width: 200px; height: 200px; border-radius: 50%; background: conic-gradient(#409EFF 0deg 144deg, #67C23A 144deg 216deg, #E6A23C 216deg 288deg, #F56C6C 288deg 360deg); position: relative;">
                                                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 100px; height: 100px; background: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold;">
                                                    支付方式
                                                </div>
                                            </div>
                                        </div>
                                        <div style="flex: 1; padding-left: 20px;">
                                            <div style="margin-bottom: 15px;">
                                                <div style="display: flex; align-items: center; margin-bottom: 5px;">
                                                    <div style="width: 12px; height: 12px; background: #409EFF; margin-right: 8px;"></div>
                                                    <span>微信支付 (40%)</span>
                                                </div>
                                                <div style="display: flex; align-items: center; margin-bottom: 5px;">
                                                    <div style="width: 12px; height: 12px; background: #67C23A; margin-right: 8px;"></div>
                                                    <span>支付宝 (20%)</span>
                                                </div>
                                                <div style="display: flex; align-items: center; margin-bottom: 5px;">
                                                    <div style="width: 12px; height: 12px; background: #E6A23C; margin-right: 8px;"></div>
                                                    <span>银行转账 (20%)</span>
                                                </div>
                                                <div style="display: flex; align-items: center;">
                                                    <div style="width: 12px; height: 12px; background: #F56C6C; margin-right: 8px;"></div>
                                                    <span>线下代缴 (20%)</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-card>
                    </el-tab-pane>
                </el-tabs>

                <!-- 支付账单页面 -->
                <div v-if="showBillPayment">
                    <el-card>
                        <div slot="header" style="display: flex; justify-content: space-between; align-items: center;">
                            <span>{{ currentBill && currentBill.status === '已支付' ? '账单详情' : '支付账单' }}</span>
                            <el-button size="small" @click="backToBillList">
                                <i class="el-icon-back"></i> 返回列表
                            </el-button>
                        </div>

                        <div v-if="currentBill">
                            <!-- 已支付账单详情 -->
                            <div v-if="currentBill.status === '已支付'">
                                <el-descriptions title="账单详情" :column="2" border style="margin: 20px 0;">
                                    <el-descriptions-item label="账单编号">{{ currentBill.id }}</el-descriptions-item>
                                    <el-descriptions-item label="账单状态">
                                        <el-tag type="success">{{ currentBill.status }}</el-tag>
                                    </el-descriptions-item>
                                    <el-descriptions-item label="房源名称">{{ currentBill.houseTitle }}</el-descriptions-item>
                                    <el-descriptions-item label="房源地址">{{ currentBill.address }}</el-descriptions-item>
                                    <el-descriptions-item label="承租人">{{ currentBill.tenant }}</el-descriptions-item>
                                    <el-descriptions-item label="联系电话">{{ currentBill.phone }}</el-descriptions-item>
                                    <el-descriptions-item label="账单月份">{{ currentBill.month }}</el-descriptions-item>
                                    <el-descriptions-item label="到期日期">{{ currentBill.dueDate }}</el-descriptions-item>
                                </el-descriptions>

                                <div style="margin-top: 20px;">
                                    <h3>费用明细</h3>
                                    <el-table
                                        :data="currentBill.items"
                                        style="width: 100%"
                                        border>
                                        <el-table-column prop="name" label="费用名称" width="180"></el-table-column>
                                        <el-table-column prop="amount" label="金额(元)" width="100">
                                            <template slot-scope="scope">
                                                <span>{{ scope.row.amount.toFixed(2) }}</span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="description" label="说明"></el-table-column>
                                    </el-table>

                                    <div style="margin-top: 20px; text-align: right;">
                                        <div style="font-size: 18px; font-weight: bold;">
                                            总计：¥{{ currentBill.totalAmount.toFixed(2) }}
                                        </div>
                                    </div>
                                </div>

                                <div style="margin-top: 30px;">
                                    <h3>支付信息</h3>
                                    <el-descriptions :column="2" border>
                                        <el-descriptions-item label="支付时间">{{ currentBill.paymentTime }}</el-descriptions-item>
                                        <el-descriptions-item label="支付方式">{{ getPaymentMethodName(currentBill.paymentMethod) }}</el-descriptions-item>
                                        <el-descriptions-item label="交易流水号">{{ currentBill.transactionId }}</el-descriptions-item>
                                    </el-descriptions>

                                    <div style="margin-top: 20px; text-align: center;">
                                        <el-button type="success" @click="downloadInvoice(currentBill)">下载电子发票</el-button>
                                    </div>
                                </div>
                            </div>

                            <!-- 待支付账单的支付流程 -->
                            <div v-else>
                                <el-steps :active="paymentStep" finish-status="success" style="margin-bottom: 30px;">
                                    <el-step title="确认账单信息"></el-step>
                                    <el-step title="选择支付方式"></el-step>
                                    <el-step title="支付完成"></el-step>
                                </el-steps>

                                <!-- 步骤1：确认账单信息 -->
                                <div v-if="paymentStep === 0">
                                <h3>请确认以下账单信息：</h3>
                                <el-descriptions title="账单详情" :column="2" border style="margin: 20px 0;">
                                    <el-descriptions-item label="账单编号">{{ currentBill.id }}</el-descriptions-item>
                                    <el-descriptions-item label="房源名称">{{ currentBill.houseTitle }}</el-descriptions-item>
                                    <el-descriptions-item label="承租人">{{ currentBill.tenant }}</el-descriptions-item>
                                    <el-descriptions-item label="账单月份">{{ currentBill.month }}</el-descriptions-item>
                                    <el-descriptions-item label="应付金额">¥{{ currentBill.amount }}</el-descriptions-item>
                                    <el-descriptions-item label="到期日期">{{ currentBill.dueDate }}</el-descriptions-item>
                                </el-descriptions>

                                <div style="background: #f5f7fa; padding: 20px; border-radius: 4px; margin: 20px 0;">
                                    <h4>费用明细：</h4>
                                    <p>租金：¥{{ currentBill.rent || currentBill.amount }}</p>
                                    <p>物业费：¥0</p>
                                    <p>水电费：¥0</p>
                                    <p style="font-weight: bold; color: #E6A23C;">总计：¥{{ currentBill.amount }}</p>
                                </div>

                                <div style="text-align: center; margin-top: 30px;">
                                    <el-button @click="backToBillList">取消</el-button>
                                    <el-button type="primary" @click="nextPaymentStep">确认信息，下一步</el-button>
                                </div>
                            </div>

                            <!-- 步骤2：选择支付方式 -->
                            <div v-if="paymentStep === 1">
                                <h3>选择支付方式</h3>
                                <div style="margin: 20px 0;">
                                    <el-radio-group v-model="paymentMethod" size="large">
                                        <el-radio-button label="wechat">
                                            <i class="el-icon-chat-dot-round" style="color: #07C160;"></i> 微信支付
                                        </el-radio-button>
                                        <el-radio-button label="alipay">
                                            <i class="el-icon-wallet" style="color: #1677FF;"></i> 支付宝
                                        </el-radio-button>
                                        <el-radio-button label="bank">
                                            <i class="el-icon-bank-card" style="color: #F56C6C;"></i> 银行卡
                                        </el-radio-button>
                                    </el-radio-group>
                                </div>

                                <div style="border: 1px solid #ddd; padding: 30px; text-align: center; margin: 20px 0; background: #fafafa;">
                                    <div v-if="paymentMethod === 'wechat'">
                                        <i class="el-icon-qrcode" style="font-size: 64px; color: #07C160; margin-bottom: 20px;"></i>
                                        <p>请使用微信扫描二维码支付</p>
                                        <p style="color: #999;">支付金额：¥{{ currentBill.amount }}</p>
                                    </div>
                                    <div v-if="paymentMethod === 'alipay'">
                                        <i class="el-icon-qrcode" style="font-size: 64px; color: #1677FF; margin-bottom: 20px;"></i>
                                        <p>请使用支付宝扫描二维码支付</p>
                                        <p style="color: #999;">支付金额：¥{{ currentBill.amount }}</p>
                                    </div>
                                    <div v-if="paymentMethod === 'bank'">
                                        <i class="el-icon-bank-card" style="font-size: 64px; color: #F56C6C; margin-bottom: 20px;"></i>
                                        <p>银行卡支付</p>
                                        <p style="color: #999;">支付金额：¥{{ currentBill.amount }}</p>
                                    </div>
                                </div>

                                <div style="text-align: center; margin-top: 30px;">
                                    <el-button @click="prevPaymentStep">上一步</el-button>
                                    <el-button type="primary" @click="nextPaymentStep">确认支付</el-button>
                                </div>
                            </div>

                            <!-- 步骤3：支付完成 -->
                            <div v-if="paymentStep === 2">
                                <div style="text-align: center; padding: 40px;">
                                    <i class="el-icon-success" style="font-size: 64px; color: #67C23A; margin-bottom: 20px;"></i>
                                    <h2 style="color: #67C23A; margin-bottom: 20px;">支付成功！</h2>
                                    <p>账单编号：{{ currentBill.id }}</p>
                                    <p>支付金额：¥{{ currentBill.amount }}</p>
                                    <p>支付时间：{{ new Date().toLocaleString() }}</p>

                                    <div style="margin-top: 30px;">
                                        <el-button type="primary" @click="downloadReceipt">下载收据</el-button>
                                        <el-button @click="backToBillList">返回列表</el-button>
                                    </div>
                                </div>
                            </div>
                            </div>
                        </div>
                    </el-card>
                </div>

                <!-- 账单详情对话框 -->
                <el-dialog title="账单详情" :visible.sync="billDetailVisible" width="70%">
                    <div v-if="currentBill">
                        <el-descriptions title="基本信息" :column="2" border>
                            <el-descriptions-item label="账单编号">{{ currentBill.id }}</el-descriptions-item>
                            <el-descriptions-item label="账单状态">
                                <el-tag :type="getBillStatusType(currentBill.status)">
                                    {{ currentBill.status }}
                                </el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label="房源名称">{{ currentBill.houseTitle }}</el-descriptions-item>
                            <el-descriptions-item label="账单月份">{{ currentBill.month }}</el-descriptions-item>
                            <el-descriptions-item label="承租人">{{ currentBill.tenant }}</el-descriptions-item>
                            <el-descriptions-item label="到期日">{{ currentBill.dueDate }}</el-descriptions-item>
                        </el-descriptions>
                        
                        <el-divider content-position="left">费用明细</el-divider>
                        
                        <el-table
                            :data="currentBill.items"
                            style="width: 100%"
                            border>
                            <el-table-column prop="name" label="费用名称" width="180"></el-table-column>
                            <el-table-column prop="amount" label="金额(元)" width="100">
                                <template slot-scope="scope">
                                    <span>{{ scope.row.amount.toFixed(2) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="description" label="说明"></el-table-column>
                        </el-table>
                        
                        <div style="margin-top: 20px; text-align: right;">
                            <div v-if="currentBill.status === '已逾期'" style="color: #f56c6c; margin-bottom: 10px;">
                                <i class="el-icon-warning"></i> 
                                已逾期{{ currentBill.overdueDays }}天，产生滞纳金{{ currentBill.lateFee.toFixed(2) }}元
                            </div>
                            <div style="font-size: 18px; font-weight: bold;">
                                总计：¥{{ (currentBill.totalAmount + (currentBill.lateFee || 0)).toFixed(2) }}
                            </div>
                        </div>
                        
                        <div v-if="currentBill.status === '已支付'" style="margin-top: 20px;">
                            <el-divider content-position="left">支付信息</el-divider>
                            
                            <el-descriptions :column="2" border>
                                <el-descriptions-item label="支付时间">{{ currentBill.paymentTime }}</el-descriptions-item>
                                <el-descriptions-item label="支付方式">{{ getPaymentMethodName(currentBill.paymentMethod) }}</el-descriptions-item>
                                <el-descriptions-item label="交易流水号">{{ currentBill.transactionId }}</el-descriptions-item>
                            </el-descriptions>
                        </div>
                    </div>
                </el-dialog>
            </div>
            
            <!-- 维修服务内容 -->
            <div v-if="activeTab === 'repair'" class="tab-content">
                <el-tabs v-model="repairActiveTab">
                    <el-tab-pane label="报修管理" name="list">
                        <!-- 筛选条件 -->
                        <el-card class="filter-section">
                            <div slot="header">
                                <span>报修工单管理</span>
                                <el-button type="primary" size="small" style="float: right;" @click="showRepairDialog = true">
                                    <i class="el-icon-plus"></i> 新增报修
                                </el-button>
                            </div>

                            <el-form :inline="true" size="small">
                                <el-form-item label="工单状态">
                                    <el-select v-model="repairSearch.status" placeholder="请选择状态" clearable style="width: 120px;">
                                        <el-option label="全部" value=""></el-option>
                                        <el-option label="待处理" value="待处理"></el-option>
                                        <el-option label="处理中" value="处理中"></el-option>
                                        <el-option label="已完成" value="已完成"></el-option>
                                        <el-option label="已取消" value="已取消"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="故障类型">
                                    <el-select v-model="repairSearch.type" placeholder="请选择类型" clearable style="width: 120px;">
                                        <el-option label="全部" value=""></el-option>
                                        <el-option label="水电" value="水电"></el-option>
                                        <el-option label="家具" value="家具"></el-option>
                                        <el-option label="结构" value="结构"></el-option>
                                        <el-option label="其他" value="其他"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="紧急程度">
                                    <el-select v-model="repairSearch.urgency" placeholder="请选择程度" clearable style="width: 120px;">
                                        <el-option label="全部" value=""></el-option>
                                        <el-option label="紧急" value="紧急"></el-option>
                                        <el-option label="一般" value="一般"></el-option>
                                        <el-option label="不急" value="不急"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="searchRepairs">搜索</el-button>
                                    <el-button @click="resetRepairSearch">重置</el-button>
                                </el-form-item>
                            </el-form>
                        </el-card>

                        <!-- 报修列表 -->
                        <el-card style="margin-top: 20px;">
                            <div slot="header">
                                <span>报修工单列表</span>
                            </div>

                            <div class="table-container">
                                <el-table :data="filteredRepairs" stripe style="width: 100%">
                                <el-table-column prop="id" label="工单编号" width="120"></el-table-column>
                                <el-table-column prop="houseAddress" label="房源地址" min-width="200"></el-table-column>
                                <el-table-column prop="tenant" label="报修人" width="100"></el-table-column>
                                <el-table-column prop="type" label="故障类型" width="100"></el-table-column>
                                <el-table-column prop="description" label="故障描述" min-width="150"></el-table-column>
                                <el-table-column prop="urgency" label="紧急程度" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getUrgencyType(scope.row.urgency)">{{ scope.row.urgency }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="createTime" label="报修时间" width="120"></el-table-column>
                                <el-table-column prop="status" label="状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getRepairStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="160">
                                    <template slot-scope="scope">
                                        <div class="action-buttons">
                                            <el-button size="mini" @click="viewRepair(scope.row)">查看</el-button>
                                            <el-button size="mini" type="primary" @click="assignRepair(scope.row)" v-if="scope.row.status === '待处理'">派单</el-button>
                                            <el-button size="mini" type="success" @click="completeRepair(scope.row)" v-if="scope.row.status === '处理中'">完成</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                                </el-table>
                            </div>

                            <!-- 分页 -->
                            <div style="text-align: center; margin-top: 20px;">
                                <el-pagination
                                    @current-change="handleRepairPageChange"
                                    :current-page.sync="repairCurrentPage"
                                    :page-size="repairPageSize"
                                    layout="prev, pager, next, jumper"
                                    :total="totalRepairs">
                                </el-pagination>
                            </div>
                        </el-card>
                    </el-tab-pane>

                    <el-tab-pane label="维修员管理" name="worker">
                        <el-card>
                            <div slot="header">
                                <span>维修员信息</span>
                                <el-button type="primary" size="small" style="float: right;">
                                    <i class="el-icon-plus"></i> 添加维修员
                                </el-button>
                            </div>

                            <div class="table-container">
                                <el-table :data="repairWorkers" stripe style="width: 100%">
                                <el-table-column prop="id" label="员工编号" width="120"></el-table-column>
                                <el-table-column prop="name" label="姓名" width="100"></el-table-column>
                                <el-table-column prop="phone" label="联系电话" width="130"></el-table-column>
                                <el-table-column prop="specialty" label="专业领域" min-width="150"></el-table-column>
                                <el-table-column prop="workload" label="当前工单" width="100"></el-table-column>
                                <el-table-column prop="rating" label="评分" width="120">
                                    <template slot-scope="scope">
                                        <el-rate v-model="scope.row.rating" disabled show-score></el-rate>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="status" label="状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="scope.row.status === '在线' ? 'success' : 'info'">{{ scope.row.status }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="120">
                                    <template slot-scope="scope">
                                        <div class="action-buttons">
                                            <el-button size="mini" @click="viewWorker(scope.row)">查看</el-button>
                                            <el-button size="mini" type="primary" @click="editWorker(scope.row)">编辑</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                                </el-table>
                            </div>
                        </el-card>
                    </el-tab-pane>
                </el-tabs>

                <!-- 报修对话框 -->
                <el-dialog title="新增报修" :visible.sync="showRepairDialog" width="60%">
                    <el-form :model="repairForm" label-width="100px" ref="repairForm">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="房源地址" required>
                                    <el-input v-model="repairForm.houseAddress" placeholder="请输入房源地址"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="报修人" required>
                                    <el-input v-model="repairForm.tenant" placeholder="请输入报修人姓名"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="故障类型" required>
                                    <el-select v-model="repairForm.type" placeholder="请选择故障类型" style="width: 100%;">
                                        <el-option label="水电" value="水电"></el-option>
                                        <el-option label="家具" value="家具"></el-option>
                                        <el-option label="结构" value="结构"></el-option>
                                        <el-option label="其他" value="其他"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="紧急程度" required>
                                    <el-select v-model="repairForm.urgency" placeholder="请选择紧急程度" style="width: 100%;">
                                        <el-option label="紧急" value="紧急"></el-option>
                                        <el-option label="一般" value="一般"></el-option>
                                        <el-option label="不急" value="不急"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-form-item label="故障描述" required>
                            <el-input type="textarea" v-model="repairForm.description" :rows="3" placeholder="请详细描述故障情况"></el-input>
                        </el-form-item>

                        <el-form-item label="联系电话">
                            <el-input v-model="repairForm.phone" placeholder="请输入联系电话"></el-input>
                        </el-form-item>

                        <el-form-item label="故障图片">
                            <el-upload
                                action="#"
                                list-type="picture-card"
                                :auto-upload="false"
                                :file-list="repairForm.fileList">
                                <i class="el-icon-plus"></i>
                            </el-upload>
                            <div style="color: #909399; font-size: 12px;">
                                请上传故障现场照片，便于维修员了解情况
                            </div>
                        </el-form-item>
                    </el-form>

                    <div slot="footer">
                        <el-button @click="showRepairDialog = false">取消</el-button>
                        <el-button type="primary" @click="submitRepair">提交报修</el-button>
                    </div>
                </el-dialog>

                <!-- 查看维修详情对话框 -->
                <el-dialog title="维修工单详情" :visible.sync="showRepairDetailDialog" width="60%">
                    <div v-if="currentRepair">
                        <el-descriptions title="工单信息" :column="2" border>
                            <el-descriptions-item label="工单编号">{{ currentRepair.id }}</el-descriptions-item>
                            <el-descriptions-item label="工单状态">
                                <el-tag :type="getRepairStatusType(currentRepair.status)">{{ currentRepair.status }}</el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label="房源地址">{{ currentRepair.houseAddress }}</el-descriptions-item>
                            <el-descriptions-item label="报修人">{{ currentRepair.tenant }}</el-descriptions-item>
                            <el-descriptions-item label="故障类型">{{ currentRepair.type }}</el-descriptions-item>
                            <el-descriptions-item label="紧急程度">
                                <el-tag :type="getUrgencyType(currentRepair.urgency)">{{ currentRepair.urgency }}</el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label="报修时间">{{ currentRepair.createTime }}</el-descriptions-item>
                            <el-descriptions-item label="联系电话">{{ currentRepair.phone || '未提供' }}</el-descriptions-item>
                        </el-descriptions>

                        <el-divider content-position="left">故障描述</el-divider>
                        <p>{{ currentRepair.description }}</p>

                        <el-divider content-position="left">处理进度</el-divider>
                        <el-steps :active="getRepairStep(currentRepair.status)" finish-status="success">
                            <el-step title="提交报修" description="报修已提交"></el-step>
                            <el-step title="派单处理" description="分配维修员"></el-step>
                            <el-step title="维修中" description="维修员处理中"></el-step>
                            <el-step title="完成验收" description="维修完成"></el-step>
                        </el-steps>

                        <div v-if="currentRepair.worker" style="margin-top: 20px;">
                            <el-divider content-position="left">维修员信息</el-divider>
                            <el-descriptions :column="2" border>
                                <el-descriptions-item label="维修员">{{ currentRepair.worker.name }}</el-descriptions-item>
                                <el-descriptions-item label="联系电话">{{ currentRepair.worker.phone }}</el-descriptions-item>
                                <el-descriptions-item label="专业领域">{{ currentRepair.worker.specialty }}</el-descriptions-item>
                                <el-descriptions-item label="评分">
                                    <el-rate v-model="currentRepair.worker.rating" disabled show-score></el-rate>
                                </el-descriptions-item>
                            </el-descriptions>
                        </div>
                    </div>

                    <div slot="footer">
                        <el-button @click="showRepairDetailDialog = false">关闭</el-button>
                    </div>
                </el-dialog>

                <!-- 添加维修员对话框 -->
                <el-dialog title="添加维修员" :visible.sync="showAddWorkerDialog" width="50%">
                    <el-form :model="workerForm" label-width="100px" ref="workerForm">
                        <el-form-item label="姓名" required>
                            <el-input v-model="workerForm.name" placeholder="请输入维修员姓名"></el-input>
                        </el-form-item>
                        <el-form-item label="联系电话" required>
                            <el-input v-model="workerForm.phone" placeholder="请输入联系电话"></el-input>
                        </el-form-item>
                        <el-form-item label="专业领域" required>
                            <el-select v-model="workerForm.specialty" placeholder="请选择专业领域" style="width: 100%;">
                                <el-option label="水电维修" value="水电维修"></el-option>
                                <el-option label="家具维修" value="家具维修"></el-option>
                                <el-option label="结构维修" value="结构维修"></el-option>
                                <el-option label="综合维修" value="综合维修"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="工作经验">
                            <el-input-number v-model="workerForm.experience" :min="0" :max="30" placeholder="年"></el-input-number>
                            <span style="margin-left: 10px;">年</span>
                        </el-form-item>
                    </el-form>

                    <div slot="footer">
                        <el-button @click="showAddWorkerDialog = false">取消</el-button>
                        <el-button type="primary" @click="addWorker">添加</el-button>
                    </div>
                </el-dialog>

                <!-- 查看维修员详情对话框 -->
                <el-dialog title="维修员详情" :visible.sync="showWorkerDetailDialog" width="50%">
                    <div v-if="currentWorker">
                        <el-descriptions title="基本信息" :column="2" border>
                            <el-descriptions-item label="员工编号">{{ currentWorker.id }}</el-descriptions-item>
                            <el-descriptions-item label="姓名">{{ currentWorker.name }}</el-descriptions-item>
                            <el-descriptions-item label="联系电话">{{ currentWorker.phone }}</el-descriptions-item>
                            <el-descriptions-item label="专业领域">{{ currentWorker.specialty }}</el-descriptions-item>
                            <el-descriptions-item label="当前工单">{{ currentWorker.workload }}个</el-descriptions-item>
                            <el-descriptions-item label="评分">
                                <el-rate v-model="currentWorker.rating" disabled show-score></el-rate>
                            </el-descriptions-item>
                            <el-descriptions-item label="状态">
                                <el-tag :type="currentWorker.status === '在线' ? 'success' : 'info'">{{ currentWorker.status }}</el-tag>
                            </el-descriptions-item>
                        </el-descriptions>
                    </div>

                    <div slot="footer">
                        <el-button @click="showWorkerDetailDialog = false">关闭</el-button>
                    </div>
                </el-dialog>

                <!-- 编辑维修员对话框 -->
                <el-dialog title="编辑维修员" :visible.sync="showEditWorkerDialog" width="50%">
                    <el-form :model="editWorkerForm" label-width="100px" ref="editWorkerForm" v-if="currentWorker">
                        <el-form-item label="姓名" required>
                            <el-input v-model="editWorkerForm.name" placeholder="请输入维修员姓名"></el-input>
                        </el-form-item>
                        <el-form-item label="联系电话" required>
                            <el-input v-model="editWorkerForm.phone" placeholder="请输入联系电话"></el-input>
                        </el-form-item>
                        <el-form-item label="专业领域" required>
                            <el-select v-model="editWorkerForm.specialty" placeholder="请选择专业领域" style="width: 100%;">
                                <el-option label="水电维修" value="水电维修"></el-option>
                                <el-option label="家具维修" value="家具维修"></el-option>
                                <el-option label="结构维修" value="结构维修"></el-option>
                                <el-option label="综合维修" value="综合维修"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="状态">
                            <el-select v-model="editWorkerForm.status" placeholder="请选择状态" style="width: 100%;">
                                <el-option label="在线" value="在线"></el-option>
                                <el-option label="忙碌" value="忙碌"></el-option>
                                <el-option label="离线" value="离线"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>

                    <div slot="footer">
                        <el-button @click="showEditWorkerDialog = false">取消</el-button>
                        <el-button type="primary" @click="updateWorker">保存修改</el-button>
                    </div>
                </el-dialog>
            </div>
            
            <!-- 退租管理内容 -->
            <div v-if="activeTab === 'exit'" class="tab-content">
                <el-tabs v-model="exitActiveTab">
                    <el-tab-pane label="退租申请" name="list">
                        <!-- 筛选条件 -->
                        <el-card class="filter-section">
                            <div slot="header">
                                <span>退租申请管理</span>
                                <el-button type="primary" size="small" style="float: right;" @click="showExitDialog = true">
                                    <i class="el-icon-plus"></i> 新增退租申请
                                </el-button>
                            </div>

                            <el-form :inline="true" size="small">
                                <el-form-item label="申请状态">
                                    <el-select v-model="exitSearch.status" placeholder="请选择状态" clearable style="width: 120px;">
                                        <el-option label="全部" value=""></el-option>
                                        <el-option label="待审核" value="待审核"></el-option>
                                        <el-option label="审核通过" value="审核通过"></el-option>
                                        <el-option label="待验房" value="待验房"></el-option>
                                        <el-option label="已完成" value="已完成"></el-option>
                                        <el-option label="已拒绝" value="已拒绝"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="退租原因">
                                    <el-select v-model="exitSearch.reason" placeholder="请选择原因" clearable style="width: 120px;">
                                        <el-option label="全部" value=""></el-option>
                                        <el-option label="购房" value="购房"></el-option>
                                        <el-option label="迁离" value="迁离"></el-option>
                                        <el-option label="其他" value="其他"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="承租人">
                                    <el-input v-model="exitSearch.tenant" placeholder="请输入承租人姓名" clearable style="width: 150px;"></el-input>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="searchExits">搜索</el-button>
                                    <el-button @click="resetExitSearch">重置</el-button>
                                </el-form-item>
                            </el-form>
                        </el-card>

                        <!-- 退租申请列表 -->
                        <el-card style="margin-top: 20px;">
                            <div slot="header">
                                <span>退租申请列表</span>
                            </div>

                            <el-table :data="filteredExits" stripe style="width: 100%">
                                <el-table-column prop="id" label="申请编号" width="120"></el-table-column>
                                <el-table-column prop="houseAddress" label="房源地址" min-width="200"></el-table-column>
                                <el-table-column prop="tenant" label="承租人" width="100"></el-table-column>
                                <el-table-column prop="reason" label="退租原因" width="100"></el-table-column>
                                <el-table-column prop="applyDate" label="申请时间" width="120"></el-table-column>
                                <el-table-column prop="expectedDate" label="预计退租日期" width="120"></el-table-column>
                                <el-table-column prop="deposit" label="押金金额" width="100">
                                    <template slot-scope="scope">
                                        ¥{{ scope.row.deposit }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="status" label="状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getExitStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="160">
                                    <template slot-scope="scope">
                                        <div class="action-buttons">
                                            <el-button size="mini" @click="viewExit(scope.row)">查看</el-button>
                                            <el-button size="mini" type="primary" @click="approveExit(scope.row)" v-if="scope.row.status === '待审核'">审核</el-button>
                                            <el-button size="mini" type="success" @click="scheduleInspection(scope.row)" v-if="scope.row.status === '审核通过'">安排验房</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>

                            <!-- 分页 -->
                            <div style="text-align: center; margin-top: 20px;">
                                <el-pagination
                                    @current-change="handleExitPageChange"
                                    :current-page.sync="exitCurrentPage"
                                    :page-size="exitPageSize"
                                    layout="prev, pager, next, jumper"
                                    :total="totalExits">
                                </el-pagination>
                            </div>
                        </el-card>
                    </el-tab-pane>

                    <el-tab-pane label="线上验房" name="inspection">
                        <el-card>
                            <div slot="header">
                                <span>验房管理</span>
                            </div>

                            <el-table :data="inspections" stripe style="width: 100%">
                                <el-table-column prop="id" label="验房编号" width="120"></el-table-column>
                                <el-table-column prop="houseAddress" label="房源地址" min-width="200"></el-table-column>
                                <el-table-column prop="tenant" label="承租人" width="100"></el-table-column>
                                <el-table-column prop="inspector" label="验房员" width="100"></el-table-column>
                                <el-table-column prop="scheduledDate" label="预约时间" width="150"></el-table-column>
                                <el-table-column prop="status" label="验房状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getInspectionStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="160">
                                    <template slot-scope="scope">
                                        <div class="action-buttons">
                                            <el-button size="mini" @click="viewInspection(scope.row)">查看</el-button>
                                            <el-button size="mini" type="primary" @click="startInspection(scope.row)" v-if="scope.row.status === '待验房'">开始验房</el-button>
                                            <el-button size="mini" type="success" @click="completeInspection(scope.row)" v-if="scope.row.status === '验房中'">完成验房</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-card>
                    </el-tab-pane>

                    <el-tab-pane label="押金管理" name="deposit">
                        <el-card>
                            <div slot="header">
                                <span>押金退还管理</span>
                            </div>

                            <el-table :data="deposits" stripe style="width: 100%">
                                <el-table-column prop="id" label="退款编号" width="120"></el-table-column>
                                <el-table-column prop="tenant" label="承租人" width="100"></el-table-column>
                                <el-table-column prop="houseAddress" label="房源地址" min-width="200"></el-table-column>
                                <el-table-column prop="originalDeposit" label="原押金" width="100">
                                    <template slot-scope="scope">
                                        ¥{{ scope.row.originalDeposit }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="deduction" label="扣除金额" width="100">
                                    <template slot-scope="scope">
                                        ¥{{ scope.row.deduction }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="refundAmount" label="退还金额" width="100">
                                    <template slot-scope="scope">
                                        ¥{{ scope.row.refundAmount }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="status" label="退款状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getDepositStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="160">
                                    <template slot-scope="scope">
                                        <div class="action-buttons">
                                            <el-button size="mini" @click="viewDeposit(scope.row)">查看</el-button>
                                            <el-button size="mini" type="primary" @click="processRefund(scope.row)" v-if="scope.row.status === '待退款'">处理退款</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-card>
                    </el-tab-pane>
                </el-tabs>

                <!-- 退租申请对话框 -->
                <el-dialog title="新增退租申请" :visible.sync="showExitDialog" width="60%">
                    <el-form :model="exitForm" label-width="100px" ref="exitForm">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="房源地址" required>
                                    <el-input v-model="exitForm.houseAddress" placeholder="请输入房源地址"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="承租人" required>
                                    <el-input v-model="exitForm.tenant" placeholder="请输入承租人姓名"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="退租原因" required>
                                    <el-select v-model="exitForm.reason" placeholder="请选择退租原因" style="width: 100%;">
                                        <el-option label="购房" value="购房"></el-option>
                                        <el-option label="迁离" value="迁离"></el-option>
                                        <el-option label="其他" value="其他"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="预计退租日期" required>
                                    <el-date-picker v-model="exitForm.expectedDate" type="date" placeholder="选择日期" style="width: 100%;"></el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-form-item label="详细说明">
                            <el-input type="textarea" v-model="exitForm.description" :rows="3" placeholder="请详细说明退租原因"></el-input>
                        </el-form-item>

                        <el-form-item label="联系电话">
                            <el-input v-model="exitForm.phone" placeholder="请输入联系电话"></el-input>
                        </el-form-item>

                        <el-form-item label="相关材料">
                            <el-upload
                                action="#"
                                list-type="picture-card"
                                :auto-upload="false"
                                :file-list="exitForm.fileList">
                                <i class="el-icon-plus"></i>
                            </el-upload>
                            <div style="color: #909399; font-size: 12px;">
                                请上传购房合同、工作调动证明等相关材料
                            </div>
                        </el-form-item>
                    </el-form>

                    <div slot="footer">
                        <el-button @click="showExitDialog = false">取消</el-button>
                        <el-button type="primary" @click="submitExit">提交申请</el-button>
                    </div>
                </el-dialog>

                <!-- 查看退租详情对话框 -->
                <el-dialog title="退租申请详情" :visible.sync="showExitDetailDialog" width="60%">
                    <div v-if="currentExit">
                        <el-descriptions title="申请信息" :column="2" border>
                            <el-descriptions-item label="申请编号">{{ currentExit.id }}</el-descriptions-item>
                            <el-descriptions-item label="申请状态">
                                <el-tag :type="getExitStatusType(currentExit.status)">{{ currentExit.status }}</el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label="房源地址">{{ currentExit.houseAddress }}</el-descriptions-item>
                            <el-descriptions-item label="承租人">{{ currentExit.tenant }}</el-descriptions-item>
                            <el-descriptions-item label="退租原因">{{ currentExit.reason }}</el-descriptions-item>
                            <el-descriptions-item label="申请时间">{{ currentExit.applyDate }}</el-descriptions-item>
                            <el-descriptions-item label="预计退租日期">{{ currentExit.expectedDate }}</el-descriptions-item>
                            <el-descriptions-item label="联系电话">{{ currentExit.phone || '未提供' }}</el-descriptions-item>
                        </el-descriptions>

                        <el-divider content-position="left">退租说明</el-divider>
                        <p>{{ currentExit.description || '无' }}</p>

                        <el-divider content-position="left">办理进度</el-divider>
                        <el-steps :active="getExitStep(currentExit.status)" finish-status="success">
                            <el-step title="提交申请" description="退租申请已提交"></el-step>
                            <el-step title="资格审核" description="审核退租资格"></el-step>
                            <el-step title="房屋验收" description="验收房屋状况"></el-step>
                            <el-step title="押金退还" description="退还押金"></el-step>
                            <el-step title="完成退租" description="退租手续完成"></el-step>
                        </el-steps>

                        <div v-if="currentExit.status !== '待审核'" style="margin-top: 20px;">
                            <el-divider content-position="left">处理记录</el-divider>
                            <el-timeline>
                                <el-timeline-item
                                    v-for="(record, index) in getExitRecords(currentExit)"
                                    :key="index"
                                    :timestamp="record.time"
                                    :type="record.type">
                                    {{ record.content }}
                                </el-timeline-item>
                            </el-timeline>
                        </div>
                    </div>

                    <div slot="footer">
                        <el-button @click="showExitDetailDialog = false">关闭</el-button>
                        <el-button v-if="currentExit && currentExit.status === '待审核'" type="primary" @click="approveExit">审核通过</el-button>
                        <el-button v-if="currentExit && currentExit.status === '待审核'" type="danger" @click="rejectExit">审核拒绝</el-button>
                    </div>
                </el-dialog>
            </div>
            
            <!-- 动态监管内容 -->
            <div v-if="activeTab === 'monitor'" class="tab-content">
                <el-tabs v-model="monitorActiveTab">
                    <el-tab-pane label="信用积分" name="credit">
                        <!-- 信用积分概览 -->
                        <el-row :gutter="20" style="margin-bottom: 20px;">
                            <el-col :span="8">
                                <el-card shadow="hover">
                                    <div style="display: flex; align-items: center;">
                                        <i class="el-icon-star-on" style="font-size: 40px; margin-right: 15px; color: #F7BA2A;"></i>
                                        <div>
                                            <div style="font-size: 24px; font-weight: bold;">{{ creditStats.averageScore }}</div>
                                            <div>平均信用分</div>
                                        </div>
                                    </div>
                                </el-card>
                            </el-col>
                            <el-col :span="8">
                                <el-card shadow="hover">
                                    <div style="display: flex; align-items: center;">
                                        <i class="el-icon-warning" style="font-size: 40px; margin-right: 15px; color: #E6A23C;"></i>
                                        <div>
                                            <div style="font-size: 24px; font-weight: bold;">{{ creditStats.lowCreditCount }}</div>
                                            <div>低信用用户</div>
                                        </div>
                                    </div>
                                </el-card>
                            </el-col>
                            <el-col :span="8">
                                <el-card shadow="hover">
                                    <div style="display: flex; align-items: center;">
                                        <i class="el-icon-trophy" style="font-size: 40px; margin-right: 15px; color: #67C23A;"></i>
                                        <div>
                                            <div style="font-size: 24px; font-weight: bold;">{{ creditStats.excellentCount }}</div>
                                            <div>优秀用户</div>
                                        </div>
                                    </div>
                                </el-card>
                            </el-col>
                        </el-row>

                        <!-- 信用积分列表 -->
                        <el-card>
                            <div slot="header">
                                <span>租户信用积分管理</span>
                            </div>

                            <el-form :inline="true" size="small" style="margin-bottom: 20px;">
                                <el-form-item label="信用等级">
                                    <el-select v-model="creditSearch.level" placeholder="请选择等级" clearable style="width: 120px;">
                                        <el-option label="全部" value=""></el-option>
                                        <el-option label="优秀" value="优秀"></el-option>
                                        <el-option label="良好" value="良好"></el-option>
                                        <el-option label="一般" value="一般"></el-option>
                                        <el-option label="较差" value="较差"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="租户姓名">
                                    <el-input v-model="creditSearch.tenant" placeholder="请输入租户姓名" clearable style="width: 150px;"></el-input>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="searchCredits">搜索</el-button>
                                    <el-button @click="resetCreditSearch">重置</el-button>
                                </el-form-item>
                            </el-form>

                            <el-table :data="filteredCredits" stripe style="width: 100%">
                                <el-table-column prop="tenant" label="租户姓名" width="100"></el-table-column>
                                <el-table-column prop="houseAddress" label="房源地址" min-width="200"></el-table-column>
                                <el-table-column prop="score" label="信用分" width="100">
                                    <template slot-scope="scope">
                                        <span :style="{color: getCreditScoreColor(scope.row.score)}">{{ scope.row.score }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="level" label="信用等级" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getCreditLevelType(scope.row.level)">{{ scope.row.level }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="lastUpdate" label="最后更新" width="120"></el-table-column>
                                <el-table-column label="操作" width="160">
                                    <template slot-scope="scope">
                                        <div class="action-buttons">
                                            <el-button size="mini" @click="viewCreditDetail(scope.row)">查看详情</el-button>
                                            <el-button size="mini" type="primary" @click="adjustCredit(scope.row)">调整积分</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>

                            <!-- 分页 -->
                            <div style="text-align: center; margin-top: 20px;">
                                <el-pagination
                                    @current-change="handleCreditPageChange"
                                    :current-page.sync="creditCurrentPage"
                                    :page-size="creditPageSize"
                                    layout="prev, pager, next, jumper"
                                    :total="totalCredits">
                                </el-pagination>
                            </div>
                        </el-card>
                    </el-tab-pane>

                    <el-tab-pane label="多部门核验" name="verification">
                        <el-card>
                            <div slot="header">
                                <span>多部门数据核验看板</span>
                                <el-button type="primary" size="small" style="float: right;" @click="refreshVerification">
                                    <i class="el-icon-refresh"></i> 刷新数据
                                </el-button>
                            </div>

                            <!-- 核验统计 -->
                            <el-row :gutter="20" style="margin-bottom: 20px;">
                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <div style="text-align: center;">
                                            <div style="font-size: 24px; font-weight: bold; color: #409EFF;">{{ verificationStats.totalChecked }}</div>
                                            <div>已核验户数</div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <div style="text-align: center;">
                                            <div style="font-size: 24px; font-weight: bold; color: #67C23A;">{{ verificationStats.passedCount }}</div>
                                            <div>核验通过</div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <div style="text-align: center;">
                                            <div style="font-size: 24px; font-weight: bold; color: #E6A23C;">{{ verificationStats.warningCount }}</div>
                                            <div>异常预警</div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <div style="text-align: center;">
                                            <div style="font-size: 24px; font-weight: bold; color: #F56C6C;">{{ verificationStats.failedCount }}</div>
                                            <div>核验失败</div>
                                        </div>
                                    </el-card>
                                </el-col>
                            </el-row>

                            <!-- 核验结果列表 -->
                            <el-table :data="verifications" stripe style="width: 100%">
                                <el-table-column prop="tenant" label="租户姓名" width="100"></el-table-column>
                                <el-table-column prop="idCard" label="身份证号" width="180">
                                    <template slot-scope="scope">
                                        {{ scope.row.idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2') }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="houseAddress" label="房源地址" min-width="200"></el-table-column>
                                <el-table-column prop="publicSecurity" label="公安核验" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getVerificationStatusType(scope.row.publicSecurity)">{{ scope.row.publicSecurity }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="civilAffairs" label="民政核验" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getVerificationStatusType(scope.row.civilAffairs)">{{ scope.row.civilAffairs }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="realEstate" label="不动产核验" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getVerificationStatusType(scope.row.realEstate)">{{ scope.row.realEstate }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="lastCheck" label="最后核验时间" width="150"></el-table-column>
                                <el-table-column label="操作" width="160">
                                    <template slot-scope="scope">
                                        <div class="action-buttons">
                                            <el-button size="mini" @click="viewVerificationDetail(scope.row)">查看详情</el-button>
                                            <el-button size="mini" type="primary" @click="recheckVerification(scope.row)">重新核验</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>

                            <!-- 分页 -->
                            <div style="text-align: center; margin-top: 20px;">
                                <el-pagination
                                    @current-change="handleVerificationPageChange"
                                    :current-page.sync="verificationCurrentPage"
                                    :page-size="verificationPageSize"
                                    layout="prev, pager, next, jumper"
                                    :total="totalVerifications">
                                </el-pagination>
                            </div>
                        </el-card>
                    </el-tab-pane>

                    <el-tab-pane label="异常监控" name="alert">
                        <el-card>
                            <div slot="header">
                                <span>异常情况监控</span>
                            </div>

                            <el-table :data="alerts" stripe style="width: 100%">
                                <el-table-column prop="id" label="预警编号" width="120"></el-table-column>
                                <el-table-column prop="tenant" label="租户姓名" width="100"></el-table-column>
                                <el-table-column prop="type" label="异常类型" width="120">
                                    <template slot-scope="scope">
                                        <el-tag :type="getAlertType(scope.row.type)">{{ scope.row.type }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="description" label="异常描述" min-width="200"></el-table-column>
                                <el-table-column prop="level" label="严重程度" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getAlertLevelType(scope.row.level)">{{ scope.row.level }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="createTime" label="发现时间" width="150"></el-table-column>
                                <el-table-column prop="status" label="处理状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getAlertStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="120">
                                    <template slot-scope="scope">
                                        <div class="action-buttons">
                                            <el-button size="mini" @click="viewAlert(scope.row)">查看</el-button>
                                            <el-button size="mini" type="primary" @click="handleAlert(scope.row)" v-if="scope.row.status === '待处理'">处理</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-card>
                    </el-tab-pane>
                </el-tabs>

                <!-- 信用积分详情对话框 -->
                <el-dialog title="信用积分详情" :visible.sync="showCreditDetailDialog" width="60%">
                    <div v-if="currentCredit">
                        <el-descriptions title="租户信息" :column="2" border>
                            <el-descriptions-item label="租户姓名">{{ currentCredit.tenant }}</el-descriptions-item>
                            <el-descriptions-item label="身份证号">{{ currentCredit.idCard }}</el-descriptions-item>
                            <el-descriptions-item label="联系电话">{{ currentCredit.phone }}</el-descriptions-item>
                            <el-descriptions-item label="房源地址">{{ currentCredit.address }}</el-descriptions-item>
                            <el-descriptions-item label="当前积分">
                                <span :style="{color: getCreditScoreColor(currentCredit.score), fontSize: '18px', fontWeight: 'bold'}">
                                    {{ currentCredit.score }}分
                                </span>
                            </el-descriptions-item>
                            <el-descriptions-item label="信用等级">
                                <el-tag :type="getCreditLevelType(currentCredit.level)">{{ currentCredit.level }}</el-tag>
                            </el-descriptions-item>
                        </el-descriptions>

                        <el-divider content-position="left">积分变动记录</el-divider>
                        <el-timeline>
                            <el-timeline-item
                                v-for="(record, index) in getCreditHistory(currentCredit)"
                                :key="index"
                                :timestamp="record.date"
                                :type="record.type">
                                {{ record.description }} ({{ record.change > 0 ? '+' : '' }}{{ record.change }}分)
                            </el-timeline-item>
                        </el-timeline>
                    </div>

                    <div slot="footer">
                        <el-button @click="showCreditDetailDialog = false">关闭</el-button>
                        <el-button type="primary" @click="showAdjustCreditDialog = true">调整积分</el-button>
                    </div>
                </el-dialog>

                <!-- 调整积分对话框 -->
                <el-dialog title="调整信用积分" :visible.sync="showAdjustCreditDialog" width="40%">
                    <el-form :model="adjustCreditForm" label-width="100px" v-if="currentCredit">
                        <el-form-item label="租户姓名">
                            <el-input v-model="currentCredit.tenant" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="当前积分">
                            <el-input v-model="currentCredit.score" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="调整类型" required>
                            <el-select v-model="adjustCreditForm.type" placeholder="请选择调整类型" style="width: 100%;">
                                <el-option label="增加积分" value="add"></el-option>
                                <el-option label="扣减积分" value="subtract"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="调整分值" required>
                            <el-input-number v-model="adjustCreditForm.amount" :min="1" :max="100" placeholder="请输入调整分值"></el-input-number>
                        </el-form-item>
                        <el-form-item label="调整原因" required>
                            <el-input type="textarea" v-model="adjustCreditForm.reason" :rows="3" placeholder="请输入调整原因"></el-input>
                        </el-form-item>
                    </el-form>

                    <div slot="footer">
                        <el-button @click="showAdjustCreditDialog = false">取消</el-button>
                        <el-button type="primary" @click="submitCreditAdjustment">确认调整</el-button>
                    </div>
                </el-dialog>

                <!-- 核验详情对话框 -->
                <el-dialog title="核验详情" :visible.sync="showVerificationDetailDialog" width="60%">
                    <div v-if="currentVerification">
                        <el-descriptions title="核验信息" :column="2" border>
                            <el-descriptions-item label="租户姓名">{{ currentVerification.tenant }}</el-descriptions-item>
                            <el-descriptions-item label="身份证号">{{ currentVerification.idCard }}</el-descriptions-item>
                            <el-descriptions-item label="核验状态">
                                <el-tag :type="getVerificationStatusType(currentVerification.status)">{{ currentVerification.status }}</el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label="最后核验时间">{{ currentVerification.lastCheck }}</el-descriptions-item>
                        </el-descriptions>

                        <el-divider content-position="left">各部门核验结果</el-divider>
                        <el-table :data="getVerificationDetails(currentVerification)" border>
                            <el-table-column prop="department" label="核验部门" width="120"></el-table-column>
                            <el-table-column prop="item" label="核验项目"></el-table-column>
                            <el-table-column prop="result" label="核验结果" width="100">
                                <template slot-scope="scope">
                                    <el-tag :type="scope.row.result === '通过' ? 'success' : 'danger'">{{ scope.row.result }}</el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="checkTime" label="核验时间" width="150"></el-table-column>
                        </el-table>
                    </div>

                    <div slot="footer">
                        <el-button @click="showVerificationDetailDialog = false">关闭</el-button>
                        <el-button type="primary" @click="recheckVerification(currentVerification)">重新核验</el-button>
                    </div>
                </el-dialog>

                <!-- 异常详情对话框 -->
                <el-dialog title="异常详情" :visible.sync="showAlertDetailDialog" width="60%">
                    <div v-if="currentAlert">
                        <el-descriptions title="异常信息" :column="2" border>
                            <el-descriptions-item label="异常编号">{{ currentAlert.id }}</el-descriptions-item>
                            <el-descriptions-item label="异常类型">
                                <el-tag :type="getAlertType(currentAlert.type)">{{ currentAlert.type }}</el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label="严重程度">
                                <el-tag :type="getAlertLevelType(currentAlert.level)">{{ currentAlert.level }}</el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label="处理状态">
                                <el-tag :type="getAlertStatusType(currentAlert.status)">{{ currentAlert.status }}</el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label="租户姓名">{{ currentAlert.tenant }}</el-descriptions-item>
                            <el-descriptions-item label="房源地址">{{ currentAlert.address }}</el-descriptions-item>
                            <el-descriptions-item label="发现时间">{{ currentAlert.createTime }}</el-descriptions-item>
                            <el-descriptions-item label="处理人">{{ currentAlert.handler || '未分配' }}</el-descriptions-item>
                        </el-descriptions>

                        <el-divider content-position="left">异常详情</el-divider>
                        <p>{{ currentAlert.description }}</p>

                        <div v-if="currentAlert.status !== '待处理'">
                            <el-divider content-position="left">处理记录</el-divider>
                            <el-timeline>
                                <el-timeline-item
                                    v-for="(record, index) in getAlertHistory(currentAlert)"
                                    :key="index"
                                    :timestamp="record.time"
                                    :type="record.type">
                                    {{ record.content }}
                                </el-timeline-item>
                            </el-timeline>
                        </div>
                    </div>

                    <div slot="footer">
                        <el-button @click="showAlertDetailDialog = false">关闭</el-button>
                        <el-button v-if="currentAlert && currentAlert.status === '待处理'" type="primary" @click="processAlert">开始处理</el-button>
                        <el-button v-if="currentAlert && currentAlert.status === '处理中'" type="success" @click="completeAlert">完成处理</el-button>
                    </div>
                </el-dialog>
            </div>
            </div>

            <!-- 页面底部 -->
            <div class="footer" style="background: white; text-align: center; padding: 15px; border-top: 1px solid #e6e6e6; color: #909399;">
                © 2023 公租房智慧社区云平台
            </div>
        </div>
    </div>

    <script src="vue.js"></script>
    <script src="element-ui.js"></script>
    <script>
        // 页面级Vue实例
        new Vue({
            el: '#app',
            data: {
                // 当前激活的标签页
                activeTab: 'home',
                
                // 统计数据
                statistics: [
                    { title: '可用房源总数', value: '1,284', icon: 'el-icon-house' },
                    { title: '在租合同数量', value: '926', icon: 'el-icon-document' },
                    { title: '按时缴费率', value: '94.5%', icon: 'el-icon-money' },
                    { title: '待处理工单', value: '42', icon: 'el-icon-service' }
                ],
                
                // 模块列表
                modules: [
                    { name: '房源管理', tab: 'house', icon: 'el-icon-house' },
                    { name: '配租申请', tab: 'apply', icon: 'el-icon-document-checked' },
                    { name: '合同管理', tab: 'contract', icon: 'el-icon-document' },
                    { name: '租金管理', tab: 'payment', icon: 'el-icon-money' },
                    { name: '维修服务', tab: 'repair', icon: 'el-icon-service' },
                    { name: '退租管理', tab: 'exit', icon: 'el-icon-switch-button' },
                    { name: '动态监管', tab: 'monitor', icon: 'el-icon-data-line' }
                ],
                
                // 合同管理相关数据
                contractActiveTab: 'list',
                contractFilterForm: {
                    status: '',
                    dateRange: [],
                    tenant: ''
                },
                contractCurrentPage: 1,
                contractPageSize: 10,
                totalContracts: 50,
                contracts: [
                    {
                        id: 'CT001',
                        houseTitle: '融创·智慧公租房',
                        address: '滨海新区中央大道888号',
                        tenant: '张三',
                        idCard: '110101199001011234',
                        phone: '13800138000',
                        startDate: '2023-01-01',
                        endDate: '2026-01-01',
                        rent: 800,
                        status: '执行中',
                        signDate: '2022-12-25',
                        roomType: '一室一厅',
                        area: 45,
                        progress: 35,
                        activities: [
                            { content: '合同创建', timestamp: '2022-12-20 10:00', type: 'primary' },
                            { content: '电子签约完成', timestamp: '2022-12-25 15:30', type: 'success' },
                            { content: '房屋交接', timestamp: '2022-12-30 09:15', type: 'success' },
                            { content: '首月租金支付', timestamp: '2023-01-05 14:20', type: 'success' }
                        ],
                        cohabitants: [
                            { name: '李四', relation: '配偶', idCard: '110101199102024567', phone: '13900139000', status: '已授权' }
                        ]
                    },
                    {
                        id: 'CT002',
                        houseTitle: '龙湖·椿山公租房',
                        address: '武清区京津路123号',
                        tenant: '王五',
                        idCard: '120101199203035678',
                        phone: '13700137000',
                        startDate: '2023-02-01',
                        endDate: '2026-02-01',
                        rent: 1200,
                        status: '待签约',
                        roomType: '两室一厅',
                        area: 60
                    },
                    {
                        id: 'CT003',
                        houseTitle: '万科·城市花园公租房',
                        address: '南开区卫津路456号',
                        tenant: '赵六',
                        idCard: '130101199304047890',
                        phone: '13600136000',
                        startDate: '2022-10-01',
                        endDate: '2025-10-01',
                        rent: 1000,
                        status: '执行中',
                        signDate: '2022-09-25',
                        roomType: '两室一厅',
                        area: 55,
                        progress: 50,
                        activities: [
                            { content: '合同创建', timestamp: '2022-09-20 11:00', type: 'primary' },
                            { content: '电子签约完成', timestamp: '2022-09-25 16:00', type: 'success' },
                            { content: '房屋交接', timestamp: '2022-09-30 10:00', type: 'success' },
                            { content: '首月租金支付', timestamp: '2022-10-05 09:30', type: 'success' }
                        ],
                        cohabitants: [
                            { name: '钱七', relation: '子女', idCard: '130101201504041234', phone: '13500135000', status: '已授权' },
                            { name: '孙八', relation: '父母', idCard: '130101196504041234', phone: '13400134000', status: '未授权' }
                        ]
                    },
                    {
                        id: 'CT004',
                        houseTitle: '保利·未来城公租房',
                        address: '河西区友谊路789号',
                        tenant: '周九',
                        idCard: '140101199405051122',
                        phone: '13300133000',
                        startDate: '2022-06-01',
                        endDate: '2025-06-01',
                        rent: 950,
                        status: '已到期',
                        signDate: '2022-05-25',
                        roomType: '一室一厅',
                        area: 50,
                        progress: 100,
                        activities: [
                            { content: '合同创建', timestamp: '2022-05-20 14:00', type: 'primary' },
                            { content: '电子签约完成', timestamp: '2022-05-25 11:20', type: 'success' },
                            { content: '房屋交接', timestamp: '2022-05-30 15:45', type: 'success' },
                            { content: '合同到期', timestamp: '2022-06-01 00:00', type: 'warning' }
                        ]
                    }
                ],
                currentContract: null,
                contractDetailVisible: false,
                showContractDetail: false,
                showSignContract: false,
                
                // 签约相关数据
                currentContractForSigning: null,
                signStep: 0,
                contractRead: false,
                verificationComplete: false,
                idVerification: {
                    name: '王五',
                    idNumber: '120101199203035678'
                },
                signatureConfirm: false,
                
                // 共同居住人相关数据
                cohabitantDialogVisible: false,
                cohabitantForm: {
                    name: '',
                    relation: '',
                    idCard: '',
                    phone: ''
                },
                cohabitantFormRules: {
                    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
                    relation: [{ required: true, message: '请选择关系', trigger: 'change' }],
                    idCard: [{ required: true, message: '请输入身份证号', trigger: 'blur' }],
                    phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }]
                },
                
                // 终止合同相关数据
                terminateDialogVisible: false,
                terminateForm: {
                    reason: '',
                    description: '',
                    date: ''
                },
                terminateFormRules: {
                    reason: [{ required: true, message: '请选择终止原因', trigger: 'change' }],
                    description: [{ required: true, message: '请输入具体说明', trigger: 'blur' }],
                    date: [{ type: 'date', required: true, message: '请选择终止日期', trigger: 'change' }]
                },
                
                // 租金管理相关数据
                paymentActiveTab: 'list',
                paymentFilterForm: {
                    status: '',
                    month: '',
                    tenant: ''
                },
                paymentCurrentPage: 1,
                paymentPageSize: 10,
                totalBills: 50,
                billDetailVisible: false,
                currentBill: null,
                paymentMethod: 'wechat',
                showBillPayment: false,
                paymentStep: 0,
                bills: [
                    {
                        id: 'BILL001',
                        houseTitle: '融创·智慧公租房',
                        address: '滨海新区中央大道888号',
                        tenant: '张三',
                        phone: '13800138000',
                        month: '2023-06',
                        dueDate: '2023-06-05',
                        totalAmount: 800,
                        status: '已支付',
                        paymentTime: '2023-06-03 14:25:36',
                        paymentMethod: 'wechat',
                        transactionId: '202306031425360001',
                        items: [
                            { name: '房租', amount: 800, description: '2023年6月房租' }
                        ]
                    },
                    {
                        id: 'BILL002',
                        houseTitle: '融创·智慧公租房',
                        address: '滨海新区中央大道888号',
                        tenant: '张三',
                        phone: '13800138000',
                        month: '2023-07',
                        dueDate: '2023-07-05',
                        totalAmount: 950,
                        status: '待支付',
                        items: [
                            { name: '房租', amount: 800, description: '2023年7月房租' },
                            { name: '水费', amount: 50, description: '2023年6月水费' },
                            { name: '电费', amount: 100, description: '2023年6月电费' }
                        ]
                    },
                    {
                        id: 'BILL003',
                        houseTitle: '龙湖·椿山公租房',
                        address: '武清区京津路123号',
                        tenant: '王五',
                        phone: '13700137000',
                        month: '2023-06',
                        dueDate: '2023-06-05',
                        totalAmount: 1200,
                        status: '已支付',
                        paymentTime: '2023-06-04 09:15:22',
                        paymentMethod: 'alipay',
                        transactionId: '202306040915220002',
                        items: [
                            { name: '房租', amount: 1200, description: '2023年6月房租' }
                        ]
                    },
                    {
                        id: 'BILL004',
                        houseTitle: '万科·城市花园公租房',
                        address: '南开区卫津路456号',
                        tenant: '赵六',
                        phone: '13600136000',
                        month: '2023-06',
                        dueDate: '2023-06-05',
                        totalAmount: 1150,
                        status: '已逾期',
                        overdueDays: 15,
                        lateFee: 57.5,
                        items: [
                            { name: '房租', amount: 1000, description: '2023年6月房租' },
                            { name: '物业费', amount: 150, description: '2023年6月物业费' }
                        ]
                    }
                ],
                
                // 缴费记录相关数据
                paymentHistoryFilterForm: {
                    dateRange: [],
                    method: ''
                },
                historyCurrentPage: 1,
                historyPageSize: 10,
                totalHistory: 30,
                paymentHistory: [
                    {
                        id: 'BILL001',
                        houseTitle: '融创·智慧公租房',
                        month: '2023-06',
                        paymentTime: '2023-06-03 14:25:36',
                        amount: 800,
                        paymentMethod: 'wechat'
                    },
                    {
                        id: 'BILL003',
                        houseTitle: '龙湖·椿山公租房',
                        month: '2023-06',
                        paymentTime: '2023-06-04 09:15:22',
                        amount: 1200,
                        paymentMethod: 'alipay'
                    },
                    {
                        id: 'PAY001',
                        houseTitle: '融创·智慧公租房',
                        month: '2023-05',
                        paymentTime: '2023-05-02 10:35:46',
                        amount: 800,
                        paymentMethod: 'bank'
                    },
                    {
                        id: 'PAY002',
                        houseTitle: '龙湖·椿山公租房',
                        month: '2023-05',
                        paymentTime: '2023-05-03 16:42:18',
                        amount: 1200,
                        paymentMethod: 'offline'
                    }
                ],
                
                // 缴费统计相关数据
                paymentStatsYear: '2023',
                monthlyStats: [
                    { month: '1月', amount: 45000 },
                    { month: '2月', amount: 42000 },
                    { month: '3月', amount: 48000 },
                    { month: '4月', amount: 46000 },
                    { month: '5月', amount: 44000 },
                    { month: '6月', amount: 47000 },
                    { month: '7月', amount: 49000 },
                    { month: '8月', amount: 43000 },
                    { month: '9月', amount: 45000 },
                    { month: '10月', amount: 48000 },
                    { month: '11月', amount: 46000 },
                    { month: '12月', amount: 50000 }
                ],
                
                // 筛选表单
                filterForm: {
                    area: '',
                    roomType: '',
                    priceMin: '',
                    priceMax: '',
                },
                
                // 房源列表
                houses: [
                    {
                        id: 1,
                        title: '融创·智慧公租房',
                        address: '滨海新区中央大道888号',
                        roomType: '一室一厅',
                        area: 45,
                        price: 800,
                        status: '可租',
                        imageUrl: 'https://via.placeholder.com/300x180?text=融创·智慧公租房'
                    },
                    {
                        id: 2,
                        title: '龙湖·椿山公租房',
                        address: '武清区京津路123号',
                        roomType: '两室一厅',
                        area: 60,
                        price: 1200,
                        status: '可租',
                        imageUrl: 'https://via.placeholder.com/300x180?text=龙湖·椿山公租房'
                    },
                    {
                        id: 3,
                        title: '万科·城市花园公租房',
                        address: '南开区卫津路456号',
                        roomType: '两室一厅',
                        area: 55,
                        price: 1000,
                        status: '可租',
                        imageUrl: 'https://via.placeholder.com/300x180?text=万科·城市花园公租房'
                    },
                    {
                        id: 4,
                        title: '保利·未来城公租房',
                        address: '河西区友谊路789号',
                        roomType: '一室一厅',
                        area: 50,
                        price: 950,
                        status: '已租',
                        imageUrl: 'https://via.placeholder.com/300x180?text=保利·未来城公租房'
                    }
                ],
                
                // 分页数据
                currentPage: 1,
                pageSize: 8,
                totalHouses: 32,
                
                // 当前选中的房源
                currentHouse: null,
                detailDialogVisible: false,

                // 配租申请相关数据
                showApplyDialog: false,
                showViewApplicationDialog: false,
                showEditApplicationDialog: false,
                currentApplication: null,
                applySearch: {
                    idCard: '',
                    address: '',
                    status: ''
                },
                applyForm: {
                    applicantName: '',
                    idCard: '',
                    phone: '',
                    familySize: 1,
                    preferredArea: '',
                    preferredRoomType: '',
                    monthlyIncome: '',
                    reason: '',
                    fileList: [],
                    agreed: false
                },
                editApplicationForm: {
                    applicantName: '',
                    idCard: '',
                    phone: '',
                    roomType: '',
                    houseAddress: ''
                },
                applications: [
                    {
                        id: 'APP001',
                        applicantName: '张三',
                        idCard: '110101199001011234',
                        phone: '13800138001',
                        houseAddress: '滨海新区海河东路公租房小区A栋',
                        roomType: '两室一厅',
                        applyDate: '2023-10-15',
                        status: '待审核'
                    },
                    {
                        id: 'APP002',
                        applicantName: '李四',
                        idCard: '110101199002021234',
                        phone: '13800138002',
                        houseAddress: '武清区建设路公租房小区B栋',
                        roomType: '一室一厅',
                        applyDate: '2023-10-12',
                        status: '审核通过'
                    },
                    {
                        id: 'APP003',
                        applicantName: '王五',
                        idCard: '110101199003031234',
                        phone: '13800138003',
                        houseAddress: '南开区长江道公租房小区C栋',
                        roomType: '三室一厅',
                        applyDate: '2023-10-10',
                        status: '已配租'
                    },
                    {
                        id: 'APP004',
                        applicantName: '赵六',
                        idCard: '110101199004041234',
                        phone: '13800138004',
                        houseAddress: '河西区友谊路公租房小区D栋',
                        roomType: '两室一厅',
                        applyDate: '2023-10-08',
                        status: '审核拒绝'
                    }
                ],
                applyCurrentPage: 1,
                applyPageSize: 10,
                totalApplications: 4,

                // 合同管理相关数据
                contractActiveTab: 'list',
                contractFilterForm: {
                    status: '',
                    dateRange: null,
                    tenant: ''
                },
                contracts: [
                    {
                        id: 'CT001',
                        houseTitle: '滨海新区海河东路公租房小区A栋101',
                        address: '滨海新区海河东路123号A栋101室',
                        tenant: '张三',
                        phone: '13800138001',
                        startDate: '2023-11-01',
                        endDate: '2026-10-31',
                        rent: 800,
                        status: '执行中',
                        progress: 75,
                        activities: [
                            { content: '合同签署完成', timestamp: '2023-11-01 10:30', type: 'success' },
                            { content: '首次缴费完成', timestamp: '2023-11-01 14:20', type: 'success' },
                            { content: '房屋交接完成', timestamp: '2023-11-02 09:15', type: 'success' },
                            { content: '11月份租金缴费', timestamp: '2023-11-05 16:45', type: 'success' }
                        ],
                        cohabitants: [
                            { name: '李梅', relation: '配偶', idCard: '110101199002021234', phone: '13800138002' },
                            { name: '张小明', relation: '子女', idCard: '110101201501011234', phone: '' }
                        ]
                    },
                    {
                        id: 'CT002',
                        houseTitle: '武清区建设路公租房小区B栋201',
                        address: '武清区建设路456号B栋201室',
                        tenant: '李四',
                        phone: '13800138003',
                        startDate: '2023-10-15',
                        endDate: '2026-10-14',
                        rent: 650,
                        status: '待签约',
                        progress: 0,
                        activities: [
                            { content: '合同生成完成', timestamp: '2023-10-10 14:30', type: 'success' },
                            { content: '等待承租人签约', timestamp: '2023-10-10 14:35', type: 'warning' }
                        ],
                        cohabitants: []
                    },
                    {
                        id: 'CT003',
                        houseTitle: '南开区长江道公租房小区C栋301',
                        tenant: '王五',
                        startDate: '2023-09-01',
                        endDate: '2026-08-31',
                        rent: 950,
                        status: '执行中'
                    },
                    {
                        id: 'CT004',
                        houseTitle: '河西区友谊路公租房小区D栋401',
                        tenant: '刘七',
                        startDate: '2022-08-01',
                        endDate: '2025-07-31',
                        rent: 750,
                        status: '已到期'
                    },
                    {
                        id: 'CT005',
                        houseTitle: '和平区南京路公租房小区E栋501',
                        tenant: '陈八',
                        startDate: '2023-06-01',
                        endDate: '2024-05-31',
                        rent: 900,
                        status: '已终止'
                    }
                ],
                contractCurrentPage: 1,
                contractPageSize: 10,
                totalContracts: 5,
                currentContractForSigning: null,
                signStep: 0,

                // 租金管理相关数据
                paymentActiveTab: 'list',
                currentBill: null,
                paymentFilterForm: {
                    status: '',
                    month: null,
                    tenant: ''
                },
                bills: [
                    {
                        id: 'BILL001',
                        houseTitle: '滨海新区海河东路公租房小区A栋101',
                        address: '滨海新区海河东路123号A栋101室',
                        tenant: '张三',
                        phone: '13800138001',
                        month: '2023-11',
                        dueDate: '2023-11-05',
                        totalAmount: 850,
                        rent: 800,
                        utilities: 50,
                        status: '待支付',
                        items: [
                            { name: '房租', amount: 800, description: '2023年11月房租' },
                            { name: '物业费', amount: 30, description: '2023年11月物业管理费' },
                            { name: '水费', amount: 15, description: '2023年11月用水费用' },
                            { name: '电费', amount: 5, description: '2023年11月用电费用' }
                        ]
                    },
                    {
                        id: 'BILL002',
                        houseTitle: '武清区建设路公租房小区B栋201',
                        tenant: '李四',
                        month: '2023-11',
                        dueDate: '2023-11-05',
                        totalAmount: 700,
                        rent: 650,
                        utilities: 50,
                        status: '已支付'
                    },
                    {
                        id: 'BILL003',
                        houseTitle: '南开区长江道公租房小区C栋301',
                        tenant: '王五',
                        month: '2023-10',
                        dueDate: '2023-10-05',
                        totalAmount: 1000,
                        rent: 950,
                        utilities: 50,
                        status: '已逾期'
                    },
                    {
                        id: 'BILL004',
                        houseTitle: '河西区友谊路公租房小区D栋401',
                        tenant: '刘七',
                        month: '2023-11',
                        dueDate: '2023-11-05',
                        totalAmount: 800,
                        rent: 750,
                        utilities: 50,
                        status: '已支付'
                    }
                ],
                paymentRecords: [
                    {
                        id: 'PAY001',
                        billId: 'BILL002',
                        tenant: '李四',
                        amount: 700,
                        paymentDate: '2023-11-03',
                        paymentMethod: '微信支付',
                        status: '成功'
                    },
                    {
                        id: 'PAY002',
                        billId: 'BILL004',
                        tenant: '刘七',
                        amount: 800,
                        paymentDate: '2023-11-02',
                        paymentMethod: '银行转账',
                        status: '成功'
                    }
                ],
                paymentStats: {
                    totalBills: 4,
                    paidBills: 2,
                    unpaidBills: 1,
                    overdueBills: 1,
                    totalAmount: 3350,
                    paidAmount: 1500,
                    unpaidAmount: 1850
                },
                billCurrentPage: 1,
                billPageSize: 10,
                totalBills: 4,
                paymentCurrentPage: 1,
                paymentPageSize: 10,
                totalPayments: 2,
                historyCurrentPage: 1,
                historyPageSize: 10,
                totalHistory: 2,

                // 维修服务相关数据
                repairActiveTab: 'list',
                showRepairDialog: false,
                showRepairDetailDialog: false,
                showAddWorkerDialog: false,
                showWorkerDetailDialog: false,
                showEditWorkerDialog: false,
                currentRepair: null,
                currentWorker: null,
                repairSearch: {
                    status: '',
                    type: '',
                    urgency: ''
                },
                repairForm: {
                    houseAddress: '',
                    tenant: '',
                    type: '',
                    urgency: '',
                    description: '',
                    phone: '',
                    fileList: []
                },
                repairs: [
                    {
                        id: 'REP001',
                        houseAddress: '滨海新区海河东路公租房小区A栋101',
                        tenant: '张三',
                        phone: '13800138001',
                        type: '水电',
                        description: '厨房水龙头漏水，需要更换',
                        urgency: '一般',
                        createTime: '2023-11-15',
                        status: '待处理'
                    },
                    {
                        id: 'REP002',
                        houseAddress: '武清区建设路公租房小区B栋201',
                        tenant: '李四',
                        phone: '13800138003',
                        type: '家具',
                        description: '卧室衣柜门脱落',
                        urgency: '不急',
                        createTime: '2023-11-14',
                        status: '处理中',
                        worker: {
                            name: '王师傅',
                            phone: '13900139001',
                            specialty: '家具维修',
                            rating: 4.5
                        }
                    },
                    {
                        id: 'REP003',
                        houseAddress: '南开区长江道公租房小区C栋301',
                        tenant: '王五',
                        type: '结构',
                        description: '客厅墙面出现裂缝',
                        urgency: '紧急',
                        createTime: '2023-11-13',
                        status: '已完成'
                    }
                ],
                repairWorkers: [
                    {
                        id: 'W001',
                        name: '刘师傅',
                        phone: '13800138001',
                        specialty: '水电维修',
                        workload: 3,
                        rating: 4.8,
                        status: '在线'
                    },
                    {
                        id: 'W002',
                        name: '陈师傅',
                        phone: '13800138002',
                        specialty: '家具维修',
                        workload: 2,
                        rating: 4.6,
                        status: '忙碌'
                    }
                ],
                repairCurrentPage: 1,
                repairPageSize: 10,
                totalRepairs: 3,

                // 维修员相关数据
                workerForm: {
                    name: '',
                    phone: '',
                    specialty: '',
                    experience: 0
                },
                editWorkerForm: {
                    name: '',
                    phone: '',
                    specialty: '',
                    status: ''
                },

                // 退租管理相关数据
                exitActiveTab: 'list',
                showExitDialog: false,
                showExitDetailDialog: false,
                currentExit: null,
                exitSearch: {
                    status: '',
                    reason: '',
                    tenant: ''
                },
                exitForm: {
                    houseAddress: '',
                    tenant: '',
                    reason: '',
                    expectedDate: '',
                    description: '',
                    phone: '',
                    fileList: []
                },
                exits: [
                    {
                        id: 'EXIT001',
                        houseAddress: '滨海新区海河东路公租房小区A栋101',
                        tenant: '张三',
                        reason: '购房',
                        applyDate: '2023-11-10',
                        expectedDate: '2023-12-01',
                        deposit: 1600,
                        status: '待审核'
                    },
                    {
                        id: 'EXIT002',
                        houseAddress: '武清区建设路公租房小区B栋201',
                        tenant: '李四',
                        reason: '迁离',
                        applyDate: '2023-11-08',
                        expectedDate: '2023-11-30',
                        deposit: 1300,
                        status: '审核通过'
                    }
                ],
                inspections: [
                    {
                        id: 'INS001',
                        houseAddress: '武清区建设路公租房小区B栋201',
                        tenant: '李四',
                        inspector: '王验房员',
                        scheduledDate: '2023-11-25 14:00',
                        status: '待验房'
                    }
                ],
                deposits: [
                    {
                        id: 'DEP001',
                        tenant: '赵六',
                        houseAddress: '河西区友谊路公租房小区D栋401',
                        originalDeposit: 1500,
                        deduction: 200,
                        refundAmount: 1300,
                        status: '待退款'
                    }
                ],
                exitCurrentPage: 1,
                exitPageSize: 10,
                totalExits: 2,

                // 动态监管相关数据
                monitorActiveTab: 'credit',
                showCreditDetailDialog: false,
                showAdjustCreditDialog: false,
                showVerificationDetailDialog: false,
                showAlertDetailDialog: false,
                currentCredit: null,
                currentVerification: null,
                currentAlert: null,
                creditSearch: {
                    level: '',
                    tenant: ''
                },
                adjustCreditForm: {
                    type: '',
                    amount: 0,
                    reason: ''
                },
                creditStats: {
                    averageScore: 85.6,
                    lowCreditCount: 12,
                    excellentCount: 156
                },
                credits: [
                    {
                        tenant: '张三',
                        houseAddress: '滨海新区海河东路公租房小区A栋101',
                        score: 92,
                        level: '优秀',
                        lastUpdate: '2023-11-15'
                    },
                    {
                        tenant: '李四',
                        houseAddress: '武清区建设路公租房小区B栋201',
                        score: 78,
                        level: '良好',
                        lastUpdate: '2023-11-14'
                    },
                    {
                        tenant: '王五',
                        houseAddress: '南开区长江道公租房小区C栋301',
                        score: 65,
                        level: '一般',
                        lastUpdate: '2023-11-13'
                    }
                ],
                creditCurrentPage: 1,
                creditPageSize: 10,
                totalCredits: 3,

                verificationStats: {
                    totalChecked: 245,
                    passedCount: 198,
                    warningCount: 32,
                    failedCount: 15
                },
                verifications: [
                    {
                        tenant: '张三',
                        idCard: '110101199001011234',
                        houseAddress: '滨海新区海河东路公租房小区A栋101',
                        publicSecurity: '通过',
                        civilAffairs: '通过',
                        realEstate: '通过',
                        lastCheck: '2023-11-15 10:30'
                    },
                    {
                        tenant: '李四',
                        idCard: '110101199002021234',
                        houseAddress: '武清区建设路公租房小区B栋201',
                        publicSecurity: '通过',
                        civilAffairs: '异常',
                        realEstate: '通过',
                        lastCheck: '2023-11-14 15:20'
                    }
                ],
                verificationCurrentPage: 1,
                verificationPageSize: 10,
                totalVerifications: 2,

                alerts: [
                    {
                        id: 'ALT001',
                        tenant: '李四',
                        type: '收入超标',
                        description: '民政系统返回收入异常，月收入超过申请标准',
                        level: '高',
                        createTime: '2023-11-14 15:25',
                        status: '待处理'
                    },
                    {
                        id: 'ALT002',
                        tenant: '王五',
                        type: '新增房产',
                        description: '不动产系统检测到新增房产信息',
                        level: '中',
                        createTime: '2023-11-13 09:15',
                        status: '已处理'
                    }
                ]
            },
            methods: {
                // 切换标签页
                handleTabChange(tab) {
                    console.log('切换到标签页:', tab);
                    this.activeTab = tab;
                },
                
                // 筛选房源
                filterHouses() {
                    this.$message({
                        message: '筛选条件已应用',
                        type: 'success'
                    });
                },
                
                // 重置筛选条件
                resetFilter() {
                    this.filterForm = {
                        area: '',
                        roomType: '',
                        priceMin: '',
                        priceMax: '',
                    };
                },
                
                // 查看房源详情
                viewDetail(id) {
                    this.currentHouse = this.houses.find(h => h.id === id);
                    this.detailDialogVisible = true;
                },
                
                // 申请配租
                applyRent(id) {
                    this.currentHouse = this.houses.find(h => h.id === id);
                    this.activeTab = 'apply';
                },
                
                // 分页变化
                handleCurrentChange(val) {
                    this.currentPage = val;
                    this.$message({
                        message: `加载第 ${val} 页数据`,
                        type: 'info'
                    });
                },

                // 配租申请相关方法
                searchApplications() {
                    this.$message({
                        message: '搜索申请信息',
                        type: 'info'
                    });
                },

                resetApplySearch() {
                    this.applySearch = {
                        idCard: '',
                        address: '',
                        status: ''
                    };
                },

                submitApplication() {
                    if (!this.applyForm.applicantName || !this.applyForm.idCard || !this.applyForm.phone) {
                        this.$message.error('请填写必填信息');
                        return;
                    }
                    if (!this.applyForm.agreed) {
                        this.$message.error('请先同意相关协议');
                        return;
                    }

                    // 生成新的申请记录
                    const newApplication = {
                        id: 'APP' + String(this.applications.length + 1).padStart(3, '0'),
                        applicantName: this.applyForm.applicantName,
                        idCard: this.applyForm.idCard,
                        phone: this.applyForm.phone,
                        houseAddress: this.applyForm.preferredArea + '公租房小区',
                        roomType: this.applyForm.preferredRoomType,
                        applyDate: new Date().toISOString().split('T')[0],
                        status: '待审核'
                    };

                    this.applications.unshift(newApplication);
                    this.totalApplications++;
                    this.showApplyDialog = false;

                    // 重置表单
                    this.applyForm = {
                        applicantName: '',
                        idCard: '',
                        phone: '',
                        familySize: 1,
                        preferredArea: '',
                        preferredRoomType: '',
                        monthlyIncome: '',
                        reason: '',
                        fileList: [],
                        agreed: false
                    };

                    this.$message.success('申请提交成功，请等待审核');
                },

                viewApplication(row) {
                    this.currentApplication = row;
                    this.showViewApplicationDialog = true;
                },

                editApplication(row) {
                    this.currentApplication = row;
                    this.editApplicationForm = {
                        applicantName: row.applicantName,
                        idCard: row.idCard,
                        phone: row.phone,
                        roomType: row.roomType,
                        houseAddress: row.houseAddress
                    };
                    this.showEditApplicationDialog = true;
                },

                updateApplication() {
                    if (!this.editApplicationForm.applicantName || !this.editApplicationForm.idCard || !this.editApplicationForm.phone) {
                        this.$message.error('请填写必填信息');
                        return;
                    }

                    // 更新当前申请信息
                    Object.assign(this.currentApplication, this.editApplicationForm);
                    this.showEditApplicationDialog = false;
                    this.$message.success('申请信息更新成功');
                },

                getApplicationStep(status) {
                    const stepMap = {
                        '待审核': 1,
                        '审核通过': 2,
                        '已配租': 3,
                        '审核拒绝': 1
                    };
                    return stepMap[status] || 0;
                },

                deleteApplication(id) {
                    this.$confirm('确认删除该申请吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        const index = this.applications.findIndex(app => app.id === id);
                        if (index > -1) {
                            this.applications.splice(index, 1);
                            this.totalApplications--;
                            this.$message.success('删除成功');
                        }
                    });
                },

                getStatusType(status) {
                    const typeMap = {
                        '待审核': 'warning',
                        '审核通过': 'success',
                        '审核拒绝': 'danger',
                        '已配租': 'info'
                    };
                    return typeMap[status] || 'info';
                },

                handleApplyPageChange(val) {
                    this.applyCurrentPage = val;
                    this.$message({
                        message: `加载第 ${val} 页申请数据`,
                        type: 'info'
                    });
                },

                // 合同管理相关方法
                resetContractFilter() {
                this.contractFilterForm = {
                    status: '',
                    dateRange: [],
                    tenant: ''
                };
            },
            
            filterContracts() {
                this.$message({
                    message: '合同筛选条件已应用',
                    type: 'success'
                });
            },
            
            handleContractCurrentChange(val) {
                this.contractCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页合同数据`,
                    type: 'info'
                });
            },

            signContract(contract) {
                this.currentContractForSigning = contract;
                this.contractActiveTab = 'sign';
                this.signStep = 0;
            },

            terminateContract(contract) {
                this.$confirm('确认终止该合同吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    contract.status = '已终止';
                    this.$message.success('合同已终止');
                });
            },

            getContractStatusType(status) {
                const typeMap = {
                    '待签约': 'warning',
                    '执行中': 'success',
                    '已到期': 'info',
                    '已终止': 'danger'
                };
                return typeMap[status] || 'info';
            },

            nextSignStep() {
                if (this.signStep < 3) {
                    this.signStep++;
                    if (this.signStep === 3) {
                        // 签约完成
                        this.currentContractForSigning.status = '执行中';
                        this.$message.success('合同签约完成！');
                    }
                }
            },

            prevSignStep() {
                if (this.signStep > 0) {
                    this.signStep--;
                }
            },

            // 租金管理相关方法
            resetPaymentFilter() {
                this.paymentFilterForm = {
                    status: '',
                    month: null,
                    tenant: ''
                };
            },

            filterPayments() {
                this.$message({
                    message: '账单筛选条件已应用',
                    type: 'success'
                });
            },

            payBill(bill) {
                this.$confirm('确认支付该账单吗？', '支付确认', {
                    confirmButtonText: '确定支付',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    bill.status = '已支付';

                    // 添加支付记录
                    const paymentRecord = {
                        id: 'PAY' + String(this.paymentRecords.length + 1).padStart(3, '0'),
                        billId: bill.id,
                        tenant: bill.tenant,
                        amount: bill.totalAmount,
                        paymentDate: new Date().toISOString().split('T')[0],
                        paymentMethod: '在线支付',
                        status: '成功'
                    };

                    this.paymentRecords.unshift(paymentRecord);
                    this.totalPayments++;

                    // 更新统计数据
                    this.paymentStats.paidBills++;
                    this.paymentStats.unpaidBills--;
                    this.paymentStats.paidAmount += bill.totalAmount;
                    this.paymentStats.unpaidAmount -= bill.totalAmount;

                    this.$message.success('支付成功！');
                });
            },

            getBillStatusType(status) {
                const typeMap = {
                    '待支付': 'warning',
                    '已支付': 'success',
                    '已逾期': 'danger'
                };
                return typeMap[status] || 'info';
            },

            handleBillCurrentChange(val) {
                this.billCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页账单数据`,
                    type: 'info'
                });
            },

            handlePaymentCurrentChange(val) {
                this.paymentCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页支付记录`,
                    type: 'info'
                });
            },

            viewBill(bill) {
                this.currentBill = bill;
                this.paymentActiveTab = 'pay';
            },

            downloadInvoice(record) {
                this.$message.success('发票下载成功！');
            },

            getPaymentMethodName(method) {
                const methodMap = {
                    '微信支付': '微信支付',
                    '银行转账': '银行转账',
                    '在线支付': '在线支付',
                    'wechat': '微信支付',
                    'alipay': '支付宝',
                    'bank': '银行卡',
                    'offline': '线下代缴'
                };
                return methodMap[method] || method;
            },

            filterPaymentHistory() {
                this.$message({
                    message: '缴费记录筛选条件已应用',
                    type: 'success'
                });
            },

            handleHistoryCurrentChange(val) {
                this.historyCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页缴费记录`,
                    type: 'info'
                });
            },

            // 维修服务相关方法
            searchRepairs() {
                this.$message({
                    message: '搜索维修工单',
                    type: 'info'
                });
            },

            resetRepairSearch() {
                this.repairSearch = {
                    status: '',
                    type: '',
                    urgency: ''
                };
            },

            submitRepair() {
                if (!this.repairForm.houseAddress || !this.repairForm.tenant || !this.repairForm.type) {
                    this.$message.error('请填写必填信息');
                    return;
                }

                const newRepair = {
                    id: 'REP' + String(this.repairs.length + 1).padStart(3, '0'),
                    houseAddress: this.repairForm.houseAddress,
                    tenant: this.repairForm.tenant,
                    type: this.repairForm.type,
                    description: this.repairForm.description,
                    urgency: this.repairForm.urgency,
                    createTime: new Date().toISOString().split('T')[0],
                    status: '待处理'
                };

                this.repairs.unshift(newRepair);
                this.totalRepairs++;
                this.showRepairDialog = false;

                // 重置表单
                this.repairForm = {
                    houseAddress: '',
                    tenant: '',
                    type: '',
                    urgency: '',
                    description: '',
                    phone: '',
                    fileList: []
                };

                this.$message.success('报修提交成功');
            },

            viewRepair(repair) {
                this.currentRepair = repair;
                this.showRepairDetailDialog = true;
            },

            assignRepair(repair) {
                this.$message.success('工单已派发给维修员');
                repair.status = '处理中';
            },

            getRepairStep(status) {
                const stepMap = {
                    '待处理': 1,
                    '处理中': 2,
                    '已完成': 3
                };
                return stepMap[status] || 0;
            },

            // 维修员管理方法
            addWorker() {
                if (!this.workerForm.name || !this.workerForm.phone || !this.workerForm.specialty) {
                    this.$message.error('请填写必填信息');
                    return;
                }

                const newWorker = {
                    id: 'W' + String(Date.now()).slice(-3),
                    name: this.workerForm.name,
                    phone: this.workerForm.phone,
                    specialty: this.workerForm.specialty,
                    workload: 0,
                    rating: 5.0,
                    status: '在线'
                };

                this.workers.unshift(newWorker);
                this.showAddWorkerDialog = false;

                // 重置表单
                this.workerForm = {
                    name: '',
                    phone: '',
                    specialty: '',
                    experience: 0
                };

                this.$message.success('维修员添加成功');
            },

            viewWorker(worker) {
                this.currentWorker = worker;
                this.showWorkerDetailDialog = true;
            },

            editWorker(worker) {
                this.currentWorker = worker;
                this.editWorkerForm = {
                    name: worker.name,
                    phone: worker.phone,
                    specialty: worker.specialty,
                    status: worker.status
                };
                this.showEditWorkerDialog = true;
            },

            updateWorker() {
                if (!this.editWorkerForm.name || !this.editWorkerForm.phone || !this.editWorkerForm.specialty) {
                    this.$message.error('请填写必填信息');
                    return;
                }

                Object.assign(this.currentWorker, this.editWorkerForm);
                this.showEditWorkerDialog = false;
                this.$message.success('维修员信息更新成功');
            },

            completeRepair(repair) {
                this.$confirm('确认完成该维修工单吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    repair.status = '已完成';
                    this.$message.success('维修工单已完成');
                });
            },

            getUrgencyType(urgency) {
                const typeMap = {
                    '紧急': 'danger',
                    '一般': 'warning',
                    '不急': 'info'
                };
                return typeMap[urgency] || 'info';
            },

            getRepairStatusType(status) {
                const typeMap = {
                    '待处理': 'warning',
                    '处理中': 'primary',
                    '已完成': 'success',
                    '已取消': 'info'
                };
                return typeMap[status] || 'info';
            },

            viewWorker(worker) {
                this.$message.info('查看维修员详情：' + worker.name);
            },

            editWorker(worker) {
                this.$message.info('编辑维修员信息：' + worker.name);
            },

            handleRepairPageChange(val) {
                this.repairCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页维修工单`,
                    type: 'info'
                });
            },

            // 退租管理相关方法
            searchExits() {
                this.$message({
                    message: '搜索退租申请',
                    type: 'info'
                });
            },

            resetExitSearch() {
                this.exitSearch = {
                    status: '',
                    reason: '',
                    tenant: ''
                };
            },

            submitExit() {
                if (!this.exitForm.houseAddress || !this.exitForm.tenant || !this.exitForm.reason) {
                    this.$message.error('请填写必填信息');
                    return;
                }

                const newExit = {
                    id: 'EXIT' + String(this.exits.length + 1).padStart(3, '0'),
                    houseAddress: this.exitForm.houseAddress,
                    tenant: this.exitForm.tenant,
                    reason: this.exitForm.reason,
                    applyDate: new Date().toISOString().split('T')[0],
                    expectedDate: this.exitForm.expectedDate,
                    deposit: 1500, // 默认押金
                    status: '待审核'
                };

                this.exits.unshift(newExit);
                this.totalExits++;
                this.showExitDialog = false;

                // 重置表单
                this.exitForm = {
                    houseAddress: '',
                    tenant: '',
                    reason: '',
                    expectedDate: '',
                    description: '',
                    phone: '',
                    fileList: []
                };

                this.$message.success('退租申请提交成功');
            },

            viewExit(exit) {
                this.currentExit = exit;
                this.showExitDetailDialog = true;
            },

            getExitStep(status) {
                const stepMap = {
                    '待审核': 1,
                    '审核通过': 2,
                    '验收中': 3,
                    '押金退还中': 4,
                    '已完成': 5,
                    '审核拒绝': 1
                };
                return stepMap[status] || 0;
            },

            getExitRecords(exit) {
                const records = [
                    { content: '退租申请已提交', time: exit.applyDate, type: 'success' }
                ];

                if (exit.status !== '待审核') {
                    records.push({ content: '申请审核通过', time: '2023-11-16 10:30', type: 'success' });
                }

                if (exit.status === '验收中' || exit.status === '押金退还中' || exit.status === '已完成') {
                    records.push({ content: '开始房屋验收', time: '2023-11-17 14:20', type: 'primary' });
                }

                if (exit.status === '押金退还中' || exit.status === '已完成') {
                    records.push({ content: '房屋验收完成', time: '2023-11-18 16:45', type: 'success' });
                    records.push({ content: '押金退还处理中', time: '2023-11-19 09:15', type: 'primary' });
                }

                if (exit.status === '已完成') {
                    records.push({ content: '押金已退还，退租完成', time: '2023-11-20 11:30', type: 'success' });
                }

                return records;
            },

            approveExit() {
                this.currentExit.status = '审核通过';
                this.showExitDetailDialog = false;
                this.$message.success('退租申请审核通过');
            },

            rejectExit() {
                this.currentExit.status = '审核拒绝';
                this.showExitDetailDialog = false;
                this.$message.success('退租申请已拒绝');
            },

            approveExit(exit) {
                this.$confirm('确认审核通过该退租申请吗？', '审核确认', {
                    confirmButtonText: '通过',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    exit.status = '审核通过';
                    this.$message.success('审核通过');
                });
            },

            scheduleInspection(exit) {
                this.$message.success('已安排验房，请等待验房员联系');
                exit.status = '待验房';
            },

            getExitStatusType(status) {
                const typeMap = {
                    '待审核': 'warning',
                    '审核通过': 'success',
                    '待验房': 'primary',
                    '已完成': 'info',
                    '已拒绝': 'danger'
                };
                return typeMap[status] || 'info';
            },

            viewInspection(inspection) {
                this.$message.info('查看验房详情：' + inspection.id);
            },

            startInspection(inspection) {
                this.$message.success('开始验房');
                inspection.status = '验房中';
            },

            completeInspection(inspection) {
                this.$message.success('验房完成');
                inspection.status = '已完成';
            },

            getInspectionStatusType(status) {
                const typeMap = {
                    '待验房': 'warning',
                    '验房中': 'primary',
                    '已完成': 'success'
                };
                return typeMap[status] || 'info';
            },

            viewDeposit(deposit) {
                this.$message.info('查看押金详情：' + deposit.id);
            },

            processRefund(deposit) {
                this.$message.success('押金退款处理中');
                deposit.status = '处理中';
            },

            getDepositStatusType(status) {
                const typeMap = {
                    '待退款': 'warning',
                    '处理中': 'primary',
                    '已退款': 'success'
                };
                return typeMap[status] || 'info';
            },

            handleExitPageChange(val) {
                this.exitCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页退租申请`,
                    type: 'info'
                });
            },

            // 动态监管相关方法
            searchCredits() {
                this.$message({
                    message: '搜索信用积分',
                    type: 'info'
                });
            },

            resetCreditSearch() {
                this.creditSearch = {
                    level: '',
                    tenant: ''
                };
            },

            viewCreditDetail(credit) {
                this.currentCredit = credit;
                this.showCreditDetailDialog = true;
            },

            adjustCredit(credit) {
                this.currentCredit = credit;
                this.adjustCreditForm = {
                    type: '',
                    amount: 0,
                    reason: ''
                };
                this.showAdjustCreditDialog = true;
            },

            getCreditHistory(credit) {
                return [
                    { date: '2023-11-01', description: '按时缴费', change: 5, type: 'success' },
                    { date: '2023-10-01', description: '按时缴费', change: 5, type: 'success' },
                    { date: '2023-09-15', description: '逾期缴费', change: -10, type: 'danger' },
                    { date: '2023-09-01', description: '按时缴费', change: 5, type: 'success' },
                    { date: '2023-08-01', description: '初始积分', change: 100, type: 'primary' }
                ];
            },

            submitCreditAdjustment() {
                if (!this.adjustCreditForm.type || !this.adjustCreditForm.amount || !this.adjustCreditForm.reason) {
                    this.$message.error('请填写完整信息');
                    return;
                }

                const change = this.adjustCreditForm.type === 'add' ? this.adjustCreditForm.amount : -this.adjustCreditForm.amount;
                this.currentCredit.score += change;

                // 更新信用等级
                if (this.currentCredit.score >= 90) {
                    this.currentCredit.level = '优秀';
                } else if (this.currentCredit.score >= 80) {
                    this.currentCredit.level = '良好';
                } else if (this.currentCredit.score >= 70) {
                    this.currentCredit.level = '一般';
                } else {
                    this.currentCredit.level = '较差';
                }

                this.showAdjustCreditDialog = false;
                this.showCreditDetailDialog = false;
                this.$message.success('积分调整成功');
            },

            getCreditScoreColor(score) {
                if (score >= 90) return '#67C23A';
                if (score >= 80) return '#409EFF';
                if (score >= 70) return '#E6A23C';
                return '#F56C6C';
            },

            getCreditLevelType(level) {
                const typeMap = {
                    '优秀': 'success',
                    '良好': 'primary',
                    '一般': 'warning',
                    '较差': 'danger'
                };
                return typeMap[level] || 'info';
            },

            handleCreditPageChange(val) {
                this.creditCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页信用数据`,
                    type: 'info'
                });
            },

            refreshVerification() {
                this.$message.success('数据刷新成功');
            },

            viewVerificationDetail(verification) {
                this.currentVerification = verification;
                this.showVerificationDetailDialog = true;
            },

            getVerificationDetails(verification) {
                return [
                    { department: '公安部门', item: '身份信息核验', result: '通过', checkTime: '2023-11-15 10:30' },
                    { department: '民政部门', item: '婚姻状况核验', result: '通过', checkTime: '2023-11-15 11:20' },
                    { department: '不动产部门', item: '房产信息核验', result: verification.status === '异常' ? '异常' : '通过', checkTime: '2023-11-15 14:45' },
                    { department: '税务部门', item: '收入信息核验', result: '通过', checkTime: '2023-11-15 16:15' }
                ];
            },

            recheckVerification(verification) {
                verification.status = '待核验';
                verification.lastCheck = new Date().toLocaleString();
                this.showVerificationDetailDialog = false;
                this.$message.success('重新核验已启动');
            },

            getVerificationStatusType(status) {
                const typeMap = {
                    '通过': 'success',
                    '异常': 'danger',
                    '待核验': 'warning'
                };
                return typeMap[status] || 'info';
            },

            handleVerificationPageChange(val) {
                this.verificationCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页核验数据`,
                    type: 'info'
                });
            },

            viewAlert(alert) {
                this.currentAlert = alert;
                this.showAlertDetailDialog = true;
            },

            getAlertHistory(alert) {
                const history = [
                    { content: '异常情况发现', time: alert.createTime, type: 'warning' }
                ];

                if (alert.status !== '待处理') {
                    history.push({ content: '开始处理异常', time: '2023-11-16 09:30', type: 'primary' });
                }

                if (alert.status === '已处理') {
                    history.push({ content: '异常处理完成', time: '2023-11-17 15:20', type: 'success' });
                }

                return history;
            },

            processAlert() {
                this.currentAlert.status = '处理中';
                this.currentAlert.handler = '管理员';
                this.showAlertDetailDialog = false;
                this.$message.success('异常处理已开始');
            },

            completeAlert() {
                this.currentAlert.status = '已处理';
                this.showAlertDetailDialog = false;
                this.$message.success('异常处理已完成');
            },

            handleAlert(alert) {
                this.$message.success('异常处理中...');
                alert.status = '处理中';
            },

            getAlertType(type) {
                const typeMap = {
                    '收入超标': 'danger',
                    '新增房产': 'warning',
                    '违规转租': 'danger',
                    '其他': 'info'
                };
                return typeMap[type] || 'info';
            },

            getAlertLevelType(level) {
                const typeMap = {
                    '高': 'danger',
                    '中': 'warning',
                    '低': 'info'
                };
                return typeMap[level] || 'info';
            },

            getAlertStatusType(status) {
                const typeMap = {
                    '待处理': 'warning',
                    '处理中': 'primary',
                    '已处理': 'success'
                };
                return typeMap[status] || 'info';
            },
            
            getContractStatusType(status) {
                switch (status) {
                    case '待签约': return 'warning';
                    case '执行中': return 'success';
                    case '已到期': return 'info';
                    case '已终止': return 'danger';
                    default: return '';
                }
            },
            
            viewContract(contract) {
                this.currentContract = contract;
                this.showContractDetail = true;
                this.showSignContract = false;
            },

            signContract(contract) {
                this.currentContract = contract;
                this.showSignContract = true;
                this.showContractDetail = false;
                this.signStep = 0;
                this.contractRead = false;
                this.verificationComplete = false;
                this.signatureConfirm = false;
            },

            // 返回合同列表
            backToContractList() {
                this.showContractDetail = false;
                this.showSignContract = false;
                this.currentContract = null;
                this.signStep = 0;
            },

            // 签约步骤控制
            nextSignStep() {
                if (this.signStep < 2) {
                    this.signStep++;
                } else {
                    // 完成签约
                    this.currentContract.status = '执行中';
                    this.currentContract.signTime = new Date().toLocaleString();
                    this.$message.success('签约成功！');
                }
            },

            prevSignStep() {
                if (this.signStep > 0) {
                    this.signStep--;
                }
            },

            // 下载合同
            downloadContract() {
                this.$message.success('合同下载成功！');
            },

            terminateContract(contract) {
                this.currentContract = contract;
                this.terminateForm = {
                    reason: '',
                    description: '',
                    date: new Date()
                };
                this.terminateDialogVisible = true;
            },
            
            confirmTerminate() {
                this.$refs.terminateForm.validate(valid => {
                    if (valid) {
                        this.$confirm('确认终止该合同？此操作不可逆', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(() => {
                            // 模拟终止合同逻辑
                            const index = this.contracts.findIndex(c => c.id === this.currentContract.id);
                            if (index !== -1) {
                                this.contracts[index].status = '已终止';
                                this.contracts[index].activities.push({
                                    content: `合同终止：${this.terminateForm.reason}`,
                                    timestamp: new Date().toLocaleString(),
                                    type: 'danger'
                                });
                            }
                            
                            this.$message({
                                message: '合同已终止',
                                type: 'success'
                            });
                            this.terminateDialogVisible = false;
                        }).catch(() => {
                            // 用户取消操作
                        });
                    } else {
                        this.$message({
                            message: '请完善终止信息',
                            type: 'error'
                        });
                    }
                });
            },
            
            // 电子签约相关方法
            nextStep() {
                if (this.signStep < 3) {
                    this.signStep++;
                    
                    // 模拟身份验证完成
                    if (this.signStep === 2) {
                        this.verificationComplete = true;
                    }
                }
            },
            
            prevStep() {
                if (this.signStep > 0) {
                    this.signStep--;
                }
            },
            
            cancelSign() {
                this.contractActiveTab = 'list';
                this.currentContractForSigning = null;
            },
            
            startFaceRecognition() {
                // 模拟人脸识别过程
                this.$message({
                    message: '人脸识别成功',
                    type: 'success'
                });
                this.verificationComplete = true;
            },
            
            clearSignature() {
                this.$message({
                    message: '签名已清除',
                    type: 'info'
                });
            },
            
            downloadContract() {
                this.$message({
                    message: '合同下载中...',
                    type: 'success'
                });
            },
            
            finishSigning() {
                // 模拟签约完成逻辑
                const index = this.contracts.findIndex(c => c.id === this.currentContractForSigning.id);
                if (index !== -1) {
                    this.contracts[index].status = '执行中';
                    this.contracts[index].signDate = new Date().toISOString().slice(0, 10);
                    this.contracts[index].progress = 25;
                    this.contracts[index].activities = [
                        { content: '合同创建', timestamp: new Date(new Date().getTime() - 5*24*60*60*1000).toLocaleString(), type: 'primary' },
                        { content: '电子签约完成', timestamp: new Date().toLocaleString(), type: 'success' }
                    ];
                }
                
                this.contractActiveTab = 'list';
                this.currentContractForSigning = null;
                
                this.$message({
                    message: '签约流程已完成',
                    type: 'success'
                });
            },
            
            // 共同居住人相关方法
            addCohabitant() {
                this.cohabitantForm = {
                    name: '',
                    relation: '',
                    idCard: '',
                    phone: ''
                };
                this.cohabitantDialogVisible = true;
            },
            
            submitCohabitant() {
                this.$refs.cohabitantForm.validate(valid => {
                    if (valid) {
                        // 模拟添加共同居住人逻辑
                        if (!this.currentContract.cohabitants) {
                            this.currentContract.cohabitants = [];
                        }
                        
                        this.currentContract.cohabitants.push({
                            ...this.cohabitantForm,
                            status: '未授权'
                        });
                        
                        this.$message({
                            message: '共同居住人添加成功',
                            type: 'success'
                        });
                        
                        this.cohabitantDialogVisible = false;
                    } else {
                        this.$message({
                            message: '请完善共同居住人信息',
                            type: 'error'
                        });
                    }
                });
            },
            
            // 租金管理相关方法
            resetPaymentFilter() {
                this.paymentFilterForm = {
                    status: '',
                    month: '',
                    tenant: ''
                };
            },
            
            filterPayments() {
                this.$message({
                    message: '账单筛选条件已应用',
                    type: 'success'
                });
            },
            
            handlePaymentCurrentChange(val) {
                this.paymentCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页账单数据`,
                    type: 'info'
                });
            },
            
            getBillStatusType(status) {
                switch (status) {
                    case '待支付': return 'warning';
                    case '已支付': return 'success';
                    case '已逾期': return 'danger';
                    default: return '';
                }
            },
            
            payBill(bill) {
                this.currentBill = bill;
                this.showBillPayment = true;
                this.paymentStep = 0;
                this.paymentMethod = 'wechat';
            },

            // 返回账单列表
            backToBillList() {
                this.showBillPayment = false;
                this.currentBill = null;
                this.paymentStep = 0;
            },

            // 支付步骤控制
            nextPaymentStep() {
                if (this.paymentStep < 2) {
                    this.paymentStep++;
                } else {
                    // 完成支付
                    const index = this.bills.findIndex(b => b.id === this.currentBill.id);
                    if (index !== -1) {
                        this.bills[index].status = '已支付';
                        this.bills[index].paymentTime = new Date().toLocaleString();
                    }
                    this.$message.success('支付成功！');
                }
            },

            prevPaymentStep() {
                if (this.paymentStep > 0) {
                    this.paymentStep--;
                }
            },

            // 下载收据
            downloadReceipt() {
                this.$message.success('收据下载成功！');
            },

            confirmPayment() {
                // 模拟支付逻辑
                this.$confirm('确认支付该账单？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    const index = this.bills.findIndex(b => b.id === this.currentBill.id);
                    if (index !== -1) {
                        const now = new Date();
                        const paymentTime = now.toLocaleString();
                        const transactionId = now.getFullYear().toString() +
                            (now.getMonth() + 1).toString().padStart(2, '0') +
                            now.getDate().toString().padStart(2, '0') +
                            now.getHours().toString().padStart(2, '0') +
                            now.getMinutes().toString().padStart(2, '0') +
                            now.getSeconds().toString().padStart(2, '0') +
                            Math.floor(Math.random() * 10000).toString().padStart(4, '0');
                        
                        this.bills[index].status = '已支付';
                        this.bills[index].paymentTime = paymentTime;
                        this.bills[index].paymentMethod = this.paymentMethod;
                        this.bills[index].transactionId = transactionId;
                        
                        // 添加到支付历史
                        this.paymentHistory.unshift({
                            id: this.currentBill.id,
                            houseTitle: this.currentBill.houseTitle,
                            month: this.currentBill.month,
                            paymentTime: paymentTime,
                            amount: this.currentBill.totalAmount + (this.currentBill.lateFee || 0),
                            paymentMethod: this.paymentMethod
                        });
                        
                        this.$message({
                            message: '支付成功',
                            type: 'success'
                        });
                        
                        // 更新当前账单
                        this.currentBill = { ...this.bills[index] };
                    }
                }).catch(() => {
                    // 用户取消支付
                });
            },
            
            cancelPayment() {
                this.paymentActiveTab = 'list';
                this.currentBill = null;
            },
            
            downloadInvoice(bill) {
                this.$message({
                    message: '电子发票下载中...',
                    type: 'success'
                });
            },
            
            getPaymentMethodName(method) {
                switch (method) {
                    case 'wechat': return '微信支付';
                    case 'alipay': return '支付宝';
                    case 'bank': return '银行卡';
                    case 'offline': return '线下代缴';
                    default: return '未知';
                }
            },
            
            filterPaymentHistory() {
                this.$message({
                    message: '缴费记录筛选条件已应用',
                    type: 'success'
                });
            },
            
            handleHistoryCurrentChange(val) {
                this.historyCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页缴费记录`,
                    type: 'info'
                });
            }
            },

            // 计算属性
            computed: {
                filteredApplications() {
                    let filtered = this.applications;

                    if (this.applySearch.idCard) {
                        filtered = filtered.filter(app =>
                            app.idCard.includes(this.applySearch.idCard)
                        );
                    }

                    if (this.applySearch.address) {
                        filtered = filtered.filter(app =>
                            app.houseAddress.includes(this.applySearch.address)
                        );
                    }

                    if (this.applySearch.status) {
                        filtered = filtered.filter(app =>
                            app.status === this.applySearch.status
                        );
                    }

                    return filtered;
                },

                filteredRepairs() {
                    let filtered = this.repairs;

                    if (this.repairSearch.status) {
                        filtered = filtered.filter(repair =>
                            repair.status === this.repairSearch.status
                        );
                    }

                    if (this.repairSearch.type) {
                        filtered = filtered.filter(repair =>
                            repair.type === this.repairSearch.type
                        );
                    }

                    if (this.repairSearch.urgency) {
                        filtered = filtered.filter(repair =>
                            repair.urgency === this.repairSearch.urgency
                        );
                    }

                    return filtered;
                },

                filteredExits() {
                    let filtered = this.exits;

                    if (this.exitSearch.status) {
                        filtered = filtered.filter(exit =>
                            exit.status === this.exitSearch.status
                        );
                    }

                    if (this.exitSearch.reason) {
                        filtered = filtered.filter(exit =>
                            exit.reason === this.exitSearch.reason
                        );
                    }

                    if (this.exitSearch.tenant) {
                        filtered = filtered.filter(exit =>
                            exit.tenant.includes(this.exitSearch.tenant)
                        );
                    }

                    return filtered;
                },

                filteredCredits() {
                    let filtered = this.credits;

                    if (this.creditSearch.level) {
                        filtered = filtered.filter(credit =>
                            credit.level === this.creditSearch.level
                        );
                    }

                    if (this.creditSearch.tenant) {
                        filtered = filtered.filter(credit =>
                            credit.tenant.includes(this.creditSearch.tenant)
                        );
                    }

                    return filtered;
                }
            },

            // 组件挂载时设置默认标签页
            mounted() {
                console.log('应用已加载，当前标签页:', this.activeTab);
            }
        });
    </script>
</body>
</html> 