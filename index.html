<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公租房经租管理平台</title>
    <link rel="stylesheet" href="element-ui.css">
    <style>
        body {
            font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            overflow-x: hidden;
        }
        
        #app {
            min-height: 100vh;
            display: flex;
        }

        .header {
            background-color: #001529;
            color: white;
            padding: 12px 16px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
        }

        .logo i {
            font-size: 18px;
            margin-right: 6px;
        }

        .logo span {
            font-size: 14px;
            font-weight: bold;
            white-space: nowrap;
        }

        .sidebar {
            width: 200px !important;
            min-width: 200px !important;
            max-width: 200px !important;
            background-color: #001529;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
        }

        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: #f5f7fa;
            min-width: 0;
            overflow-x: hidden;
        }

        .top-header {
            background: white;
            padding: 16px 24px;
            border-bottom: 1px solid #e6e6e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #262626;
            margin: 0;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .content {
            flex: 1;
            padding: 24px 24px 24px 24px;
            overflow-y: auto;
            overflow-x: hidden;
            min-width: 0;
        }

        .tab-content h2 {
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
        
        .footer {
            background-color: #001529;
            color: white;
            text-align: center;
            padding: 15px;
        }
        
        .module-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }

        .module-card {
            text-align: center;
            padding: 24px 16px;
            cursor: pointer;
            transition: all 0.3s;
            border-radius: 8px;
        }

        .module-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .module-icon {
            font-size: 36px;
            margin-bottom: 8px;
            color: #409EFF;
        }

        .module-card .el-card__body {
            padding: 24px 16px;
        }

        /* 响应式设计优化 */
        @media (max-width: 1200px) {
            .sidebar {
                width: 180px;
            }

            .top-header {
                padding: 12px 20px;
            }

            .page-title {
                font-size: 18px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 60px;
            }

            .sidebar .header {
                padding: 8px;
                text-align: center;
            }

            .sidebar .logo span {
                display: none;
            }

            .sidebar-nav .el-menu-item {
                padding: 0 15px;
                text-align: center;
            }

            .top-header {
                padding: 8px 16px;
            }

            .page-title {
                font-size: 16px;
            }

            .content {
                padding: 16px;
            }

            .module-grid {
                grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
                gap: 12px;
            }
        }

        /* 房源卡片样式 */
        .house-card {
            height: 100%;
            position: relative;
            transition: all 0.3s;
        }
        
        .house-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .house-status {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1;
        }
        
        .house-image {
            height: 180px;
            overflow: hidden;
            position: relative;
        }
        
        .house-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .house-info {
            padding: 10px 15px;
        }
        
        .house-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .house-address {
            color: #606266;
            font-size: 14px;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .house-price {
            color: #f56c6c;
            font-size: 18px;
            font-weight: bold;
            margin: 5px 0;
        }
        
        .filter-section {
            margin-bottom: 20px;
        }
        
        .tab-content {
            padding-top: 0;
        }

        /* 导航栏样式 */
        .sidebar-nav {
            flex: 1;
            padding: 0;
            margin-top: 10px;
        }

        .sidebar-nav .el-menu {
            background-color: #001529;
            border: none;
        }

        .sidebar-nav .el-menu-item {
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            height: 48px;
            line-height: 48px;
            border: none;
            margin: 2px 8px;
            border-radius: 6px;
            padding: 0 16px !important;
            position: relative;
        }

        .sidebar-nav .el-menu-item i {
            margin-right: 8px;
            font-size: 16px;
            width: 16px;
            text-align: center;
        }

        .sidebar-nav .el-menu-item:hover {
            background-color: rgba(24, 144, 255, 0.1);
            color: #1890ff;
        }

        .sidebar-nav .el-menu-item.is-active {
            background-color: #1890ff;
            color: white;
            font-weight: 500;
        }

        .sidebar-nav .el-menu-item.is-active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background-color: white;
            border-radius: 0 2px 2px 0;
        }
        
        .empty-module {
            text-align: center;
            padding: 100px 0;
        }
        
        .empty-module img {
            width: 120px;
            margin-bottom: 20px;
        }

        /* 防止水平滚动条的样式 */
        .table-container {
            overflow-x: auto;
            margin: -1px;
        }

        .el-table {
            min-width: 800px;
        }

        .el-card {
            overflow: hidden;
        }

        .tab-content {
            max-width: 100%;
            overflow-x: hidden;
        }

        .el-row {
            max-width: 100%;
        }

        .el-col {
            min-width: 0;
        }

        /* 操作按钮布局优化 */
        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            max-width: 200px;
        }

        .action-buttons .el-button {
            flex: 0 0 calc(50% - 2.5px);
            margin: 0 !important;
            font-size: 12px;
            padding: 5px 8px;
            min-width: 0;
        }

        .action-buttons .el-button + .el-button {
            margin-left: 0 !important;
        }

        /* 响应式表格 */
        @media (max-width: 1200px) {
            .el-table .el-table__header-wrapper,
            .el-table .el-table__body-wrapper {
                overflow-x: auto;
            }
        }

        /* 合同管理样式 */
        .contract-section {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 4px;
        }
        
        .highlight-section {
            background-color: #fdf6ec;
            border-left: 4px solid #e6a23c;
        }
        
        .important-clause {
            color: #f56c6c;
            font-weight: bold;
        }
        
        .verification-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .verification-section {
            flex: 1;
            min-width: 300px;
        }
        
        .camera-placeholder {
            height: 200px;
            background-color: #f5f7fa;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border: 1px dashed #d9d9d9;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .signature-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .signature-pad {
            width: 100%;
            height: 200px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f7fa;
        }
        
        .completion-container {
            text-align: center;
            padding: 40px 0;
        }
        
        .completion-container h2 {
            margin: 20px 0;
            color: #67C23A;
        }
        
        .completion-actions {
            margin-top: 30px;
        }

        /* 新增的UI优化样式 */
        .filter-section .el-card__header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 4px 4px 0 0;
        }

        .filter-section .el-card__header span {
            font-weight: 600;
            font-size: 16px;
        }

        .el-table {
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .el-table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
        }

        .el-table .el-table__row:hover {
            background: #f8f9fa;
        }

        .el-button {
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .el-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .el-card {
            border-radius: 12px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .el-card:hover {
            box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
        }

        .el-form-item__label {
            font-weight: 500;
            color: #495057;
        }

        .el-input__inner, .el-select .el-input__inner, .el-textarea__inner {
            border-radius: 6px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .el-input__inner:focus, .el-select .el-input__inner:focus, .el-textarea__inner:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .el-pagination {
            text-align: center;
            margin-top: 20px;
        }

        .el-pagination .el-pager li {
            border-radius: 6px;
            margin: 0 2px;
        }

        .el-tag {
            border-radius: 12px;
            font-weight: 500;
        }

        .el-dialog {
            border-radius: 12px;
            overflow: hidden;
        }

        .el-dialog__header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
        }

        .el-dialog__title {
            font-weight: 600;
            font-size: 18px;
        }

        .el-dialog__headerbtn .el-dialog__close {
            color: white;
            font-size: 20px;
        }

        .el-dialog__body {
            padding: 30px;
        }

        .el-dialog__footer {
            padding: 20px 30px;
            background: #f8f9fa;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .module-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .el-form--inline .el-form-item {
                display: block;
                margin-bottom: 15px;
            }

            .el-table__column {
                min-width: 120px;
            }
        }

        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 成功/错误提示样式 */
        .success-message {
            color: #67C23A;
            font-weight: 500;
        }

        .error-message {
            color: #F56C6C;
            font-weight: 500;
        }

        .warning-message {
            color: #E6A23C;
            font-weight: 500;
        }

        /* 面包屑导航样式 */
        .breadcrumb-container {
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid #e6e6e6;
        }

        .el-breadcrumb {
            font-size: 14px;
        }

        .el-breadcrumb__item {
            color: #909399;
        }

        .el-breadcrumb__item:last-child {
            color: #409EFF;
            font-weight: 500;
        }

        /* 头部操作按钮样式 */
        .header-actions {
            display: flex;
            gap: 8px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .header-actions .el-button {
            width: 32px;
            height: 32px;
            padding: 0;
        }
        
        .no-contract-for-signing,
        .no-contract-selected {
            text-align: center;
            padding: 100px 0;
            color: #909399;
        }
        
        .no-contract-for-signing h3,
        .no-contract-selected h3 {
            margin: 20px 0 10px;
        }
        
        .no-qualification {
            text-align: center;
            padding: 100px 0;
            color: #909399;
        }
        
        .no-qualification h3 {
            margin: 20px 0 10px;
        }
        
        .no-qualification p {
            margin-bottom: 20px;
        }
        
        /* 租金管理样式 */
        .no-bill-selected {
            text-align: center;
            padding: 100px 0;
            color: #909399;
        }
        
        .no-bill-selected h3 {
            margin: 20px 0 10px;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <!-- 页面头部 -->
            <div class="header">
                <div class="logo">
                    <i class="el-icon-office-building"></i>
                    <span>公租房经租管理平台</span>
                </div>

                <!-- 快捷操作按钮 -->
                <div class="header-actions" style="margin-top: 15px;">
                    <el-tooltip content="全屏显示" placement="bottom">
                        <el-button size="mini" circle @click="toggleFullscreen">
                            <i class="el-icon-full-screen"></i>
                        </el-button>
                    </el-tooltip>

                    <el-tooltip content="刷新数据" placement="bottom">
                        <el-button size="mini" circle @click="refreshData">
                            <i class="el-icon-refresh"></i>
                        </el-button>
                    </el-tooltip>

                    <el-tooltip content="系统设置" placement="bottom">
                        <el-button size="mini" circle @click="showSettings">
                            <i class="el-icon-setting"></i>
                        </el-button>
                    </el-tooltip>

                    <el-tooltip content="帮助文档" placement="bottom">
                        <el-button size="mini" circle @click="showHelp">
                            <i class="el-icon-question"></i>
                        </el-button>
                    </el-tooltip>
                </div>
            </div>

            <!-- 导航菜单 -->
            <div class="sidebar-nav">
                <el-menu mode="vertical" :default-active="activeTab" @select="handleTabChange" :collapse="false">
                    <el-menu-item index="home">
                        <i class="el-icon-s-home"></i>
                        <span>首页</span>
                    </el-menu-item>
                    <el-menu-item index="house">
                        <i class="el-icon-house"></i>
                        <span>房源管理</span>
                    </el-menu-item>
                    <el-menu-item index="person">
                        <i class="el-icon-user-solid"></i>
                        <span>人员管理</span>
                    </el-menu-item>
                    <el-menu-item index="apply">
                        <i class="el-icon-document-add"></i>
                        <span>配租申请</span>
                    </el-menu-item>
                    <el-menu-item index="contract">
                        <i class="el-icon-document"></i>
                        <span>合同管理</span>
                    </el-menu-item>
                    <el-menu-item index="payment">
                        <i class="el-icon-money"></i>
                        <span>租金管理</span>
                    </el-menu-item>
                    <el-menu-item index="repair">
                        <i class="el-icon-setting"></i>
                        <span>维修服务</span>
                    </el-menu-item>
                    <el-menu-item index="exit">
                        <i class="el-icon-switch-button"></i>
                        <span>退租管理</span>
                    </el-menu-item>
                    <el-menu-item index="monitor">
                        <i class="el-icon-view"></i>
                        <span>动态监管</span>
                    </el-menu-item>
                </el-menu>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-container">
            <!-- 顶部标题栏 -->
            <div class="top-header">
                <!-- 面包屑导航 -->
                <div class="breadcrumb-container">
                    <el-breadcrumb separator="/">
                        <el-breadcrumb-item>
                            <i class="el-icon-s-home"></i> 首页
                        </el-breadcrumb-item>
                        <el-breadcrumb-item v-if="activeTab !== 'home'">
                            {{ getPageTitle() }}
                        </el-breadcrumb-item>
                        <el-breadcrumb-item v-if="personCurrentView === 'family'">
                            家庭成员管理
                        </el-breadcrumb-item>
                    </el-breadcrumb>
                </div>

                <h1 class="page-title" v-if="activeTab === 'home'">欢迎使用公租房经租管理平台</h1>
                <h1 class="page-title" v-else-if="activeTab === 'house'">房源管理</h1>
                <h1 class="page-title" v-else-if="activeTab === 'person' && personCurrentView === 'applicant'">主申请人管理</h1>
                <h1 class="page-title" v-else-if="activeTab === 'person' && personCurrentView === 'family'">家庭成员管理</h1>
                <h1 class="page-title" v-else-if="activeTab === 'apply'">配租管理</h1>
                <h1 class="page-title" v-else-if="activeTab === 'contract'">合同管理</h1>
                <h1 class="page-title" v-else-if="activeTab === 'payment'">租金管理</h1>
                <h1 class="page-title" v-else-if="activeTab === 'repair'">维修服务</h1>
                <h1 class="page-title" v-else-if="activeTab === 'exit'">退租管理</h1>
                <h1 class="page-title" v-else-if="activeTab === 'monitor'">动态监管</h1>

                <div class="user-info">
                    <el-dropdown>
                        <span class="el-dropdown-link" style="color: #606266; cursor: pointer;">
                            <i class="el-icon-user"></i> 张三 <i class="el-icon-arrow-down"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item>个人中心</el-dropdown-item>
                            <el-dropdown-item>退出登录</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
            </div>

            <!-- 页面内容 -->
            <div class="content">
            <!-- 首页内容 -->
            <div v-if="activeTab === 'home'" class="tab-content">
                
                <el-row :gutter="16" style="margin-bottom: 20px;">
                    <el-col :span="6" v-for="item in statistics" :key="item.title">
                        <el-card shadow="hover" style="border-radius: 8px;">
                            <div style="display: flex; align-items: center; padding: 8px 0;">
                                <i :class="item.icon" style="font-size: 36px; margin-right: 12px; color: #409EFF;"></i>
                                <div>
                                    <div style="font-size: 22px; font-weight: bold; color: #262626;">{{ item.value }}</div>
                                    <div style="color: #8c8c8c; font-size: 14px;">{{ item.title }}</div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
                
                <h3 style="margin: 20px 0 16px 0; font-size: 16px; font-weight: 600; color: #262626;">系统模块</h3>
                
                <div class="module-grid">
                    <el-card shadow="hover" class="module-card" v-for="module in modules" :key="module.name" @click.native="handleTabChange(module.tab)">
                        <i :class="module.icon" class="module-icon"></i>
                        <div>{{ module.name }}</div>
                    </el-card>
                </div>
            </div>
            
            <!-- 房源管理内容 -->
            <div v-if="activeTab === 'house'" class="tab-content">
                
                <!-- 搜索筛选区域 -->
                <el-card class="filter-section">
                    <div slot="header">
                        <span>房源管理</span>
                        <div style="float: right;">
                            <el-button type="primary" size="small" @click="showHouseDialog = true">
                                <i class="el-icon-plus"></i> 新增房源
                            </el-button>
                            <el-button type="success" size="small" @click="showBatchGenerateDialog = true">
                                <i class="el-icon-document-add"></i> 批量生成
                            </el-button>
                        </div>
                    </div>

                    <el-form :inline="true" :model="houseFilterForm" size="small">
                        <el-form-item label="小区名称">
                            <el-input v-model="houseFilterForm.community" placeholder="请输入小区名称" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="幢">
                            <el-input v-model="houseFilterForm.building" placeholder="请输入幢号" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="单元">
                            <el-input v-model="houseFilterForm.unit" placeholder="请输入单元号" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="房间">
                            <el-input v-model="houseFilterForm.room" placeholder="请输入房间号" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="状态">
                            <el-select v-model="houseFilterForm.status" placeholder="选择状态" clearable>
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="可租" value="可租"></el-option>
                                <el-option label="已租" value="已租"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="searchHouses">
                                <i class="el-icon-search"></i> 搜索
                            </el-button>
                            <el-button @click="resetHouseFilter">
                                <i class="el-icon-refresh-left"></i> 重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
                
                <!-- 房源列表 -->
                <el-card style="margin-top: 20px;">
                    <el-table
                        :data="houses"
                        style="width: 100%"
                        border
                        stripe>
                        <el-table-column prop="community" label="小区名称" width="150"></el-table-column>
                        <el-table-column prop="building" label="幢" width="80"></el-table-column>
                        <el-table-column prop="unit" label="单元" width="80"></el-table-column>
                        <el-table-column prop="room" label="房间" width="80"></el-table-column>
                        <el-table-column prop="status" label="状态" width="100">
                            <template slot-scope="scope">
                                <el-tag :type="scope.row.status === '可租' ? 'success' : 'info'">
                                    {{ scope.row.status }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="area" label="面积(㎡)" width="100"></el-table-column>
                        <el-table-column prop="roomType" label="户型" width="120"></el-table-column>
                        <el-table-column prop="price" label="月租金(元)" width="120">
                            <template slot-scope="scope">
                                <span>{{ scope.row.price.toLocaleString() }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="150" fixed="right">
                            <template slot-scope="scope">
                                <el-button size="mini" type="primary" @click="editHouse(scope.row)">编辑</el-button>
                                <el-button size="mini" type="danger" @click="deleteHouse(scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <div style="text-align: center; margin-top: 20px;">
                        <el-pagination
                            @current-change="handleHouseCurrentChange"
                            :current-page.sync="houseCurrentPage"
                            :page-size="housePageSize"
                            layout="prev, pager, next, jumper"
                            :total="totalHouses">
                        </el-pagination>
                    </div>
                </el-card>

                <!-- 房源新增/编辑对话框 -->
                <el-dialog
                    :title="getHouseDialogTitle()"
                    :visible.sync="showHouseDialog"
                    width="60%"
                    @close="resetHouseForm">
                    <el-form :model="houseForm" :rules="houseRules" ref="houseForm" label-width="120px">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="小区名称" prop="community">
                                    <el-input v-model="houseForm.community" placeholder="请输入小区名称"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="幢" prop="building">
                                    <el-input v-model="houseForm.building" placeholder="请输入幢号"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="单元" prop="unit">
                                    <el-input v-model="houseForm.unit" placeholder="请输入单元号"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="房间" prop="room">
                                    <el-input v-model="houseForm.room" placeholder="请输入房间号"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="户型" prop="roomType">
                                    <el-select v-model="houseForm.roomType" style="width: 100%">
                                        <el-option label="一室一厅" value="一室一厅"></el-option>
                                        <el-option label="两室一厅" value="两室一厅"></el-option>
                                        <el-option label="三室一厅" value="三室一厅"></el-option>
                                        <el-option label="三室两厅" value="三室两厅"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="面积" prop="area">
                                    <el-input-number v-model="houseForm.area" :min="10" :max="200" style="width: 100%"></el-input-number>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="月租金" prop="price">
                                    <el-input-number v-model="houseForm.price" :min="100" :step="50" style="width: 100%"></el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="状态" prop="status">
                                    <el-select v-model="houseForm.status" style="width: 100%">
                                        <el-option label="可租" value="可租"></el-option>
                                        <el-option label="已租" value="已租"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                    <div slot="footer">
                        <el-button @click="showHouseDialog = false">取消</el-button>
                        <el-button type="primary" @click="saveHouse">确定</el-button>
                    </div>
                </el-dialog>

                <!-- 批量生成对话框 -->
                <el-dialog
                    title="批量生成房源"
                    :visible.sync="showBatchGenerateDialog"
                    width="50%"
                    @close="resetBatchForm">
                    <el-form :model="batchForm" :rules="batchRules" ref="batchForm" label-width="120px">
                        <el-form-item label="小区名称" prop="community">
                            <el-input v-model="batchForm.community" placeholder="请输入小区名称"></el-input>
                        </el-form-item>
                        <el-form-item label="幢号范围" prop="buildingRange">
                            <el-input-number v-model="batchForm.buildingStart" :min="1" placeholder="起始幢号" style="width: 120px;"></el-input-number>
                            <span style="margin: 0 10px;">至</span>
                            <el-input-number v-model="batchForm.buildingEnd" :min="1" placeholder="结束幢号" style="width: 120px;"></el-input-number>
                        </el-form-item>
                        <el-form-item label="单元数量" prop="unitCount">
                            <el-input-number v-model="batchForm.unitCount" :min="1" :max="10" style="width: 200px;"></el-input-number>
                        </el-form-item>
                        <el-form-item label="每层房间数" prop="roomsPerFloor">
                            <el-input-number v-model="batchForm.roomsPerFloor" :min="1" :max="20" style="width: 200px;"></el-input-number>
                        </el-form-item>
                        <el-form-item label="楼层数" prop="floors">
                            <el-input-number v-model="batchForm.floors" :min="1" :max="50" style="width: 200px;"></el-input-number>
                        </el-form-item>
                        <el-form-item label="户型" prop="roomType">
                            <el-select v-model="batchForm.roomType" style="width: 200px;">
                                <el-option label="一室一厅" value="一室一厅"></el-option>
                                <el-option label="两室一厅" value="两室一厅"></el-option>
                                <el-option label="三室一厅" value="三室一厅"></el-option>
                                <el-option label="三室两厅" value="三室两厅"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="面积" prop="area">
                            <el-input-number v-model="batchForm.area" :min="10" :max="200" style="width: 200px;"></el-input-number>
                        </el-form-item>
                        <el-form-item label="月租金" prop="price">
                            <el-input-number v-model="batchForm.price" :min="100" :step="50" style="width: 200px;"></el-input-number>
                        </el-form-item>
                    </el-form>
                    <div slot="footer">
                        <el-button @click="showBatchGenerateDialog = false">取消</el-button>
                        <el-button type="primary" @click="batchGenerateHouses">确定生成</el-button>
                    </div>
                </el-dialog>
            </div>

            <!-- 人员管理内容 -->
            <div v-if="activeTab === 'person'" class="tab-content">
                <!-- 主申请人列表页面 -->
                <div v-if="personCurrentView === 'applicant'">
                    <!-- 搜索筛选区域 -->
                    <el-card class="filter-section">
                        <div slot="header">
                            <span>主申请人管理</span>
                            <div style="float: right;">
                                <el-button type="primary" size="small" @click="showApplicantDialog = true">
                                    <i class="el-icon-plus"></i> 新增主申请人
                                </el-button>
                                <el-button type="success" size="small" @click="showApplicantImportDialog = true">
                                    <i class="el-icon-upload2"></i> 批量导入
                                </el-button>
                            </div>
                        </div>

                        <el-form :inline="true" :model="applicantFilterForm" size="small">
                            <el-form-item label="姓名">
                                <el-input v-model="applicantFilterForm.name" placeholder="请输入姓名" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="身份证号">
                                <el-input v-model="applicantFilterForm.idCard" placeholder="请输入身份证号" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="申请状态">
                                <el-select v-model="applicantFilterForm.status" placeholder="选择状态" clearable>
                                    <el-option label="全部状态" value=""></el-option>
                                    <el-option label="待审核" value="待审核"></el-option>
                                    <el-option label="已通过" value="已通过"></el-option>
                                    <el-option label="已拒绝" value="已拒绝"></el-option>
                                    <el-option label="已配租" value="已配租"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="申请时间">
                                <el-date-picker
                                    v-model="applicantFilterForm.dateRange"
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd">
                                </el-date-picker>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="searchApplicants">
                                    <i class="el-icon-search"></i> 搜索
                                </el-button>
                                <el-button @click="resetApplicantFilter">
                                    <i class="el-icon-refresh-left"></i> 重置
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </el-card>

                        <!-- 主申请人列表 -->
                        <el-card style="margin-top: 20px;">
                            <el-table
                                :data="applicants"
                                style="width: 100%"
                                border
                                stripe>
                                <el-table-column prop="name" label="姓名" width="100"></el-table-column>
                                <el-table-column prop="gender" label="性别" width="80"></el-table-column>
                                <el-table-column prop="age" label="年龄" width="80"></el-table-column>
                                <el-table-column prop="idCard" label="身份证号" width="180"></el-table-column>
                                <el-table-column prop="phone" label="联系电话" width="120"></el-table-column>
                                <el-table-column prop="address" label="现住址" min-width="200"></el-table-column>
                                <el-table-column prop="income" label="月收入(元)" width="100">
                                    <template slot-scope="scope">
                                        <span>{{ scope.row.income.toLocaleString() }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="familySize" label="家庭人数" width="100"></el-table-column>
                                <el-table-column prop="applyDate" label="申请时间" width="120"></el-table-column>
                                <el-table-column prop="status" label="状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getApplicantStatusType(scope.row.status)">
                                            {{ scope.row.status }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="240" fixed="right">
                                    <template slot-scope="scope">
                                        <div style="display: flex; flex-wrap: wrap; gap: 4px;">
                                            <el-button size="mini" @click="viewApplicant(scope.row)">详情</el-button>
                                            <el-button size="mini" type="primary" @click="editApplicant(scope.row)">编辑</el-button>
                                            <el-button size="mini" type="success" @click="viewFamilyMembers(scope.row)">家庭成员</el-button>
                                            <el-button size="mini" type="danger" @click="deleteApplicant(scope.row)">删除</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>

                            <!-- 分页 -->
                            <div style="text-align: center; margin-top: 20px;">
                                <el-pagination
                                    @current-change="handleApplicantCurrentChange"
                                    :current-page.sync="applicantCurrentPage"
                                    :page-size="applicantPageSize"
                                    layout="prev, pager, next, jumper"
                                    :total="totalApplicants">
                                </el-pagination>
                            </div>
                        </el-card>
                    </div>

                    <!-- 家庭成员列表页面 -->
                    <div v-else-if="personCurrentView === 'family'">
                        <!-- 返回按钮和标题 -->
                        <el-card class="filter-section">
                            <div slot="header">
                                <el-button type="text" @click="backToApplicantList" style="padding: 0; margin-right: 10px;">
                                    <i class="el-icon-arrow-left"></i> 返回主申请人列表
                                </el-button>
                                <span>{{ currentApplicantName }}的家庭成员</span>
                                <div style="float: right;">
                                    <el-button type="primary" size="small" @click="showFamilyDialog = true">
                                        <i class="el-icon-plus"></i> 新增家庭成员
                                    </el-button>
                                    <el-button type="success" size="small" @click="showFamilyImportDialog = true">
                                        <i class="el-icon-upload2"></i> 批量导入
                                    </el-button>
                                </div>
                            </div>

                            <el-form :inline="true" :model="familyFilterForm" size="small">
                                <el-form-item label="姓名">
                                    <el-input v-model="familyFilterForm.name" placeholder="请输入姓名" clearable></el-input>
                                </el-form-item>
                                <el-form-item label="关系">
                                    <el-select v-model="familyFilterForm.relationship" placeholder="选择关系" clearable>
                                        <el-option label="全部关系" value=""></el-option>
                                        <el-option label="配偶" value="配偶"></el-option>
                                        <el-option label="子女" value="子女"></el-option>
                                        <el-option label="父母" value="父母"></el-option>
                                        <el-option label="其他" value="其他"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="searchFamilyMembers">
                                        <i class="el-icon-search"></i> 搜索
                                    </el-button>
                                    <el-button @click="resetFamilyFilter">
                                        <i class="el-icon-refresh-left"></i> 重置
                                    </el-button>
                                </el-form-item>
                            </el-form>
                        </el-card>

                        <!-- 家庭成员列表 -->
                        <el-card style="margin-top: 20px;">
                            <el-table
                                :data="currentFamilyMembers"
                                style="width: 100%"
                                border
                                stripe>
                                <el-table-column prop="name" label="姓名" width="100"></el-table-column>
                                <el-table-column prop="gender" label="性别" width="80"></el-table-column>
                                <el-table-column prop="age" label="年龄" width="80"></el-table-column>
                                <el-table-column prop="idCard" label="身份证号" width="180"></el-table-column>
                                <el-table-column prop="phone" label="联系电话" width="120"></el-table-column>
                                <el-table-column prop="relationship" label="与申请人关系" width="120"></el-table-column>
                                <el-table-column prop="occupation" label="职业" width="120"></el-table-column>
                                <el-table-column prop="income" label="月收入(元)" width="100">
                                    <template slot-scope="scope">
                                        <span>{{ scope.row.income ? scope.row.income.toLocaleString() : '-' }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="180" fixed="right">
                                    <template slot-scope="scope">
                                        <div style="display: flex; flex-wrap: wrap; gap: 4px;">
                                            <el-button size="mini" @click="viewFamilyMember(scope.row)">详情</el-button>
                                            <el-button size="mini" type="primary" @click="editFamilyMember(scope.row)">编辑</el-button>
                                            <el-button size="mini" type="danger" @click="deleteFamilyMember(scope.row)">删除</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>

                            <!-- 分页 -->
                            <div style="text-align: center; margin-top: 20px;">
                                <el-pagination
                                    @current-change="handleFamilyCurrentChange"
                                    :current-page.sync="familyCurrentPage"
                                    :page-size="familyPageSize"
                                    layout="prev, pager, next, jumper"
                                    :total="currentFamilyMembersTotal">
                                </el-pagination>
                            </div>
                        </el-card>
                    </div>
                </div>
                <!-- 主申请人新增/编辑对话框 -->
                <el-dialog
                    :title="getApplicantDialogTitle()"
                    :visible.sync="showApplicantDialog"
                    width="60%"
                    @close="resetApplicantForm">
                    <el-form :model="applicantForm" :rules="applicantRules" ref="applicantForm" label-width="120px">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="姓名" prop="name">
                                    <el-input v-model="applicantForm.name" placeholder="请输入姓名"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="性别" prop="gender">
                                    <el-radio-group v-model="applicantForm.gender">
                                        <el-radio label="男">男</el-radio>
                                        <el-radio label="女">女</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="年龄" prop="age">
                                    <el-input-number v-model="applicantForm.age" :min="18" :max="80" style="width: 100%"></el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="身份证号" prop="idCard">
                                    <el-input v-model="applicantForm.idCard" placeholder="请输入身份证号"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="联系电话" prop="phone">
                                    <el-input v-model="applicantForm.phone" placeholder="请输入联系电话"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="月收入" prop="income">
                                    <el-input-number v-model="applicantForm.income" :min="0" :step="100" style="width: 100%"></el-input-number>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-form-item label="现住址" prop="address">
                            <el-input v-model="applicantForm.address" placeholder="请输入现住址"></el-input>
                        </el-form-item>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="家庭人数" prop="familySize">
                                    <el-input-number v-model="applicantForm.familySize" :min="1" :max="10" style="width: 100%"></el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="申请状态" prop="status">
                                    <el-select v-model="applicantForm.status" style="width: 100%">
                                        <el-option label="待审核" value="待审核"></el-option>
                                        <el-option label="已通过" value="已通过"></el-option>
                                        <el-option label="已拒绝" value="已拒绝"></el-option>
                                        <el-option label="已配租" value="已配租"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-form-item label="备注">
                            <el-input v-model="applicantForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息"></el-input>
                        </el-form-item>
                    </el-form>
                    <div slot="footer">
                        <el-button @click="showApplicantDialog = false">取消</el-button>
                        <el-button type="primary" @click="saveApplicant">确定</el-button>
                    </div>
                </el-dialog>

                <!-- 家庭成员新增/编辑对话框 -->
                <el-dialog
                    :title="getFamilyDialogTitle()"
                    :visible.sync="showFamilyDialog"
                    width="60%"
                    @close="resetFamilyForm">
                    <el-form :model="familyForm" :rules="familyRules" ref="familyForm" label-width="120px">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="姓名" prop="name">
                                    <el-input v-model="familyForm.name" placeholder="请输入姓名"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="性别" prop="gender">
                                    <el-radio-group v-model="familyForm.gender">
                                        <el-radio label="男">男</el-radio>
                                        <el-radio label="女">女</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="年龄" prop="age">
                                    <el-input-number v-model="familyForm.age" :min="0" :max="100" style="width: 100%"></el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="身份证号" prop="idCard">
                                    <el-input v-model="familyForm.idCard" placeholder="请输入身份证号"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="联系电话">
                                    <el-input v-model="familyForm.phone" placeholder="请输入联系电话"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="主申请人" prop="applicantId">
                                    <el-select v-model="familyForm.applicantId" style="width: 100%" filterable>
                                        <el-option
                                            v-for="applicant in applicants"
                                            :key="applicant.id"
                                            :label="applicant.name"
                                            :value="applicant.id">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="与申请人关系" prop="relationship">
                                    <el-select v-model="familyForm.relationship" style="width: 100%">
                                        <el-option label="配偶" value="配偶"></el-option>
                                        <el-option label="子女" value="子女"></el-option>
                                        <el-option label="父母" value="父母"></el-option>
                                        <el-option label="其他" value="其他"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="职业">
                                    <el-input v-model="familyForm.occupation" placeholder="请输入职业"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-form-item label="月收入">
                            <el-input-number v-model="familyForm.income" :min="0" :step="100" style="width: 200px"></el-input-number>
                            <span style="margin-left: 10px; color: #999;">元（可选）</span>
                        </el-form-item>
                        <el-form-item label="备注">
                            <el-input v-model="familyForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息"></el-input>
                        </el-form-item>
                    </el-form>
                    <div slot="footer">
                        <el-button @click="showFamilyDialog = false">取消</el-button>
                        <el-button type="primary" @click="saveFamilyMember">确定</el-button>
                    </div>
                </el-dialog>

                <!-- 主申请人批量导入对话框 -->
                <el-dialog
                    title="批量导入主申请人"
                    :visible.sync="showApplicantImportDialog"
                    width="50%">
                    <div style="margin-bottom: 20px;">
                        <el-alert
                            title="导入说明"
                            type="info"
                            :closable="false"
                            show-icon>
                            <div slot="description">
                                <p>1. 请下载模板文件，按照模板格式填写数据</p>
                                <p>2. 支持Excel格式文件(.xlsx, .xls)</p>
                                <p>3. 单次最多导入1000条记录</p>
                            </div>
                        </el-alert>
                    </div>

                    <el-upload
                        class="upload-demo"
                        drag
                        action="#"
                        :auto-upload="false"
                        :on-change="handleApplicantFileChange"
                        :file-list="applicantFileList"
                        accept=".xlsx,.xls">
                        <i class="el-icon-upload"></i>
                        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                        <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件</div>
                    </el-upload>

                    <div slot="footer">
                        <el-button @click="downloadApplicantTemplate">下载模板</el-button>
                        <el-button @click="showApplicantImportDialog = false">取消</el-button>
                        <el-button type="primary" @click="importApplicants">确定导入</el-button>
                    </div>
                </el-dialog>

                <!-- 家庭成员批量导入对话框 -->
                <el-dialog
                    title="批量导入家庭成员"
                    :visible.sync="showFamilyImportDialog"
                    width="50%">
                    <div style="margin-bottom: 20px;">
                        <el-alert
                            title="导入说明"
                            type="info"
                            :closable="false"
                            show-icon>
                            <div slot="description">
                                <p>1. 请下载模板文件，按照模板格式填写数据</p>
                                <p>2. 支持Excel格式文件(.xlsx, .xls)</p>
                                <p>3. 单次最多导入1000条记录</p>
                                <p>4. 主申请人必须已存在系统中</p>
                            </div>
                        </el-alert>
                    </div>

                    <el-upload
                        class="upload-demo"
                        drag
                        action="#"
                        :auto-upload="false"
                        :on-change="handleFamilyFileChange"
                        :file-list="familyFileList"
                        accept=".xlsx,.xls">
                        <i class="el-icon-upload"></i>
                        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                        <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件</div>
                    </el-upload>

                    <div slot="footer">
                        <el-button @click="downloadFamilyTemplate">下载模板</el-button>
                        <el-button @click="showFamilyImportDialog = false">取消</el-button>
                        <el-button type="primary" @click="importFamilyMembers">确定导入</el-button>
                    </div>
                </el-dialog>
            </div>

            <!-- 配租管理内容 -->
            <div v-if="activeTab === 'apply'" class="tab-content">
                <!-- 搜索和操作区域 -->
                <el-card class="filter-section">
                    <div slot="header">
                        <span>配租管理</span>
                        <div style="float: right;">
                            <el-button type="primary" size="small" @click="showRentDialog = true">
                                <i class="el-icon-plus"></i> 新增配租
                            </el-button>
                            <el-button type="success" size="small" @click="showRentImportDialog = true">
                                <i class="el-icon-upload2"></i> 批量导入
                            </el-button>
                            <el-button type="warning" size="small" @click="showTransferDialog = true">
                                <i class="el-icon-sort"></i> 调房
                            </el-button>
                            <el-button type="info" size="small" @click="showExchangeDialog = true">
                                <i class="el-icon-refresh"></i> 换房
                            </el-button>
                            <el-button type="danger" size="small" @click="showChangeApplicantDialog = true">
                                <i class="el-icon-user"></i> 主申请人变更
                            </el-button>
                        </div>
                    </div>

                    <el-form :inline="true" :model="rentFilterForm" size="small">
                        <el-form-item label="房源地址">
                            <el-input v-model="rentFilterForm.address" placeholder="请输入房源地址" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="申请人身份证">
                            <el-input v-model="rentFilterForm.idCard" placeholder="请输入身份证号码" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="申请人姓名">
                            <el-input v-model="rentFilterForm.name" placeholder="请输入申请人姓名" clearable></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="searchRentRecords">
                                <i class="el-icon-search"></i> 搜索
                            </el-button>
                            <el-button @click="resetRentFilter">
                                <i class="el-icon-refresh-left"></i> 重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </el-card>

                <!-- 配租记录列表 -->
                <el-card style="margin-top: 20px;">
                    <el-table
                        :data="rentRecords"
                        style="width: 100%"
                        border
                        stripe>
                        <el-table-column prop="applicantName" label="主申请人姓名" width="120"></el-table-column>
                        <el-table-column prop="idCard" label="身份证号" width="180">
                            <template slot-scope="scope">
                                {{ scope.row.idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2') }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="phone" label="联系电话" width="120"></el-table-column>
                        <el-table-column prop="community" label="小区名称" width="150"></el-table-column>
                        <el-table-column prop="building" label="幢" width="80"></el-table-column>
                        <el-table-column prop="unit" label="单元" width="80"></el-table-column>
                        <el-table-column prop="room" label="房间" width="80"></el-table-column>
                        <el-table-column prop="roomType" label="户型" width="100"></el-table-column>
                        <el-table-column prop="area" label="面积(㎡)" width="100"></el-table-column>
                        <el-table-column prop="price" label="月租金(元)" width="120">
                            <template slot-scope="scope">
                                <span>{{ scope.row.price.toLocaleString() }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="rentDate" label="配租时间" width="120"></el-table-column>
                        <el-table-column label="操作" width="100" fixed="right">
                            <template slot-scope="scope">
                                <el-button size="mini" @click="viewRentRecord(scope.row)">查看</el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <div style="text-align: center; margin-top: 20px;">
                        <el-pagination
                            @current-change="handleRentCurrentChange"
                            :current-page.sync="rentCurrentPage"
                            :page-size="rentPageSize"
                            layout="prev, pager, next, jumper"
                            :total="totalRentRecords">
                        </el-pagination>
                    </div>
                </el-card>

                <!-- 新增配租对话框 -->
                <el-dialog
                    title="新增配租"
                    :visible.sync="showRentDialog"
                    width="60%"
                    @close="resetRentForm">
                    <el-form :model="rentForm" :rules="rentRules" ref="rentForm" label-width="120px">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="主申请人" prop="applicantId">
                                    <el-select v-model="rentForm.applicantId" placeholder="请选择主申请人" filterable style="width: 100%">
                                        <el-option
                                            v-for="applicant in applicants"
                                            :key="applicant.id"
                                            :label="`${applicant.name} (${applicant.idCard})`"
                                            :value="applicant.id">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="小区名称" prop="community">
                                    <el-select v-model="rentForm.community" placeholder="请选择小区" filterable style="width: 100%">
                                        <el-option
                                            v-for="community in communities"
                                            :key="community"
                                            :label="community"
                                            :value="community">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item label="幢" prop="building">
                                    <el-select v-model="rentForm.building" placeholder="请选择幢" filterable style="width: 100%">
                                        <el-option
                                            v-for="building in availableBuildings"
                                            :key="building"
                                            :label="building"
                                            :value="building">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="单元" prop="unit">
                                    <el-select v-model="rentForm.unit" placeholder="请选择单元" filterable style="width: 100%">
                                        <el-option
                                            v-for="unit in availableUnits"
                                            :key="unit"
                                            :label="unit"
                                            :value="unit">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="房间" prop="room">
                                    <el-select v-model="rentForm.room" placeholder="请选择房间" filterable style="width: 100%">
                                        <el-option
                                            v-for="room in availableRooms"
                                            :key="room"
                                            :label="room"
                                            :value="room">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-form-item label="配租时间" prop="rentDate">
                            <el-date-picker
                                v-model="rentForm.rentDate"
                                type="date"
                                placeholder="选择配租时间"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                                style="width: 200px;">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="备注">
                            <el-input v-model="rentForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息"></el-input>
                        </el-form-item>
                    </el-form>
                    <div slot="footer">
                        <el-button @click="showRentDialog = false">取消</el-button>
                        <el-button type="primary" @click="saveRentRecord">确定</el-button>
                    </div>
                </el-dialog>

                <!-- 批量导入配租对话框 -->
                <el-dialog
                    title="批量导入配租记录"
                    :visible.sync="showRentImportDialog"
                    width="50%">
                    <div style="margin-bottom: 20px;">
                        <el-alert
                            title="导入说明"
                            type="info"
                            :closable="false"
                            show-icon>
                            <div slot="description">
                                <p>1. 请下载模板文件，按照模板格式填写数据</p>
                                <p>2. 支持Excel格式文件(.xlsx, .xls)</p>
                                <p>3. 单次最多导入1000条记录</p>
                                <p>4. 主申请人和房源信息必须已存在系统中</p>
                            </div>
                        </el-alert>
                    </div>

                    <el-upload
                        class="upload-demo"
                        drag
                        action="#"
                        :auto-upload="false"
                        :on-change="handleRentFileChange"
                        :file-list="rentFileList"
                        accept=".xlsx,.xls">
                        <i class="el-icon-upload"></i>
                        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                        <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件</div>
                    </el-upload>

                    <div slot="footer">
                        <el-button @click="downloadRentTemplate">下载模板</el-button>
                        <el-button @click="showRentImportDialog = false">取消</el-button>
                        <el-button type="primary" @click="importRentRecords">确定导入</el-button>
                    </div>
                </el-dialog>

                <!-- 调房对话框 -->
                <el-dialog
                    title="调房申请"
                    :visible.sync="showTransferDialog"
                    width="60%">
                    <el-form :model="transferForm" :rules="transferRules" ref="transferForm" label-width="120px">
                        <el-form-item label="选择租户" prop="rentRecordId">
                            <el-select v-model="transferForm.rentRecordId" placeholder="请选择要调房的租户" filterable style="width: 100%">
                                <el-option
                                    v-for="record in rentRecords"
                                    :key="record.id"
                                    :label="`${record.applicantName} - ${record.community} ${record.building}幢${record.unit}单元${record.room}室`"
                                    :value="record.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="调房原因" prop="reason">
                            <el-select v-model="transferForm.reason" placeholder="请选择调房原因" style="width: 100%">
                                <el-option label="房屋维修" value="房屋维修"></el-option>
                                <el-option label="家庭人口变化" value="家庭人口变化"></el-option>
                                <el-option label="工作调动" value="工作调动"></el-option>
                                <el-option label="其他原因" value="其他原因"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="目标房源" prop="targetHouseId">
                            <el-select v-model="transferForm.targetHouseId" placeholder="请选择目标房源" filterable style="width: 100%">
                                <el-option
                                    v-for="house in availableHouses"
                                    :key="house.id"
                                    :label="`${house.community} ${house.building}幢${house.unit}单元${house.room}室 - ${house.roomType} ${house.area}㎡`"
                                    :value="house.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="调房时间" prop="transferDate">
                            <el-date-picker
                                v-model="transferForm.transferDate"
                                type="date"
                                placeholder="选择调房时间"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                                style="width: 200px;">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="备注">
                            <el-input v-model="transferForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息"></el-input>
                        </el-form-item>
                    </el-form>
                    <div slot="footer">
                        <el-button @click="showTransferDialog = false">取消</el-button>
                        <el-button type="primary" @click="submitTransfer">确定调房</el-button>
                    </div>
                </el-dialog>

                <!-- 换房对话框 -->
                <el-dialog
                    title="换房申请"
                    :visible.sync="showExchangeDialog"
                    width="60%">
                    <el-form :model="exchangeForm" :rules="exchangeRules" ref="exchangeForm" label-width="120px">
                        <el-form-item label="租户A" prop="rentRecordIdA">
                            <el-select v-model="exchangeForm.rentRecordIdA" placeholder="请选择租户A" filterable style="width: 100%">
                                <el-option
                                    v-for="record in rentRecords"
                                    :key="record.id"
                                    :label="`${record.applicantName} - ${record.community} ${record.building}幢${record.unit}单元${record.room}室`"
                                    :value="record.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="租户B" prop="rentRecordIdB">
                            <el-select v-model="exchangeForm.rentRecordIdB" placeholder="请选择租户B" filterable style="width: 100%">
                                <el-option
                                    v-for="record in rentRecords"
                                    :key="record.id"
                                    :label="`${record.applicantName} - ${record.community} ${record.building}幢${record.unit}单元${record.room}室`"
                                    :value="record.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="换房原因" prop="reason">
                            <el-select v-model="exchangeForm.reason" placeholder="请选择换房原因" style="width: 100%">
                                <el-option label="双方协商" value="双方协商"></el-option>
                                <el-option label="家庭需求变化" value="家庭需求变化"></el-option>
                                <el-option label="工作便利" value="工作便利"></el-option>
                                <el-option label="其他原因" value="其他原因"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="换房时间" prop="exchangeDate">
                            <el-date-picker
                                v-model="exchangeForm.exchangeDate"
                                type="date"
                                placeholder="选择换房时间"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                                style="width: 200px;">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="备注">
                            <el-input v-model="exchangeForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息"></el-input>
                        </el-form-item>
                    </el-form>
                    <div slot="footer">
                        <el-button @click="showExchangeDialog = false">取消</el-button>
                        <el-button type="primary" @click="submitExchange">确定换房</el-button>
                    </div>
                </el-dialog>

                <!-- 主申请人变更对话框 -->
                <el-dialog
                    title="主申请人变更"
                    :visible.sync="showChangeApplicantDialog"
                    width="60%">
                    <el-form :model="changeApplicantForm" :rules="changeApplicantRules" ref="changeApplicantForm" label-width="120px">
                        <el-form-item label="选择配租记录" prop="rentRecordId">
                            <el-select v-model="changeApplicantForm.rentRecordId" placeholder="请选择要变更的配租记录" filterable style="width: 100%">
                                <el-option
                                    v-for="record in rentRecords"
                                    :key="record.id"
                                    :label="`${record.applicantName} - ${record.community} ${record.building}幢${record.unit}单元${record.room}室`"
                                    :value="record.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="新主申请人" prop="newApplicantId">
                            <el-select v-model="changeApplicantForm.newApplicantId" placeholder="请选择新的主申请人" filterable style="width: 100%">
                                <el-option
                                    v-for="applicant in applicants"
                                    :key="applicant.id"
                                    :label="`${applicant.name} (${applicant.idCard})`"
                                    :value="applicant.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="变更原因" prop="reason">
                            <el-select v-model="changeApplicantForm.reason" placeholder="请选择变更原因" style="width: 100%">
                                <el-option label="原申请人去世" value="原申请人去世"></el-option>
                                <el-option label="原申请人迁出" value="原申请人迁出"></el-option>
                                <el-option label="家庭结构变化" value="家庭结构变化"></el-option>
                                <el-option label="其他原因" value="其他原因"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="变更时间" prop="changeDate">
                            <el-date-picker
                                v-model="changeApplicantForm.changeDate"
                                type="date"
                                placeholder="选择变更时间"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                                style="width: 200px;">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="备注">
                            <el-input v-model="changeApplicantForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息"></el-input>
                        </el-form-item>
                    </el-form>
                    <div slot="footer">
                        <el-button @click="showChangeApplicantDialog = false">取消</el-button>
                        <el-button type="primary" @click="submitChangeApplicant">确定变更</el-button>
                    </div>
                </el-dialog>

                <!-- 查看申请详情对话框 -->
                <el-dialog title="申请详情" :visible.sync="showViewApplicationDialog" width="60%">
                    <div v-if="currentApplication">
                        <el-descriptions title="申请人信息" :column="2" border>
                            <el-descriptions-item label="申请编号">{{ currentApplication.id }}</el-descriptions-item>
                            <el-descriptions-item label="申请人姓名">{{ currentApplication.applicantName }}</el-descriptions-item>
                            <el-descriptions-item label="身份证号">{{ currentApplication.idCard }}</el-descriptions-item>
                            <el-descriptions-item label="联系电话">{{ currentApplication.phone }}</el-descriptions-item>
                            <el-descriptions-item label="申请房源">{{ currentApplication.houseAddress }}</el-descriptions-item>
                            <el-descriptions-item label="户型">{{ currentApplication.roomType }}</el-descriptions-item>
                            <el-descriptions-item label="申请时间">{{ currentApplication.applyDate }}</el-descriptions-item>
                            <el-descriptions-item label="申请状态">
                                <el-tag :type="getStatusType(currentApplication.status)">{{ currentApplication.status }}</el-tag>
                            </el-descriptions-item>
                        </el-descriptions>

                        <el-divider content-position="left">审核进度</el-divider>
                        <el-steps :active="getApplicationStep(currentApplication.status)" finish-status="success">
                            <el-step title="提交申请" description="申请已提交"></el-step>
                            <el-step title="资格审核" description="审核申请资格"></el-step>
                            <el-step title="房源分配" description="分配合适房源"></el-step>
                            <el-step title="完成配租" description="配租完成"></el-step>
                        </el-steps>
                    </div>

                    <div slot="footer">
                        <el-button @click="showViewApplicationDialog = false">关闭</el-button>
                    </div>
                </el-dialog>

                <!-- 编辑申请对话框 -->
                <el-dialog title="编辑申请" :visible.sync="showEditApplicationDialog" width="60%">
                    <el-form :model="editApplicationForm" label-width="100px" ref="editApplicationForm" v-if="currentApplication">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="申请人姓名" required>
                                    <el-input v-model="editApplicationForm.applicantName" placeholder="请输入姓名"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="身份证号" required>
                                    <el-input v-model="editApplicationForm.idCard" placeholder="请输入身份证号"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="联系电话" required>
                                    <el-input v-model="editApplicationForm.phone" placeholder="请输入联系电话"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="户型需求">
                                    <el-select v-model="editApplicationForm.roomType" placeholder="请选择户型" style="width: 100%;">
                                        <el-option label="一室一厅" value="一室一厅"></el-option>
                                        <el-option label="两室一厅" value="两室一厅"></el-option>
                                        <el-option label="三室一厅" value="三室一厅"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-form-item label="申请房源">
                            <el-input v-model="editApplicationForm.houseAddress" placeholder="请输入申请房源地址"></el-input>
                        </el-form-item>
                    </el-form>

                    <div slot="footer">
                        <el-button @click="showEditApplicationDialog = false">取消</el-button>
                        <el-button type="primary" @click="updateApplication">保存修改</el-button>
                    </div>
                </el-dialog>
            </div>
            
            <!-- 合同管理内容 -->
            <div v-if="activeTab === 'contract'" class="tab-content">
                
                <div v-if="!showContractDetail && !showSignContract">
                        <!-- 筛选条件 -->
                        <el-card class="filter-section">
                            <div slot="header">
                                <span>筛选条件</span>
                                <el-button style="float: right; padding: 3px 0" type="text" @click="resetContractFilter">重置</el-button>
                            </div>
                            
                            <el-form :inline="true" :model="contractFilterForm" size="small">
                                <el-form-item label="合同状态">
                                    <el-select v-model="contractFilterForm.status" placeholder="选择状态">
                                        <el-option label="全部状态" value=""></el-option>
                                        <el-option label="待签约" value="待签约"></el-option>
                                        <el-option label="执行中" value="执行中"></el-option>
                                        <el-option label="已到期" value="已到期"></el-option>
                                        <el-option label="已终止" value="已终止"></el-option>
                                    </el-select>
                                </el-form-item>
                                
                                <el-form-item label="签约时间">
                                    <el-date-picker
                                        v-model="contractFilterForm.dateRange"
                                        type="daterange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期">
                                    </el-date-picker>
                                </el-form-item>
                                
                                <el-form-item label="承租人">
                                    <el-input v-model="contractFilterForm.tenant" placeholder="承租人姓名"></el-input>
                                </el-form-item>
                                
                                <el-form-item>
                                    <el-button type="primary" @click="filterContracts">筛选</el-button>
                                </el-form-item>
                            </el-form>
                        </el-card>
                        
                        <!-- 合同列表表格 -->
                        <div class="table-container">
                            <el-table
                                :data="contracts"
                                style="width: 100%"
                                border>
                            <el-table-column prop="id" label="合同编号" width="120"></el-table-column>
                            <el-table-column prop="houseTitle" label="房源名称" min-width="200"></el-table-column>
                            <el-table-column prop="tenant" label="承租人" width="100"></el-table-column>
                            <el-table-column prop="startDate" label="开始日期" width="120"></el-table-column>
                            <el-table-column prop="endDate" label="结束日期" width="120"></el-table-column>
                            <el-table-column prop="rent" label="月租金(元)" width="120"></el-table-column>
                            <el-table-column prop="status" label="状态" width="100">
                                <template slot-scope="scope">
                                    <el-tag :type="getContractStatusType(scope.row.status)">
                                        {{ scope.row.status }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="160">
                                <template slot-scope="scope">
                                    <div class="action-buttons">
                                        <el-button size="mini" @click="viewContract(scope.row)">查看</el-button>
                                        <el-button
                                            size="mini"
                                            type="primary"
                                            @click="signContract(scope.row)"
                                            v-if="scope.row.status === '待签约'">
                                            签约
                                        </el-button>
                                        <el-button
                                            size="mini"
                                            type="danger"
                                            @click="terminateContract(scope.row)"
                                            v-if="scope.row.status === '执行中'">
                                            终止
                                        </el-button>
                                    </div>
                                </template>
                            </el-table-column>
                            </el-table>
                        </div>

                        <!-- 分页控件 -->
                        <div style="text-align: center; margin-top: 20px;">
                            <el-pagination
                                @current-change="handleContractCurrentChange"
                                :current-page.sync="contractCurrentPage"
                                :page-size="contractPageSize"
                                layout="prev, pager, next, jumper"
                                :total="totalContracts">
                            </el-pagination>
                        </div>
                </div>

                <!-- 合同详情页面 -->
                <div v-if="showContractDetail && !showSignContract">
                    <el-card>
                        <div slot="header" style="display: flex; justify-content: space-between; align-items: center;">
                            <span>合同详情</span>
                            <el-button size="small" @click="backToContractList">
                                <i class="el-icon-back"></i> 返回列表
                            </el-button>
                        </div>

                        <div v-if="currentContract">
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-descriptions title="基本信息" :column="1" border>
                                        <el-descriptions-item label="合同编号">{{ currentContract.id }}</el-descriptions-item>
                                        <el-descriptions-item label="房源名称">{{ currentContract.houseTitle }}</el-descriptions-item>
                                        <el-descriptions-item label="承租人">{{ currentContract.tenant }}</el-descriptions-item>
                                        <el-descriptions-item label="联系电话">{{ currentContract.phone || '138****5678' }}</el-descriptions-item>
                                        <el-descriptions-item label="身份证号">{{ currentContract.idCard || '320***********1234' }}</el-descriptions-item>
                                    </el-descriptions>
                                </el-col>
                                <el-col :span="12">
                                    <el-descriptions title="合同条款" :column="1" border>
                                        <el-descriptions-item label="租赁期限">{{ currentContract.startDate }} 至 {{ currentContract.endDate }}</el-descriptions-item>
                                        <el-descriptions-item label="月租金">¥{{ currentContract.rent }}</el-descriptions-item>
                                        <el-descriptions-item label="押金">¥{{ currentContract.deposit || (currentContract.rent * 2) }}</el-descriptions-item>
                                        <el-descriptions-item label="合同状态">
                                            <el-tag :type="getContractStatusType(currentContract.status)">{{ currentContract.status }}</el-tag>
                                        </el-descriptions-item>
                                        <el-descriptions-item label="签约时间">{{ currentContract.signTime || '待签约' }}</el-descriptions-item>
                                    </el-descriptions>
                                </el-col>
                            </el-row>

                            <el-divider>合同条款详情</el-divider>
                            <div style="background: #f5f7fa; padding: 20px; border-radius: 4px;">
                                <h4>租赁合同主要条款：</h4>
                                <p>1. 租赁物业：{{ currentContract.houseTitle }}</p>
                                <p>2. 租赁期限：自{{ currentContract.startDate }}起至{{ currentContract.endDate }}止</p>
                                <p>3. 租金标准：每月人民币{{ currentContract.rent }}元整</p>
                                <p>4. 押金：人民币{{ currentContract.deposit || (currentContract.rent * 2) }}元整</p>
                                <p>5. 付款方式：按月支付，每月{{ currentContract.paymentDay || '1' }}日前支付当月租金</p>
                                <p>6. 其他约定：遵守相关法律法规，爱护房屋设施，按时缴纳租金</p>
                            </div>

                            <div style="text-align: center; margin-top: 30px;" v-if="currentContract.status === '待签约'">
                                <el-button type="primary" size="large" @click="signContract(currentContract)">
                                    <i class="el-icon-edit"></i> 立即签约
                                </el-button>
                            </div>
                        </div>
                    </el-card>
                </div>

                <!-- 电子签约页面 -->
                <div v-if="showSignContract">
                    <el-card>
                        <div slot="header" style="display: flex; justify-content: space-between; align-items: center;">
                            <span>电子签约</span>
                            <el-button size="small" @click="backToContractList">
                                <i class="el-icon-back"></i> 返回列表
                            </el-button>
                        </div>

                        <div v-if="currentContract">
                            <el-steps :active="signStep" finish-status="success" style="margin-bottom: 30px;">
                                <el-step title="确认合同信息"></el-step>
                                <el-step title="电子签名"></el-step>
                                <el-step title="签约完成"></el-step>
                            </el-steps>

                            <!-- 步骤1：确认合同信息 -->
                            <div v-if="signStep === 0">
                                <h3>请确认以下合同信息：</h3>
                                <el-table :data="[currentContract]" border style="margin: 20px 0;">
                                    <el-table-column prop="id" label="合同编号" width="120"></el-table-column>
                                    <el-table-column prop="houseTitle" label="房源名称" min-width="200"></el-table-column>
                                    <el-table-column prop="tenant" label="承租人" width="100"></el-table-column>
                                    <el-table-column prop="startDate" label="开始日期" width="120"></el-table-column>
                                    <el-table-column prop="endDate" label="结束日期" width="120"></el-table-column>
                                    <el-table-column prop="rent" label="月租金(元)" width="120"></el-table-column>
                                </el-table>

                                <div style="text-align: center; margin-top: 30px;">
                                    <el-button @click="backToContractList">取消</el-button>
                                    <el-button type="primary" @click="nextSignStep">确认信息，下一步</el-button>
                                </div>
                            </div>

                            <!-- 步骤2：电子签名 -->
                            <div v-if="signStep === 1">
                                <h3>电子签名</h3>
                                <div style="border: 2px dashed #ddd; padding: 40px; text-align: center; margin: 20px 0; background: #fafafa;">
                                    <i class="el-icon-edit" style="font-size: 48px; color: #409EFF; margin-bottom: 20px;"></i>
                                    <p>请在此区域进行电子签名</p>
                                    <p style="color: #999; font-size: 14px;">点击下方按钮完成签名</p>
                                </div>

                                <div style="text-align: center; margin-top: 30px;">
                                    <el-button @click="prevSignStep">上一步</el-button>
                                    <el-button type="primary" @click="nextSignStep">完成签名</el-button>
                                </div>
                            </div>

                            <!-- 步骤3：签约完成 -->
                            <div v-if="signStep === 2">
                                <div style="text-align: center; padding: 40px;">
                                    <i class="el-icon-success" style="font-size: 64px; color: #67C23A; margin-bottom: 20px;"></i>
                                    <h2 style="color: #67C23A; margin-bottom: 20px;">签约成功！</h2>
                                    <p>合同编号：{{ currentContract.id }}</p>
                                    <p>签约时间：{{ new Date().toLocaleString() }}</p>

                                    <div style="margin-top: 30px;">
                                        <el-button type="primary" @click="downloadContract">下载合同</el-button>
                                        <el-button @click="backToContractList">返回列表</el-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-card>
                </div>

                <!-- 添加共同居住人对话框 -->
                <el-dialog title="添加共同居住人" :visible.sync="cohabitantDialogVisible" width="50%">
                    <el-form :model="cohabitantForm" :rules="cohabitantFormRules" ref="cohabitantForm" label-width="100px">
                        <el-form-item label="姓名" prop="name">
                            <el-input v-model="cohabitantForm.name" placeholder="请输入姓名"></el-input>
                        </el-form-item>
                        
                        <el-form-item label="与承租人关系" prop="relation">
                            <el-select v-model="cohabitantForm.relation" placeholder="请选择关系" style="width: 100%;">
                                <el-option label="配偶" value="配偶"></el-option>
                                <el-option label="子女" value="子女"></el-option>
                                <el-option label="父母" value="父母"></el-option>
                                <el-option label="其他" value="其他"></el-option>
                            </el-select>
                        </el-form-item>
                        
                        <el-form-item label="身份证号" prop="idCard">
                            <el-input v-model="cohabitantForm.idCard" placeholder="请输入身份证号"></el-input>
                        </el-form-item>
                        
                        <el-form-item label="联系电话" prop="phone">
                            <el-input v-model="cohabitantForm.phone" placeholder="请输入联系电话"></el-input>
                        </el-form-item>
                        
                        <el-form-item label="身份证照片">
                            <el-upload
                                action="#"
                                list-type="picture-card"
                                :auto-upload="false">
                                <i class="el-icon-plus"></i>
                            </el-upload>
                            <div style="color: #909399; font-size: 12px;">
                                请上传身份证正反面照片，用于人脸识别授权
                            </div>
                        </el-form-item>
                    </el-form>
                    
                    <div slot="footer">
                        <el-button @click="cohabitantDialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="submitCohabitant">确认</el-button>
                    </div>
                </el-dialog>
                
                <!-- 合同详情对话框 -->
                <el-dialog title="合同详情" :visible.sync="contractDetailVisible" width="70%">
                    <div v-if="currentContract">
                        <el-descriptions title="基本信息" :column="2" border>
                            <el-descriptions-item label="合同编号">{{ currentContract.id }}</el-descriptions-item>
                            <el-descriptions-item label="签约日期">{{ currentContract.signDate }}</el-descriptions-item>
                            <el-descriptions-item label="承租人">{{ currentContract.tenant }}</el-descriptions-item>
                            <el-descriptions-item label="身份证号">{{ currentContract.idCard && (currentContract.idCard.substring(0, 6) + '********' + currentContract.idCard.substring(14)) }}</el-descriptions-item>
                            <el-descriptions-item label="联系电话">{{ currentContract.phone && (currentContract.phone.substring(0, 3) + '****' + currentContract.phone.substring(7)) }}</el-descriptions-item>
                            <el-descriptions-item label="合同状态">
                                <el-tag :type="getContractStatusType(currentContract.status)">
                                    {{ currentContract.status }}
                                </el-tag>
                            </el-descriptions-item>
                        </el-descriptions>
                        
                        <el-divider content-position="left">房源信息</el-divider>
                        
                        <el-descriptions :column="2" border>
                            <el-descriptions-item label="房源名称">{{ currentContract.houseTitle }}</el-descriptions-item>
                            <el-descriptions-item label="房源地址">{{ currentContract.address }}</el-descriptions-item>
                            <el-descriptions-item label="户型">{{ currentContract.roomType }}</el-descriptions-item>
                            <el-descriptions-item label="面积">{{ currentContract.area }}㎡</el-descriptions-item>
                            <el-descriptions-item label="月租金">{{ currentContract.rent }}元</el-descriptions-item>
                            <el-descriptions-item label="租期">{{ currentContract.startDate }} 至 {{ currentContract.endDate }}</el-descriptions-item>
                        </el-descriptions>
                    </div>
                </el-dialog>
                
                <!-- 终止合同对话框 -->
                <el-dialog title="终止合同" :visible.sync="terminateDialogVisible" width="50%">
                    <el-form :model="terminateForm" :rules="terminateFormRules" ref="terminateForm" label-width="100px">
                        <el-form-item label="终止原因" prop="reason">
                            <el-select v-model="terminateForm.reason" placeholder="请选择终止原因" style="width: 100%;">
                                <el-option label="承租人申请终止" value="承租人申请终止"></el-option>
                                <el-option label="违反合同规定" value="违反合同规定"></el-option>
                                <el-option label="不再符合保障条件" value="不再符合保障条件"></el-option>
                                <el-option label="其他原因" value="其他原因"></el-option>
                            </el-select>
                        </el-form-item>
                        
                        <el-form-item label="具体说明" prop="description">
                            <el-input type="textarea" v-model="terminateForm.description" :rows="4" placeholder="请输入具体说明"></el-input>
                        </el-form-item>
                        
                        <el-form-item label="终止日期" prop="date">
                            <el-date-picker
                                v-model="terminateForm.date"
                                type="date"
                                placeholder="选择日期"
                                style="width: 100%;">
                            </el-date-picker>
                        </el-form-item>
                        
                        <el-form-item label="上传证明材料">
                            <el-upload
                                action="#"
                                list-type="picture-card"
                                :auto-upload="false">
                                <i class="el-icon-plus"></i>
                            </el-upload>
                        </el-form-item>
                    </el-form>
                    
                    <div slot="footer">
                        <el-button @click="terminateDialogVisible = false">取消</el-button>
                        <el-button type="danger" @click="confirmTerminate">确认终止</el-button>
                    </div>
                </el-dialog>
            </div>
            
            <!-- 租金管理内容 -->
            <div v-if="activeTab === 'payment'" class="tab-content">
                <!-- 搜索筛选区域 -->
                <el-card class="filter-section">
                    <div slot="header">
                        <span>租金管理</span>
                        <div style="float: right;">
                            <el-button type="primary" size="small" @click="showBillDialog = true">
                                <i class="el-icon-plus"></i> 生成账单
                            </el-button>
                            <el-button type="success" size="small" @click="showBatchBillDialog = true">
                                <i class="el-icon-document-add"></i> 批量生成
                            </el-button>
                            <el-button type="warning" size="small" @click="exportBills">
                                <i class="el-icon-download"></i> 导出账单
                            </el-button>
                        </div>
                    </div>

                    <el-form :inline="true" :model="paymentFilterForm" size="small">
                        <el-form-item label="承租人姓名">
                            <el-input v-model="paymentFilterForm.tenant" placeholder="请输入承租人姓名" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="房源地址">
                            <el-input v-model="paymentFilterForm.address" placeholder="请输入房源地址" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="账单状态">
                            <el-select v-model="paymentFilterForm.status" placeholder="选择状态" clearable>
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="待支付" value="待支付"></el-option>
                                <el-option label="已支付" value="已支付"></el-option>
                                <el-option label="已逾期" value="已逾期"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="账单月份">
                            <el-date-picker
                                v-model="paymentFilterForm.month"
                                type="month"
                                placeholder="选择月份"
                                format="yyyy-MM"
                                value-format="yyyy-MM">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="filterPayments">
                                <i class="el-icon-search"></i> 搜索
                            </el-button>
                            <el-button @click="resetPaymentFilter">
                                <i class="el-icon-refresh-left"></i> 重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
                        
                <!-- 账单列表 -->
                <el-card style="margin-top: 20px;">
                    <el-table
                        :data="bills"
                        style="width: 100%"
                        border
                        stripe>
                        <el-table-column prop="id" label="账单编号" width="120"></el-table-column>
                        <el-table-column prop="tenant" label="承租人" width="100"></el-table-column>
                        <el-table-column prop="community" label="小区名称" width="150"></el-table-column>
                        <el-table-column prop="building" label="幢" width="80"></el-table-column>
                        <el-table-column prop="unit" label="单元" width="80"></el-table-column>
                        <el-table-column prop="room" label="房间" width="80"></el-table-column>
                        <el-table-column prop="month" label="账单月份" width="120"></el-table-column>
                        <el-table-column prop="rentAmount" label="租金(元)" width="100">
                            <template slot-scope="scope">
                                <span>{{ scope.row.rentAmount.toLocaleString() }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="utilityAmount" label="水电费(元)" width="100">
                            <template slot-scope="scope">
                                <span>{{ scope.row.utilityAmount.toLocaleString() }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="totalAmount" label="总金额(元)" width="120">
                            <template slot-scope="scope">
                                <span style="font-weight: bold; color: #E6A23C;">{{ scope.row.totalAmount.toLocaleString() }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="dueDate" label="到期日" width="120"></el-table-column>
                        <el-table-column prop="status" label="状态" width="100">
                            <template slot-scope="scope">
                                <el-tag :type="getBillStatusType(scope.row.status)">
                                    {{ scope.row.status }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="200" fixed="right">
                            <template slot-scope="scope">
                                <div style="display: flex; flex-wrap: wrap; gap: 4px;">
                                    <el-button
                                        size="mini"
                                        @click="viewBill(scope.row)">
                                        查看
                                    </el-button>
                                    <el-button
                                        size="mini"
                                        type="primary"
                                        @click="payBill(scope.row)"
                                        v-if="scope.row.status === '待支付' || scope.row.status === '已逾期'">
                                        支付
                                    </el-button>
                                    <el-button
                                        size="mini"
                                        type="success"
                                        @click="downloadInvoice(scope.row)"
                                        v-if="scope.row.status === '已支付'">
                                        发票
                                    </el-button>
                                    <el-button
                                        size="mini"
                                        type="warning"
                                        @click="editBill(scope.row)"
                                        v-if="scope.row.status === '待支付'">
                                        编辑
                                    </el-button>
                                    <el-button
                                        size="mini"
                                        type="danger"
                                        @click="deleteBill(scope.row)"
                                        v-if="scope.row.status === '待支付'">
                                        删除
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <div style="text-align: center; margin-top: 20px;">
                        <el-pagination
                            @current-change="handleBillCurrentChange"
                            :current-page.sync="billCurrentPage"
                            :page-size="billPageSize"
                            layout="prev, pager, next, jumper"
                            :total="totalBills">
                        </el-pagination>
                    </div>
                </el-card>
                <!-- 生成账单对话框 -->
                <el-dialog
                    title="生成账单"
                    :visible.sync="showBillDialog"
                    width="60%"
                    @close="resetBillForm">
                    <el-form :model="billForm" :rules="billRules" ref="billForm" label-width="120px">
                        <el-form-item label="选择租户" prop="rentRecordId">
                            <el-select v-model="billForm.rentRecordId" placeholder="请选择租户" filterable style="width: 100%">
                                <el-option
                                    v-for="record in rentRecords"
                                    :key="record.id"
                                    :label="`${record.applicantName} - ${record.community} ${record.building}幢${record.unit}单元${record.room}室`"
                                    :value="record.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="账单月份" prop="month">
                            <el-date-picker
                                v-model="billForm.month"
                                type="month"
                                placeholder="选择账单月份"
                                format="yyyy-MM"
                                value-format="yyyy-MM"
                                style="width: 200px;">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="租金金额" prop="rentAmount">
                            <el-input-number v-model="billForm.rentAmount" :min="0" :step="50" style="width: 200px;"></el-input-number>
                            <span style="margin-left: 10px; color: #999;">元</span>
                        </el-form-item>
                        <el-form-item label="水电费" prop="utilityAmount">
                            <el-input-number v-model="billForm.utilityAmount" :min="0" :step="10" style="width: 200px;"></el-input-number>
                            <span style="margin-left: 10px; color: #999;">元</span>
                        </el-form-item>
                        <el-form-item label="到期日期" prop="dueDate">
                            <el-date-picker
                                v-model="billForm.dueDate"
                                type="date"
                                placeholder="选择到期日期"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                                style="width: 200px;">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="备注">
                            <el-input v-model="billForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息"></el-input>
                        </el-form-item>
                    </el-form>
                    <div slot="footer">
                        <el-button @click="showBillDialog = false">取消</el-button>
                        <el-button type="primary" @click="saveBill">确定生成</el-button>
                    </div>
                </el-dialog>

                <!-- 批量生成账单对话框 -->
                <el-dialog
                    title="批量生成账单"
                    :visible.sync="showBatchBillDialog"
                    width="50%">
                    <el-form :model="batchBillForm" :rules="batchBillRules" ref="batchBillForm" label-width="120px">
                        <el-form-item label="账单月份" prop="month">
                            <el-date-picker
                                v-model="batchBillForm.month"
                                type="month"
                                placeholder="选择账单月份"
                                format="yyyy-MM"
                                value-format="yyyy-MM"
                                style="width: 200px;">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="到期日期" prop="dueDate">
                            <el-date-picker
                                v-model="batchBillForm.dueDate"
                                type="date"
                                placeholder="选择到期日期"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                                style="width: 200px;">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="生成范围">
                            <el-radio-group v-model="batchBillForm.scope">
                                <el-radio label="all">所有租户</el-radio>
                                <el-radio label="community">按小区</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="选择小区" v-if="batchBillForm.scope === 'community'">
                            <el-select v-model="batchBillForm.community" placeholder="请选择小区" style="width: 100%">
                                <el-option
                                    v-for="community in communities"
                                    :key="community"
                                    :label="community"
                                    :value="community">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                    <div slot="footer">
                        <el-button @click="showBatchBillDialog = false">取消</el-button>
                        <el-button type="primary" @click="batchGenerateBills">确定生成</el-button>
                    </div>
                </el-dialog>
            </div>

                            
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <div style="display: flex; align-items: center;">
                                            <i class="el-icon-money" style="font-size: 40px; margin-right: 15px; color: #409EFF;"></i>
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold;">{{ ((paymentStats.paidBills / paymentStats.totalBills) * 100).toFixed(1) }}%</div>
                                                <div>按时缴费率</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <div style="display: flex; align-items: center;">
                                            <i class="el-icon-warning" style="font-size: 40px; margin-right: 15px; color: #E6A23C;"></i>
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold;">{{ paymentStats.overdueBills }}</div>
                                                <div>逾期账单数</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <div style="display: flex; align-items: center;">
                                            <i class="el-icon-wallet" style="font-size: 40px; margin-right: 15px; color: #67C23A;"></i>
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold;">¥{{ paymentStats.paidAmount.toLocaleString() }}</div>
                                                <div>已收金额</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <div style="display: flex; align-items: center;">
                                            <i class="el-icon-chat-dot-square" style="font-size: 40px; margin-right: 15px; color: #F56C6C;"></i>
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold;">¥{{ paymentStats.unpaidAmount.toLocaleString() }}</div>
                                                <div>待收金额</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>
                            </el-row>
                            
                            <div style="margin-top: 20px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                    <h3>月度缴费情况</h3>
                                    <el-select v-model="paymentStatsYear" placeholder="选择年份" size="small" style="width: 120px;">
                                        <el-option label="2023年" value="2023"></el-option>
                                        <el-option label="2022年" value="2022"></el-option>
                                    </el-select>
                                </div>
                                
                                <div style="height: 300px; background-color: white; border: 1px solid #e6e6e6; border-radius: 4px; padding: 20px;">
                                    <div style="display: flex; align-items: end; height: 100%; justify-content: space-around;">
                                        <div v-for="(month, index) in monthlyStats" :key="index" style="display: flex; flex-direction: column; align-items: center;">
                                            <div :style="{
                                                width: '30px',
                                                height: (month.amount / 50000 * 200) + 'px',
                                                backgroundColor: month.amount > 40000 ? '#67C23A' : month.amount > 30000 ? '#409EFF' : '#E6A23C',
                                                marginBottom: '10px',
                                                borderRadius: '2px'
                                            }"></div>
                                            <div style="font-size: 12px; color: #666;">{{ month.month }}</div>
                                            <div style="font-size: 10px; color: #999;">¥{{ (month.amount / 1000).toFixed(0) }}k</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div style="margin-top: 30px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                    <h3>支付方式占比</h3>
                                </div>
                                
                                <div style="height: 300px; background-color: white; border: 1px solid #e6e6e6; border-radius: 4px; padding: 20px;">
                                    <div style="display: flex; align-items: center; height: 100%;">
                                        <div style="flex: 1; display: flex; justify-content: center;">
                                            <div style="width: 200px; height: 200px; border-radius: 50%; background: conic-gradient(#409EFF 0deg 144deg, #67C23A 144deg 216deg, #E6A23C 216deg 288deg, #F56C6C 288deg 360deg); position: relative;">
                                                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 100px; height: 100px; background: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold;">
                                                    支付方式
                                                </div>
                                            </div>
                                        </div>
                                        <div style="flex: 1; padding-left: 20px;">
                                            <div style="margin-bottom: 15px;">
                                                <div style="display: flex; align-items: center; margin-bottom: 5px;">
                                                    <div style="width: 12px; height: 12px; background: #409EFF; margin-right: 8px;"></div>
                                                    <span>微信支付 (40%)</span>
                                                </div>
                                                <div style="display: flex; align-items: center; margin-bottom: 5px;">
                                                    <div style="width: 12px; height: 12px; background: #67C23A; margin-right: 8px;"></div>
                                                    <span>支付宝 (20%)</span>
                                                </div>
                                                <div style="display: flex; align-items: center; margin-bottom: 5px;">
                                                    <div style="width: 12px; height: 12px; background: #E6A23C; margin-right: 8px;"></div>
                                                    <span>银行转账 (20%)</span>
                                                </div>
                                                <div style="display: flex; align-items: center;">
                                                    <div style="width: 12px; height: 12px; background: #F56C6C; margin-right: 8px;"></div>
                                                    <span>线下代缴 (20%)</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-card>
                    </el-tab-pane>
                </el-tabs>

                <!-- 支付账单页面 -->
                <div v-if="showBillPayment">
                    <el-card>
                        <div slot="header" style="display: flex; justify-content: space-between; align-items: center;">
                            <span>{{ currentBill && currentBill.status === '已支付' ? '账单详情' : '支付账单' }}</span>
                            <el-button size="small" @click="backToBillList">
                                <i class="el-icon-back"></i> 返回列表
                            </el-button>
                        </div>

                        <div v-if="currentBill">
                            <!-- 已支付账单详情 -->
                            <div v-if="currentBill.status === '已支付'">
                                <el-descriptions title="账单详情" :column="2" border style="margin: 20px 0;">
                                    <el-descriptions-item label="账单编号">{{ currentBill.id }}</el-descriptions-item>
                                    <el-descriptions-item label="账单状态">
                                        <el-tag type="success">{{ currentBill.status }}</el-tag>
                                    </el-descriptions-item>
                                    <el-descriptions-item label="房源名称">{{ currentBill.houseTitle }}</el-descriptions-item>
                                    <el-descriptions-item label="房源地址">{{ currentBill.address }}</el-descriptions-item>
                                    <el-descriptions-item label="承租人">{{ currentBill.tenant }}</el-descriptions-item>
                                    <el-descriptions-item label="联系电话">{{ currentBill.phone }}</el-descriptions-item>
                                    <el-descriptions-item label="账单月份">{{ currentBill.month }}</el-descriptions-item>
                                    <el-descriptions-item label="到期日期">{{ currentBill.dueDate }}</el-descriptions-item>
                                </el-descriptions>

                                <div style="margin-top: 20px;">
                                    <h3>费用明细</h3>
                                    <el-table
                                        :data="currentBill.items"
                                        style="width: 100%"
                                        border>
                                        <el-table-column prop="name" label="费用名称" width="180"></el-table-column>
                                        <el-table-column prop="amount" label="金额(元)" width="100">
                                            <template slot-scope="scope">
                                                <span>{{ scope.row.amount.toFixed(2) }}</span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="description" label="说明"></el-table-column>
                                    </el-table>

                                    <div style="margin-top: 20px; text-align: right;">
                                        <div style="font-size: 18px; font-weight: bold;">
                                            总计：¥{{ currentBill.totalAmount.toFixed(2) }}
                                        </div>
                                    </div>
                                </div>

                                <div style="margin-top: 30px;">
                                    <h3>支付信息</h3>
                                    <el-descriptions :column="2" border>
                                        <el-descriptions-item label="支付时间">{{ currentBill.paymentTime }}</el-descriptions-item>
                                        <el-descriptions-item label="支付方式">{{ getPaymentMethodName(currentBill.paymentMethod) }}</el-descriptions-item>
                                        <el-descriptions-item label="交易流水号">{{ currentBill.transactionId }}</el-descriptions-item>
                                    </el-descriptions>

                                    <div style="margin-top: 20px; text-align: center;">
                                        <el-button type="success" @click="downloadInvoice(currentBill)">下载电子发票</el-button>
                                    </div>
                                </div>
                            </div>

                            <!-- 待支付账单的支付流程 -->
                            <div v-else>
                                <el-steps :active="paymentStep" finish-status="success" style="margin-bottom: 30px;">
                                    <el-step title="确认账单信息"></el-step>
                                    <el-step title="选择支付方式"></el-step>
                                    <el-step title="支付完成"></el-step>
                                </el-steps>

                                <!-- 步骤1：确认账单信息 -->
                                <div v-if="paymentStep === 0">
                                <h3>请确认以下账单信息：</h3>
                                <el-descriptions title="账单详情" :column="2" border style="margin: 20px 0;">
                                    <el-descriptions-item label="账单编号">{{ currentBill.id }}</el-descriptions-item>
                                    <el-descriptions-item label="房源名称">{{ currentBill.houseTitle }}</el-descriptions-item>
                                    <el-descriptions-item label="承租人">{{ currentBill.tenant }}</el-descriptions-item>
                                    <el-descriptions-item label="账单月份">{{ currentBill.month }}</el-descriptions-item>
                                    <el-descriptions-item label="应付金额">¥{{ currentBill.amount }}</el-descriptions-item>
                                    <el-descriptions-item label="到期日期">{{ currentBill.dueDate }}</el-descriptions-item>
                                </el-descriptions>

                                <div style="background: #f5f7fa; padding: 20px; border-radius: 4px; margin: 20px 0;">
                                    <h4>费用明细：</h4>
                                    <p>租金：¥{{ currentBill.rent || currentBill.amount }}</p>
                                    <p>物业费：¥0</p>
                                    <p>水电费：¥0</p>
                                    <p style="font-weight: bold; color: #E6A23C;">总计：¥{{ currentBill.amount }}</p>
                                </div>

                                <div style="text-align: center; margin-top: 30px;">
                                    <el-button @click="backToBillList">取消</el-button>
                                    <el-button type="primary" @click="nextPaymentStep">确认信息，下一步</el-button>
                                </div>
                            </div>

                            <!-- 步骤2：选择支付方式 -->
                            <div v-if="paymentStep === 1">
                                <h3>选择支付方式</h3>
                                <div style="margin: 20px 0;">
                                    <el-radio-group v-model="paymentMethod" size="large">
                                        <el-radio-button label="wechat">
                                            <i class="el-icon-chat-dot-round" style="color: #07C160;"></i> 微信支付
                                        </el-radio-button>
                                        <el-radio-button label="alipay">
                                            <i class="el-icon-wallet" style="color: #1677FF;"></i> 支付宝
                                        </el-radio-button>
                                        <el-radio-button label="bank">
                                            <i class="el-icon-bank-card" style="color: #F56C6C;"></i> 银行卡
                                        </el-radio-button>
                                    </el-radio-group>
                                </div>

                                <div style="border: 1px solid #ddd; padding: 30px; text-align: center; margin: 20px 0; background: #fafafa;">
                                    <div v-if="paymentMethod === 'wechat'">
                                        <i class="el-icon-qrcode" style="font-size: 64px; color: #07C160; margin-bottom: 20px;"></i>
                                        <p>请使用微信扫描二维码支付</p>
                                        <p style="color: #999;">支付金额：¥{{ currentBill.amount }}</p>
                                    </div>
                                    <div v-if="paymentMethod === 'alipay'">
                                        <i class="el-icon-qrcode" style="font-size: 64px; color: #1677FF; margin-bottom: 20px;"></i>
                                        <p>请使用支付宝扫描二维码支付</p>
                                        <p style="color: #999;">支付金额：¥{{ currentBill.amount }}</p>
                                    </div>
                                    <div v-if="paymentMethod === 'bank'">
                                        <i class="el-icon-bank-card" style="font-size: 64px; color: #F56C6C; margin-bottom: 20px;"></i>
                                        <p>银行卡支付</p>
                                        <p style="color: #999;">支付金额：¥{{ currentBill.amount }}</p>
                                    </div>
                                </div>

                                <div style="text-align: center; margin-top: 30px;">
                                    <el-button @click="prevPaymentStep">上一步</el-button>
                                    <el-button type="primary" @click="nextPaymentStep">确认支付</el-button>
                                </div>
                            </div>

                            <!-- 步骤3：支付完成 -->
                            <div v-if="paymentStep === 2">
                                <div style="text-align: center; padding: 40px;">
                                    <i class="el-icon-success" style="font-size: 64px; color: #67C23A; margin-bottom: 20px;"></i>
                                    <h2 style="color: #67C23A; margin-bottom: 20px;">支付成功！</h2>
                                    <p>账单编号：{{ currentBill.id }}</p>
                                    <p>支付金额：¥{{ currentBill.amount }}</p>
                                    <p>支付时间：{{ new Date().toLocaleString() }}</p>

                                    <div style="margin-top: 30px;">
                                        <el-button type="primary" @click="downloadReceipt">下载收据</el-button>
                                        <el-button @click="backToBillList">返回列表</el-button>
                                    </div>
                                </div>
                            </div>
                            </div>
                        </div>
                    </el-card>
                </div>

                <!-- 账单详情对话框 -->
                <el-dialog title="账单详情" :visible.sync="billDetailVisible" width="70%">
                    <div v-if="currentBill">
                        <el-descriptions title="基本信息" :column="2" border>
                            <el-descriptions-item label="账单编号">{{ currentBill.id }}</el-descriptions-item>
                            <el-descriptions-item label="账单状态">
                                <el-tag :type="getBillStatusType(currentBill.status)">
                                    {{ currentBill.status }}
                                </el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label="房源名称">{{ currentBill.houseTitle }}</el-descriptions-item>
                            <el-descriptions-item label="账单月份">{{ currentBill.month }}</el-descriptions-item>
                            <el-descriptions-item label="承租人">{{ currentBill.tenant }}</el-descriptions-item>
                            <el-descriptions-item label="到期日">{{ currentBill.dueDate }}</el-descriptions-item>
                        </el-descriptions>
                        
                        <el-divider content-position="left">费用明细</el-divider>
                        
                        <el-table
                            :data="currentBill.items"
                            style="width: 100%"
                            border>
                            <el-table-column prop="name" label="费用名称" width="180"></el-table-column>
                            <el-table-column prop="amount" label="金额(元)" width="100">
                                <template slot-scope="scope">
                                    <span>{{ scope.row.amount.toFixed(2) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="description" label="说明"></el-table-column>
                        </el-table>
                        
                        <div style="margin-top: 20px; text-align: right;">
                            <div v-if="currentBill.status === '已逾期'" style="color: #f56c6c; margin-bottom: 10px;">
                                <i class="el-icon-warning"></i> 
                                已逾期{{ currentBill.overdueDays }}天，产生滞纳金{{ currentBill.lateFee.toFixed(2) }}元
                            </div>
                            <div style="font-size: 18px; font-weight: bold;">
                                总计：¥{{ (currentBill.totalAmount + (currentBill.lateFee || 0)).toFixed(2) }}
                            </div>
                        </div>
                        
                        <div v-if="currentBill.status === '已支付'" style="margin-top: 20px;">
                            <el-divider content-position="left">支付信息</el-divider>
                            
                            <el-descriptions :column="2" border>
                                <el-descriptions-item label="支付时间">{{ currentBill.paymentTime }}</el-descriptions-item>
                                <el-descriptions-item label="支付方式">{{ getPaymentMethodName(currentBill.paymentMethod) }}</el-descriptions-item>
                                <el-descriptions-item label="交易流水号">{{ currentBill.transactionId }}</el-descriptions-item>
                            </el-descriptions>
                        </div>
                    </div>
                </el-dialog>
            </div>
            
            <!-- 维修服务内容 -->
            <div v-if="activeTab === 'repair'" class="tab-content">
                <el-tabs v-model="repairActiveTab">
                    <el-tab-pane label="报修管理" name="list">
                        <!-- 筛选条件 -->
                        <el-card class="filter-section">
                            <div slot="header">
                                <span>报修工单管理</span>
                                <el-button type="primary" size="small" style="float: right;" @click="showRepairDialog = true">
                                    <i class="el-icon-plus"></i> 新增报修
                                </el-button>
                            </div>

                            <el-form :inline="true" size="small">
                                <el-form-item label="工单状态">
                                    <el-select v-model="repairSearch.status" placeholder="请选择状态" clearable style="width: 120px;">
                                        <el-option label="全部" value=""></el-option>
                                        <el-option label="待处理" value="待处理"></el-option>
                                        <el-option label="处理中" value="处理中"></el-option>
                                        <el-option label="已完成" value="已完成"></el-option>
                                        <el-option label="已取消" value="已取消"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="故障类型">
                                    <el-select v-model="repairSearch.type" placeholder="请选择类型" clearable style="width: 120px;">
                                        <el-option label="全部" value=""></el-option>
                                        <el-option label="水电" value="水电"></el-option>
                                        <el-option label="家具" value="家具"></el-option>
                                        <el-option label="结构" value="结构"></el-option>
                                        <el-option label="其他" value="其他"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="紧急程度">
                                    <el-select v-model="repairSearch.urgency" placeholder="请选择程度" clearable style="width: 120px;">
                                        <el-option label="全部" value=""></el-option>
                                        <el-option label="紧急" value="紧急"></el-option>
                                        <el-option label="一般" value="一般"></el-option>
                                        <el-option label="不急" value="不急"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="searchRepairs">搜索</el-button>
                                    <el-button @click="resetRepairSearch">重置</el-button>
                                </el-form-item>
                            </el-form>
                        </el-card>

                        <!-- 报修列表 -->
                        <el-card style="margin-top: 20px;">
                            <div slot="header">
                                <span>报修工单列表</span>
                            </div>

                            <div class="table-container">
                                <el-table :data="filteredRepairs" stripe style="width: 100%">
                                <el-table-column prop="id" label="工单编号" width="120"></el-table-column>
                                <el-table-column prop="houseAddress" label="房源地址" min-width="200"></el-table-column>
                                <el-table-column prop="tenant" label="报修人" width="100"></el-table-column>
                                <el-table-column prop="type" label="故障类型" width="100"></el-table-column>
                                <el-table-column prop="description" label="故障描述" min-width="150"></el-table-column>
                                <el-table-column prop="urgency" label="紧急程度" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getUrgencyType(scope.row.urgency)">{{ scope.row.urgency }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="createTime" label="报修时间" width="120"></el-table-column>
                                <el-table-column prop="status" label="状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getRepairStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="160">
                                    <template slot-scope="scope">
                                        <div class="action-buttons">
                                            <el-button size="mini" @click="viewRepair(scope.row)">查看</el-button>
                                            <el-button size="mini" type="primary" @click="assignRepair(scope.row)" v-if="scope.row.status === '待处理'">派单</el-button>
                                            <el-button size="mini" type="success" @click="completeRepair(scope.row)" v-if="scope.row.status === '处理中'">完成</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                                </el-table>
                            </div>

                            <!-- 分页 -->
                            <div style="text-align: center; margin-top: 20px;">
                                <el-pagination
                                    @current-change="handleRepairPageChange"
                                    :current-page.sync="repairCurrentPage"
                                    :page-size="repairPageSize"
                                    layout="prev, pager, next, jumper"
                                    :total="totalRepairs">
                                </el-pagination>
                            </div>
                        </el-card>
                    </el-tab-pane>

                    <el-tab-pane label="维修员管理" name="worker">
                        <el-card>
                            <div slot="header">
                                <span>维修员信息</span>
                                <el-button type="primary" size="small" style="float: right;">
                                    <i class="el-icon-plus"></i> 添加维修员
                                </el-button>
                            </div>

                            <div class="table-container">
                                <el-table :data="repairWorkers" stripe style="width: 100%">
                                <el-table-column prop="id" label="员工编号" width="120"></el-table-column>
                                <el-table-column prop="name" label="姓名" width="100"></el-table-column>
                                <el-table-column prop="phone" label="联系电话" width="130"></el-table-column>
                                <el-table-column prop="specialty" label="专业领域" min-width="150"></el-table-column>
                                <el-table-column prop="workload" label="当前工单" width="100"></el-table-column>
                                <el-table-column prop="rating" label="评分" width="120">
                                    <template slot-scope="scope">
                                        <el-rate v-model="scope.row.rating" disabled show-score></el-rate>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="status" label="状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="scope.row.status === '在线' ? 'success' : 'info'">{{ scope.row.status }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="120">
                                    <template slot-scope="scope">
                                        <div class="action-buttons">
                                            <el-button size="mini" @click="viewWorker(scope.row)">查看</el-button>
                                            <el-button size="mini" type="primary" @click="editWorker(scope.row)">编辑</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                                </el-table>
                            </div>
                        </el-card>
                    </el-tab-pane>
                </el-tabs>

                <!-- 报修对话框 -->
                <el-dialog title="新增报修" :visible.sync="showRepairDialog" width="60%">
                    <el-form :model="repairForm" label-width="100px" ref="repairForm">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="房源地址" required>
                                    <el-input v-model="repairForm.houseAddress" placeholder="请输入房源地址"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="报修人" required>
                                    <el-input v-model="repairForm.tenant" placeholder="请输入报修人姓名"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="故障类型" required>
                                    <el-select v-model="repairForm.type" placeholder="请选择故障类型" style="width: 100%;">
                                        <el-option label="水电" value="水电"></el-option>
                                        <el-option label="家具" value="家具"></el-option>
                                        <el-option label="结构" value="结构"></el-option>
                                        <el-option label="其他" value="其他"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="紧急程度" required>
                                    <el-select v-model="repairForm.urgency" placeholder="请选择紧急程度" style="width: 100%;">
                                        <el-option label="紧急" value="紧急"></el-option>
                                        <el-option label="一般" value="一般"></el-option>
                                        <el-option label="不急" value="不急"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-form-item label="故障描述" required>
                            <el-input type="textarea" v-model="repairForm.description" :rows="3" placeholder="请详细描述故障情况"></el-input>
                        </el-form-item>

                        <el-form-item label="联系电话">
                            <el-input v-model="repairForm.phone" placeholder="请输入联系电话"></el-input>
                        </el-form-item>

                        <el-form-item label="故障图片">
                            <el-upload
                                action="#"
                                list-type="picture-card"
                                :auto-upload="false"
                                :file-list="repairForm.fileList">
                                <i class="el-icon-plus"></i>
                            </el-upload>
                            <div style="color: #909399; font-size: 12px;">
                                请上传故障现场照片，便于维修员了解情况
                            </div>
                        </el-form-item>
                    </el-form>

                    <div slot="footer">
                        <el-button @click="showRepairDialog = false">取消</el-button>
                        <el-button type="primary" @click="submitRepair">提交报修</el-button>
                    </div>
                </el-dialog>

                <!-- 查看维修详情对话框 -->
                <el-dialog title="维修工单详情" :visible.sync="showRepairDetailDialog" width="60%">
                    <div v-if="currentRepair">
                        <el-descriptions title="工单信息" :column="2" border>
                            <el-descriptions-item label="工单编号">{{ currentRepair.id }}</el-descriptions-item>
                            <el-descriptions-item label="工单状态">
                                <el-tag :type="getRepairStatusType(currentRepair.status)">{{ currentRepair.status }}</el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label="房源地址">{{ currentRepair.houseAddress }}</el-descriptions-item>
                            <el-descriptions-item label="报修人">{{ currentRepair.tenant }}</el-descriptions-item>
                            <el-descriptions-item label="故障类型">{{ currentRepair.type }}</el-descriptions-item>
                            <el-descriptions-item label="紧急程度">
                                <el-tag :type="getUrgencyType(currentRepair.urgency)">{{ currentRepair.urgency }}</el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label="报修时间">{{ currentRepair.createTime }}</el-descriptions-item>
                            <el-descriptions-item label="联系电话">{{ currentRepair.phone || '未提供' }}</el-descriptions-item>
                        </el-descriptions>

                        <el-divider content-position="left">故障描述</el-divider>
                        <p>{{ currentRepair.description }}</p>

                        <el-divider content-position="left">处理进度</el-divider>
                        <el-steps :active="getRepairStep(currentRepair.status)" finish-status="success">
                            <el-step title="提交报修" description="报修已提交"></el-step>
                            <el-step title="派单处理" description="分配维修员"></el-step>
                            <el-step title="维修中" description="维修员处理中"></el-step>
                            <el-step title="完成验收" description="维修完成"></el-step>
                        </el-steps>

                        <div v-if="currentRepair.worker" style="margin-top: 20px;">
                            <el-divider content-position="left">维修员信息</el-divider>
                            <el-descriptions :column="2" border>
                                <el-descriptions-item label="维修员">{{ currentRepair.worker.name }}</el-descriptions-item>
                                <el-descriptions-item label="联系电话">{{ currentRepair.worker.phone }}</el-descriptions-item>
                                <el-descriptions-item label="专业领域">{{ currentRepair.worker.specialty }}</el-descriptions-item>
                                <el-descriptions-item label="评分">
                                    <el-rate v-model="currentRepair.worker.rating" disabled show-score></el-rate>
                                </el-descriptions-item>
                            </el-descriptions>
                        </div>
                    </div>

                    <div slot="footer">
                        <el-button @click="showRepairDetailDialog = false">关闭</el-button>
                    </div>
                </el-dialog>

                <!-- 添加维修员对话框 -->
                <el-dialog title="添加维修员" :visible.sync="showAddWorkerDialog" width="50%">
                    <el-form :model="workerForm" label-width="100px" ref="workerForm">
                        <el-form-item label="姓名" required>
                            <el-input v-model="workerForm.name" placeholder="请输入维修员姓名"></el-input>
                        </el-form-item>
                        <el-form-item label="联系电话" required>
                            <el-input v-model="workerForm.phone" placeholder="请输入联系电话"></el-input>
                        </el-form-item>
                        <el-form-item label="专业领域" required>
                            <el-select v-model="workerForm.specialty" placeholder="请选择专业领域" style="width: 100%;">
                                <el-option label="水电维修" value="水电维修"></el-option>
                                <el-option label="家具维修" value="家具维修"></el-option>
                                <el-option label="结构维修" value="结构维修"></el-option>
                                <el-option label="综合维修" value="综合维修"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="工作经验">
                            <el-input-number v-model="workerForm.experience" :min="0" :max="30" placeholder="年"></el-input-number>
                            <span style="margin-left: 10px;">年</span>
                        </el-form-item>
                    </el-form>

                    <div slot="footer">
                        <el-button @click="showAddWorkerDialog = false">取消</el-button>
                        <el-button type="primary" @click="addWorker">添加</el-button>
                    </div>
                </el-dialog>

                <!-- 查看维修员详情对话框 -->
                <el-dialog title="维修员详情" :visible.sync="showWorkerDetailDialog" width="50%">
                    <div v-if="currentWorker">
                        <el-descriptions title="基本信息" :column="2" border>
                            <el-descriptions-item label="员工编号">{{ currentWorker.id }}</el-descriptions-item>
                            <el-descriptions-item label="姓名">{{ currentWorker.name }}</el-descriptions-item>
                            <el-descriptions-item label="联系电话">{{ currentWorker.phone }}</el-descriptions-item>
                            <el-descriptions-item label="专业领域">{{ currentWorker.specialty }}</el-descriptions-item>
                            <el-descriptions-item label="当前工单">{{ currentWorker.workload }}个</el-descriptions-item>
                            <el-descriptions-item label="评分">
                                <el-rate v-model="currentWorker.rating" disabled show-score></el-rate>
                            </el-descriptions-item>
                            <el-descriptions-item label="状态">
                                <el-tag :type="currentWorker.status === '在线' ? 'success' : 'info'">{{ currentWorker.status }}</el-tag>
                            </el-descriptions-item>
                        </el-descriptions>
                    </div>

                    <div slot="footer">
                        <el-button @click="showWorkerDetailDialog = false">关闭</el-button>
                    </div>
                </el-dialog>

                <!-- 编辑维修员对话框 -->
                <el-dialog title="编辑维修员" :visible.sync="showEditWorkerDialog" width="50%">
                    <el-form :model="editWorkerForm" label-width="100px" ref="editWorkerForm" v-if="currentWorker">
                        <el-form-item label="姓名" required>
                            <el-input v-model="editWorkerForm.name" placeholder="请输入维修员姓名"></el-input>
                        </el-form-item>
                        <el-form-item label="联系电话" required>
                            <el-input v-model="editWorkerForm.phone" placeholder="请输入联系电话"></el-input>
                        </el-form-item>
                        <el-form-item label="专业领域" required>
                            <el-select v-model="editWorkerForm.specialty" placeholder="请选择专业领域" style="width: 100%;">
                                <el-option label="水电维修" value="水电维修"></el-option>
                                <el-option label="家具维修" value="家具维修"></el-option>
                                <el-option label="结构维修" value="结构维修"></el-option>
                                <el-option label="综合维修" value="综合维修"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="状态">
                            <el-select v-model="editWorkerForm.status" placeholder="请选择状态" style="width: 100%;">
                                <el-option label="在线" value="在线"></el-option>
                                <el-option label="忙碌" value="忙碌"></el-option>
                                <el-option label="离线" value="离线"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>

                    <div slot="footer">
                        <el-button @click="showEditWorkerDialog = false">取消</el-button>
                        <el-button type="primary" @click="updateWorker">保存修改</el-button>
                    </div>
                </el-dialog>
            </div>
            
            <!-- 退租管理内容 -->
            <div v-if="activeTab === 'exit'" class="tab-content">
                <el-tabs v-model="exitActiveTab">
                    <el-tab-pane label="退租申请" name="list">
                        <!-- 筛选条件 -->
                        <el-card class="filter-section">
                            <div slot="header">
                                <span>退租申请管理</span>
                                <el-button type="primary" size="small" style="float: right;" @click="showExitDialog = true">
                                    <i class="el-icon-plus"></i> 新增退租申请
                                </el-button>
                            </div>

                            <el-form :inline="true" size="small">
                                <el-form-item label="申请状态">
                                    <el-select v-model="exitSearch.status" placeholder="请选择状态" clearable style="width: 120px;">
                                        <el-option label="全部" value=""></el-option>
                                        <el-option label="待审核" value="待审核"></el-option>
                                        <el-option label="审核通过" value="审核通过"></el-option>
                                        <el-option label="待验房" value="待验房"></el-option>
                                        <el-option label="已完成" value="已完成"></el-option>
                                        <el-option label="已拒绝" value="已拒绝"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="退租原因">
                                    <el-select v-model="exitSearch.reason" placeholder="请选择原因" clearable style="width: 120px;">
                                        <el-option label="全部" value=""></el-option>
                                        <el-option label="购房" value="购房"></el-option>
                                        <el-option label="迁离" value="迁离"></el-option>
                                        <el-option label="其他" value="其他"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="承租人">
                                    <el-input v-model="exitSearch.tenant" placeholder="请输入承租人姓名" clearable style="width: 150px;"></el-input>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="searchExits">搜索</el-button>
                                    <el-button @click="resetExitSearch">重置</el-button>
                                </el-form-item>
                            </el-form>
                        </el-card>

                        <!-- 退租申请列表 -->
                        <el-card style="margin-top: 20px;">
                            <div slot="header">
                                <span>退租申请列表</span>
                            </div>

                            <el-table :data="filteredExits" stripe style="width: 100%">
                                <el-table-column prop="id" label="申请编号" width="120"></el-table-column>
                                <el-table-column prop="houseAddress" label="房源地址" min-width="200"></el-table-column>
                                <el-table-column prop="tenant" label="承租人" width="100"></el-table-column>
                                <el-table-column prop="reason" label="退租原因" width="100"></el-table-column>
                                <el-table-column prop="applyDate" label="申请时间" width="120"></el-table-column>
                                <el-table-column prop="expectedDate" label="预计退租日期" width="120"></el-table-column>
                                <el-table-column prop="deposit" label="押金金额" width="100">
                                    <template slot-scope="scope">
                                        ¥{{ scope.row.deposit }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="status" label="状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getExitStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="160">
                                    <template slot-scope="scope">
                                        <div class="action-buttons">
                                            <el-button size="mini" @click="viewExit(scope.row)">查看</el-button>
                                            <el-button size="mini" type="primary" @click="approveExit(scope.row)" v-if="scope.row.status === '待审核'">审核</el-button>
                                            <el-button size="mini" type="success" @click="scheduleInspection(scope.row)" v-if="scope.row.status === '审核通过'">安排验房</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>

                            <!-- 分页 -->
                            <div style="text-align: center; margin-top: 20px;">
                                <el-pagination
                                    @current-change="handleExitPageChange"
                                    :current-page.sync="exitCurrentPage"
                                    :page-size="exitPageSize"
                                    layout="prev, pager, next, jumper"
                                    :total="totalExits">
                                </el-pagination>
                            </div>
                        </el-card>
                    </el-tab-pane>

                    <el-tab-pane label="线上验房" name="inspection">
                        <el-card>
                            <div slot="header">
                                <span>验房管理</span>
                            </div>

                            <el-table :data="inspections" stripe style="width: 100%">
                                <el-table-column prop="id" label="验房编号" width="120"></el-table-column>
                                <el-table-column prop="houseAddress" label="房源地址" min-width="200"></el-table-column>
                                <el-table-column prop="tenant" label="承租人" width="100"></el-table-column>
                                <el-table-column prop="inspector" label="验房员" width="100"></el-table-column>
                                <el-table-column prop="scheduledDate" label="预约时间" width="150"></el-table-column>
                                <el-table-column prop="status" label="验房状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getInspectionStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="160">
                                    <template slot-scope="scope">
                                        <div class="action-buttons">
                                            <el-button size="mini" @click="viewInspection(scope.row)">查看</el-button>
                                            <el-button size="mini" type="primary" @click="startInspection(scope.row)" v-if="scope.row.status === '待验房'">开始验房</el-button>
                                            <el-button size="mini" type="success" @click="completeInspection(scope.row)" v-if="scope.row.status === '验房中'">完成验房</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-card>
                    </el-tab-pane>

                    <el-tab-pane label="押金管理" name="deposit">
                        <el-card>
                            <div slot="header">
                                <span>押金退还管理</span>
                            </div>

                            <el-table :data="deposits" stripe style="width: 100%">
                                <el-table-column prop="id" label="退款编号" width="120"></el-table-column>
                                <el-table-column prop="tenant" label="承租人" width="100"></el-table-column>
                                <el-table-column prop="houseAddress" label="房源地址" min-width="200"></el-table-column>
                                <el-table-column prop="originalDeposit" label="原押金" width="100">
                                    <template slot-scope="scope">
                                        ¥{{ scope.row.originalDeposit }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="deduction" label="扣除金额" width="100">
                                    <template slot-scope="scope">
                                        ¥{{ scope.row.deduction }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="refundAmount" label="退还金额" width="100">
                                    <template slot-scope="scope">
                                        ¥{{ scope.row.refundAmount }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="status" label="退款状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getDepositStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="160">
                                    <template slot-scope="scope">
                                        <div class="action-buttons">
                                            <el-button size="mini" @click="viewDeposit(scope.row)">查看</el-button>
                                            <el-button size="mini" type="primary" @click="processRefund(scope.row)" v-if="scope.row.status === '待退款'">处理退款</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-card>
                    </el-tab-pane>
                </el-tabs>

                <!-- 退租申请对话框 -->
                <el-dialog title="新增退租申请" :visible.sync="showExitDialog" width="60%">
                    <el-form :model="exitForm" label-width="100px" ref="exitForm">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="房源地址" required>
                                    <el-input v-model="exitForm.houseAddress" placeholder="请输入房源地址"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="承租人" required>
                                    <el-input v-model="exitForm.tenant" placeholder="请输入承租人姓名"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="退租原因" required>
                                    <el-select v-model="exitForm.reason" placeholder="请选择退租原因" style="width: 100%;">
                                        <el-option label="购房" value="购房"></el-option>
                                        <el-option label="迁离" value="迁离"></el-option>
                                        <el-option label="其他" value="其他"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="预计退租日期" required>
                                    <el-date-picker v-model="exitForm.expectedDate" type="date" placeholder="选择日期" style="width: 100%;"></el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-form-item label="详细说明">
                            <el-input type="textarea" v-model="exitForm.description" :rows="3" placeholder="请详细说明退租原因"></el-input>
                        </el-form-item>

                        <el-form-item label="联系电话">
                            <el-input v-model="exitForm.phone" placeholder="请输入联系电话"></el-input>
                        </el-form-item>

                        <el-form-item label="相关材料">
                            <el-upload
                                action="#"
                                list-type="picture-card"
                                :auto-upload="false"
                                :file-list="exitForm.fileList">
                                <i class="el-icon-plus"></i>
                            </el-upload>
                            <div style="color: #909399; font-size: 12px;">
                                请上传购房合同、工作调动证明等相关材料
                            </div>
                        </el-form-item>
                    </el-form>

                    <div slot="footer">
                        <el-button @click="showExitDialog = false">取消</el-button>
                        <el-button type="primary" @click="submitExit">提交申请</el-button>
                    </div>
                </el-dialog>

                <!-- 查看退租详情对话框 -->
                <el-dialog title="退租申请详情" :visible.sync="showExitDetailDialog" width="60%">
                    <div v-if="currentExit">
                        <el-descriptions title="申请信息" :column="2" border>
                            <el-descriptions-item label="申请编号">{{ currentExit.id }}</el-descriptions-item>
                            <el-descriptions-item label="申请状态">
                                <el-tag :type="getExitStatusType(currentExit.status)">{{ currentExit.status }}</el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label="房源地址">{{ currentExit.houseAddress }}</el-descriptions-item>
                            <el-descriptions-item label="承租人">{{ currentExit.tenant }}</el-descriptions-item>
                            <el-descriptions-item label="退租原因">{{ currentExit.reason }}</el-descriptions-item>
                            <el-descriptions-item label="申请时间">{{ currentExit.applyDate }}</el-descriptions-item>
                            <el-descriptions-item label="预计退租日期">{{ currentExit.expectedDate }}</el-descriptions-item>
                            <el-descriptions-item label="联系电话">{{ currentExit.phone || '未提供' }}</el-descriptions-item>
                        </el-descriptions>

                        <el-divider content-position="left">退租说明</el-divider>
                        <p>{{ currentExit.description || '无' }}</p>

                        <el-divider content-position="left">办理进度</el-divider>
                        <el-steps :active="getExitStep(currentExit.status)" finish-status="success">
                            <el-step title="提交申请" description="退租申请已提交"></el-step>
                            <el-step title="资格审核" description="审核退租资格"></el-step>
                            <el-step title="房屋验收" description="验收房屋状况"></el-step>
                            <el-step title="押金退还" description="退还押金"></el-step>
                            <el-step title="完成退租" description="退租手续完成"></el-step>
                        </el-steps>

                        <div v-if="currentExit.status !== '待审核'" style="margin-top: 20px;">
                            <el-divider content-position="left">处理记录</el-divider>
                            <el-timeline>
                                <el-timeline-item
                                    v-for="(record, index) in getExitRecords(currentExit)"
                                    :key="index"
                                    :timestamp="record.time"
                                    :type="record.type">
                                    {{ record.content }}
                                </el-timeline-item>
                            </el-timeline>
                        </div>
                    </div>

                    <div slot="footer">
                        <el-button @click="showExitDetailDialog = false">关闭</el-button>
                        <el-button v-if="currentExit && currentExit.status === '待审核'" type="primary" @click="approveExit">审核通过</el-button>
                        <el-button v-if="currentExit && currentExit.status === '待审核'" type="danger" @click="rejectExit">审核拒绝</el-button>
                    </div>
                </el-dialog>
            </div>
            
            <!-- 动态监管内容 -->
            <div v-if="activeTab === 'monitor'" class="tab-content">
                <el-tabs v-model="monitorActiveTab">
                    <el-tab-pane label="信用积分" name="credit">
                        <!-- 信用积分概览 -->
                        <el-row :gutter="20" style="margin-bottom: 20px;">
                            <el-col :span="8">
                                <el-card shadow="hover">
                                    <div style="display: flex; align-items: center;">
                                        <i class="el-icon-star-on" style="font-size: 40px; margin-right: 15px; color: #F7BA2A;"></i>
                                        <div>
                                            <div style="font-size: 24px; font-weight: bold;">{{ creditStats.averageScore }}</div>
                                            <div>平均信用分</div>
                                        </div>
                                    </div>
                                </el-card>
                            </el-col>
                            <el-col :span="8">
                                <el-card shadow="hover">
                                    <div style="display: flex; align-items: center;">
                                        <i class="el-icon-warning" style="font-size: 40px; margin-right: 15px; color: #E6A23C;"></i>
                                        <div>
                                            <div style="font-size: 24px; font-weight: bold;">{{ creditStats.lowCreditCount }}</div>
                                            <div>低信用用户</div>
                                        </div>
                                    </div>
                                </el-card>
                            </el-col>
                            <el-col :span="8">
                                <el-card shadow="hover">
                                    <div style="display: flex; align-items: center;">
                                        <i class="el-icon-trophy" style="font-size: 40px; margin-right: 15px; color: #67C23A;"></i>
                                        <div>
                                            <div style="font-size: 24px; font-weight: bold;">{{ creditStats.excellentCount }}</div>
                                            <div>优秀用户</div>
                                        </div>
                                    </div>
                                </el-card>
                            </el-col>
                        </el-row>

                        <!-- 信用积分列表 -->
                        <el-card>
                            <div slot="header">
                                <span>租户信用积分管理</span>
                            </div>

                            <el-form :inline="true" size="small" style="margin-bottom: 20px;">
                                <el-form-item label="信用等级">
                                    <el-select v-model="creditSearch.level" placeholder="请选择等级" clearable style="width: 120px;">
                                        <el-option label="全部" value=""></el-option>
                                        <el-option label="优秀" value="优秀"></el-option>
                                        <el-option label="良好" value="良好"></el-option>
                                        <el-option label="一般" value="一般"></el-option>
                                        <el-option label="较差" value="较差"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="租户姓名">
                                    <el-input v-model="creditSearch.tenant" placeholder="请输入租户姓名" clearable style="width: 150px;"></el-input>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="searchCredits">搜索</el-button>
                                    <el-button @click="resetCreditSearch">重置</el-button>
                                </el-form-item>
                            </el-form>

                            <el-table :data="filteredCredits" stripe style="width: 100%">
                                <el-table-column prop="tenant" label="租户姓名" width="100"></el-table-column>
                                <el-table-column prop="houseAddress" label="房源地址" min-width="200"></el-table-column>
                                <el-table-column prop="score" label="信用分" width="100">
                                    <template slot-scope="scope">
                                        <span :style="{color: getCreditScoreColor(scope.row.score)}">{{ scope.row.score }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="level" label="信用等级" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getCreditLevelType(scope.row.level)">{{ scope.row.level }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="lastUpdate" label="最后更新" width="120"></el-table-column>
                                <el-table-column label="操作" width="160">
                                    <template slot-scope="scope">
                                        <div class="action-buttons">
                                            <el-button size="mini" @click="viewCreditDetail(scope.row)">查看详情</el-button>
                                            <el-button size="mini" type="primary" @click="adjustCredit(scope.row)">调整积分</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>

                            <!-- 分页 -->
                            <div style="text-align: center; margin-top: 20px;">
                                <el-pagination
                                    @current-change="handleCreditPageChange"
                                    :current-page.sync="creditCurrentPage"
                                    :page-size="creditPageSize"
                                    layout="prev, pager, next, jumper"
                                    :total="totalCredits">
                                </el-pagination>
                            </div>
                        </el-card>
                    </el-tab-pane>

                    <el-tab-pane label="多部门核验" name="verification">
                        <el-card>
                            <div slot="header">
                                <span>多部门数据核验看板</span>
                                <el-button type="primary" size="small" style="float: right;" @click="refreshVerification">
                                    <i class="el-icon-refresh"></i> 刷新数据
                                </el-button>
                            </div>

                            <!-- 核验统计 -->
                            <el-row :gutter="20" style="margin-bottom: 20px;">
                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <div style="text-align: center;">
                                            <div style="font-size: 24px; font-weight: bold; color: #409EFF;">{{ verificationStats.totalChecked }}</div>
                                            <div>已核验户数</div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <div style="text-align: center;">
                                            <div style="font-size: 24px; font-weight: bold; color: #67C23A;">{{ verificationStats.passedCount }}</div>
                                            <div>核验通过</div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <div style="text-align: center;">
                                            <div style="font-size: 24px; font-weight: bold; color: #E6A23C;">{{ verificationStats.warningCount }}</div>
                                            <div>异常预警</div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :span="6">
                                    <el-card shadow="hover">
                                        <div style="text-align: center;">
                                            <div style="font-size: 24px; font-weight: bold; color: #F56C6C;">{{ verificationStats.failedCount }}</div>
                                            <div>核验失败</div>
                                        </div>
                                    </el-card>
                                </el-col>
                            </el-row>

                            <!-- 核验结果列表 -->
                            <el-table :data="verifications" stripe style="width: 100%">
                                <el-table-column prop="tenant" label="租户姓名" width="100"></el-table-column>
                                <el-table-column prop="idCard" label="身份证号" width="180">
                                    <template slot-scope="scope">
                                        {{ scope.row.idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2') }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="houseAddress" label="房源地址" min-width="200"></el-table-column>
                                <el-table-column prop="publicSecurity" label="公安核验" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getVerificationStatusType(scope.row.publicSecurity)">{{ scope.row.publicSecurity }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="civilAffairs" label="民政核验" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getVerificationStatusType(scope.row.civilAffairs)">{{ scope.row.civilAffairs }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="realEstate" label="不动产核验" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getVerificationStatusType(scope.row.realEstate)">{{ scope.row.realEstate }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="lastCheck" label="最后核验时间" width="150"></el-table-column>
                                <el-table-column label="操作" width="160">
                                    <template slot-scope="scope">
                                        <div class="action-buttons">
                                            <el-button size="mini" @click="viewVerificationDetail(scope.row)">查看详情</el-button>
                                            <el-button size="mini" type="primary" @click="recheckVerification(scope.row)">重新核验</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>

                            <!-- 分页 -->
                            <div style="text-align: center; margin-top: 20px;">
                                <el-pagination
                                    @current-change="handleVerificationPageChange"
                                    :current-page.sync="verificationCurrentPage"
                                    :page-size="verificationPageSize"
                                    layout="prev, pager, next, jumper"
                                    :total="totalVerifications">
                                </el-pagination>
                            </div>
                        </el-card>
                    </el-tab-pane>

                    <el-tab-pane label="异常监控" name="alert">
                        <el-card>
                            <div slot="header">
                                <span>异常情况监控</span>
                            </div>

                            <el-table :data="alerts" stripe style="width: 100%">
                                <el-table-column prop="id" label="预警编号" width="120"></el-table-column>
                                <el-table-column prop="tenant" label="租户姓名" width="100"></el-table-column>
                                <el-table-column prop="type" label="异常类型" width="120">
                                    <template slot-scope="scope">
                                        <el-tag :type="getAlertType(scope.row.type)">{{ scope.row.type }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="description" label="异常描述" min-width="200"></el-table-column>
                                <el-table-column prop="level" label="严重程度" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getAlertLevelType(scope.row.level)">{{ scope.row.level }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="createTime" label="发现时间" width="150"></el-table-column>
                                <el-table-column prop="status" label="处理状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getAlertStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="120">
                                    <template slot-scope="scope">
                                        <div class="action-buttons">
                                            <el-button size="mini" @click="viewAlert(scope.row)">查看</el-button>
                                            <el-button size="mini" type="primary" @click="handleAlert(scope.row)" v-if="scope.row.status === '待处理'">处理</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-card>
                    </el-tab-pane>
                </el-tabs>

                <!-- 信用积分详情对话框 -->
                <el-dialog title="信用积分详情" :visible.sync="showCreditDetailDialog" width="60%">
                    <div v-if="currentCredit">
                        <el-descriptions title="租户信息" :column="2" border>
                            <el-descriptions-item label="租户姓名">{{ currentCredit.tenant }}</el-descriptions-item>
                            <el-descriptions-item label="身份证号">{{ currentCredit.idCard }}</el-descriptions-item>
                            <el-descriptions-item label="联系电话">{{ currentCredit.phone }}</el-descriptions-item>
                            <el-descriptions-item label="房源地址">{{ currentCredit.address }}</el-descriptions-item>
                            <el-descriptions-item label="当前积分">
                                <span :style="{color: getCreditScoreColor(currentCredit.score), fontSize: '18px', fontWeight: 'bold'}">
                                    {{ currentCredit.score }}分
                                </span>
                            </el-descriptions-item>
                            <el-descriptions-item label="信用等级">
                                <el-tag :type="getCreditLevelType(currentCredit.level)">{{ currentCredit.level }}</el-tag>
                            </el-descriptions-item>
                        </el-descriptions>

                        <el-divider content-position="left">积分变动记录</el-divider>
                        <el-timeline>
                            <el-timeline-item
                                v-for="(record, index) in getCreditHistory(currentCredit)"
                                :key="index"
                                :timestamp="record.date"
                                :type="record.type">
                                {{ record.description }} ({{ record.change > 0 ? '+' : '' }}{{ record.change }}分)
                            </el-timeline-item>
                        </el-timeline>
                    </div>

                    <div slot="footer">
                        <el-button @click="showCreditDetailDialog = false">关闭</el-button>
                        <el-button type="primary" @click="showAdjustCreditDialog = true">调整积分</el-button>
                    </div>
                </el-dialog>

                <!-- 调整积分对话框 -->
                <el-dialog title="调整信用积分" :visible.sync="showAdjustCreditDialog" width="40%">
                    <el-form :model="adjustCreditForm" label-width="100px" v-if="currentCredit">
                        <el-form-item label="租户姓名">
                            <el-input v-model="currentCredit.tenant" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="当前积分">
                            <el-input v-model="currentCredit.score" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="调整类型" required>
                            <el-select v-model="adjustCreditForm.type" placeholder="请选择调整类型" style="width: 100%;">
                                <el-option label="增加积分" value="add"></el-option>
                                <el-option label="扣减积分" value="subtract"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="调整分值" required>
                            <el-input-number v-model="adjustCreditForm.amount" :min="1" :max="100" placeholder="请输入调整分值"></el-input-number>
                        </el-form-item>
                        <el-form-item label="调整原因" required>
                            <el-input type="textarea" v-model="adjustCreditForm.reason" :rows="3" placeholder="请输入调整原因"></el-input>
                        </el-form-item>
                    </el-form>

                    <div slot="footer">
                        <el-button @click="showAdjustCreditDialog = false">取消</el-button>
                        <el-button type="primary" @click="submitCreditAdjustment">确认调整</el-button>
                    </div>
                </el-dialog>

                <!-- 核验详情对话框 -->
                <el-dialog title="核验详情" :visible.sync="showVerificationDetailDialog" width="60%">
                    <div v-if="currentVerification">
                        <el-descriptions title="核验信息" :column="2" border>
                            <el-descriptions-item label="租户姓名">{{ currentVerification.tenant }}</el-descriptions-item>
                            <el-descriptions-item label="身份证号">{{ currentVerification.idCard }}</el-descriptions-item>
                            <el-descriptions-item label="核验状态">
                                <el-tag :type="getVerificationStatusType(currentVerification.status)">{{ currentVerification.status }}</el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label="最后核验时间">{{ currentVerification.lastCheck }}</el-descriptions-item>
                        </el-descriptions>

                        <el-divider content-position="left">各部门核验结果</el-divider>
                        <el-table :data="getVerificationDetails(currentVerification)" border>
                            <el-table-column prop="department" label="核验部门" width="120"></el-table-column>
                            <el-table-column prop="item" label="核验项目"></el-table-column>
                            <el-table-column prop="result" label="核验结果" width="100">
                                <template slot-scope="scope">
                                    <el-tag :type="scope.row.result === '通过' ? 'success' : 'danger'">{{ scope.row.result }}</el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="checkTime" label="核验时间" width="150"></el-table-column>
                        </el-table>
                    </div>

                    <div slot="footer">
                        <el-button @click="showVerificationDetailDialog = false">关闭</el-button>
                        <el-button type="primary" @click="recheckVerification(currentVerification)">重新核验</el-button>
                    </div>
                </el-dialog>

                <!-- 异常详情对话框 -->
                <el-dialog title="异常详情" :visible.sync="showAlertDetailDialog" width="60%">
                    <div v-if="currentAlert">
                        <el-descriptions title="异常信息" :column="2" border>
                            <el-descriptions-item label="异常编号">{{ currentAlert.id }}</el-descriptions-item>
                            <el-descriptions-item label="异常类型">
                                <el-tag :type="getAlertType(currentAlert.type)">{{ currentAlert.type }}</el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label="严重程度">
                                <el-tag :type="getAlertLevelType(currentAlert.level)">{{ currentAlert.level }}</el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label="处理状态">
                                <el-tag :type="getAlertStatusType(currentAlert.status)">{{ currentAlert.status }}</el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label="租户姓名">{{ currentAlert.tenant }}</el-descriptions-item>
                            <el-descriptions-item label="房源地址">{{ currentAlert.address }}</el-descriptions-item>
                            <el-descriptions-item label="发现时间">{{ currentAlert.createTime }}</el-descriptions-item>
                            <el-descriptions-item label="处理人">{{ currentAlert.handler || '未分配' }}</el-descriptions-item>
                        </el-descriptions>

                        <el-divider content-position="left">异常详情</el-divider>
                        <p>{{ currentAlert.description }}</p>

                        <div v-if="currentAlert.status !== '待处理'">
                            <el-divider content-position="left">处理记录</el-divider>
                            <el-timeline>
                                <el-timeline-item
                                    v-for="(record, index) in getAlertHistory(currentAlert)"
                                    :key="index"
                                    :timestamp="record.time"
                                    :type="record.type">
                                    {{ record.content }}
                                </el-timeline-item>
                            </el-timeline>
                        </div>
                    </div>

                    <div slot="footer">
                        <el-button @click="showAlertDetailDialog = false">关闭</el-button>
                        <el-button v-if="currentAlert && currentAlert.status === '待处理'" type="primary" @click="processAlert">开始处理</el-button>
                        <el-button v-if="currentAlert && currentAlert.status === '处理中'" type="success" @click="completeAlert">完成处理</el-button>
                    </div>
                </el-dialog>
            </div>
            </div>

            <!-- 页面底部 -->
            <div class="footer" style="background: white; text-align: center; padding: 15px; border-top: 1px solid #e6e6e6; color: #909399;">
                © 2023 公租房智慧社区云平台
            </div>
        </div>
    </div>

    <script src="vue.js"></script>
    <script src="element-ui.js"></script>
    <script>
        // 页面级Vue实例
        new Vue({
            el: '#app',
            data: {
                // 当前激活的标签页
                activeTab: 'home',

                // UI相关状态
                loading: null,
                isMobile: false,
                isFullscreen: false,
                
                // 统计数据
                statistics: [
                    { title: '可用房源总数', value: '1,284', icon: 'el-icon-house' },
                    { title: '在租合同数量', value: '926', icon: 'el-icon-document' },
                    { title: '按时缴费率', value: '94.5%', icon: 'el-icon-money' },
                    { title: '待处理工单', value: '42', icon: 'el-icon-service' }
                ],
                
                // 模块列表
                modules: [
                    { name: '房源管理', tab: 'house', icon: 'el-icon-house' },
                    { name: '人员管理', tab: 'person', icon: 'el-icon-user-solid' },
                    { name: '配租申请', tab: 'apply', icon: 'el-icon-document-checked' },
                    { name: '合同管理', tab: 'contract', icon: 'el-icon-document' },
                    { name: '租金管理', tab: 'payment', icon: 'el-icon-money' },
                    { name: '维修服务', tab: 'repair', icon: 'el-icon-service' },
                    { name: '退租管理', tab: 'exit', icon: 'el-icon-switch-button' },
                    { name: '动态监管', tab: 'monitor', icon: 'el-icon-data-line' }
                ],
                
                // 合同管理相关数据
                contractActiveTab: 'list',
                contractFilterForm: {
                    status: '',
                    dateRange: [],
                    tenant: ''
                },
                contractCurrentPage: 1,
                contractPageSize: 10,
                totalContracts: 50,
                contracts: [
                    {
                        id: 'CT001',
                        houseTitle: '融创·智慧公租房',
                        address: '滨海新区中央大道888号',
                        tenant: '张三',
                        idCard: '110101199001011234',
                        phone: '13800138000',
                        startDate: '2023-01-01',
                        endDate: '2026-01-01',
                        rent: 800,
                        status: '执行中',
                        signDate: '2022-12-25',
                        roomType: '一室一厅',
                        area: 45,
                        progress: 35,
                        activities: [
                            { content: '合同创建', timestamp: '2022-12-20 10:00', type: 'primary' },
                            { content: '电子签约完成', timestamp: '2022-12-25 15:30', type: 'success' },
                            { content: '房屋交接', timestamp: '2022-12-30 09:15', type: 'success' },
                            { content: '首月租金支付', timestamp: '2023-01-05 14:20', type: 'success' }
                        ],
                        cohabitants: [
                            { name: '李四', relation: '配偶', idCard: '110101199102024567', phone: '13900139000', status: '已授权' }
                        ]
                    },
                    {
                        id: 'CT002',
                        houseTitle: '龙湖·椿山公租房',
                        address: '武清区京津路123号',
                        tenant: '王五',
                        idCard: '120101199203035678',
                        phone: '13700137000',
                        startDate: '2023-02-01',
                        endDate: '2026-02-01',
                        rent: 1200,
                        status: '待签约',
                        roomType: '两室一厅',
                        area: 60
                    },
                    {
                        id: 'CT003',
                        houseTitle: '万科·城市花园公租房',
                        address: '南开区卫津路456号',
                        tenant: '赵六',
                        idCard: '130101199304047890',
                        phone: '13600136000',
                        startDate: '2022-10-01',
                        endDate: '2025-10-01',
                        rent: 1000,
                        status: '执行中',
                        signDate: '2022-09-25',
                        roomType: '两室一厅',
                        area: 55,
                        progress: 50,
                        activities: [
                            { content: '合同创建', timestamp: '2022-09-20 11:00', type: 'primary' },
                            { content: '电子签约完成', timestamp: '2022-09-25 16:00', type: 'success' },
                            { content: '房屋交接', timestamp: '2022-09-30 10:00', type: 'success' },
                            { content: '首月租金支付', timestamp: '2022-10-05 09:30', type: 'success' }
                        ],
                        cohabitants: [
                            { name: '钱七', relation: '子女', idCard: '130101201504041234', phone: '13500135000', status: '已授权' },
                            { name: '孙八', relation: '父母', idCard: '130101196504041234', phone: '13400134000', status: '未授权' }
                        ]
                    },
                    {
                        id: 'CT004',
                        houseTitle: '保利·未来城公租房',
                        address: '河西区友谊路789号',
                        tenant: '周九',
                        idCard: '140101199405051122',
                        phone: '13300133000',
                        startDate: '2022-06-01',
                        endDate: '2025-06-01',
                        rent: 950,
                        status: '已到期',
                        signDate: '2022-05-25',
                        roomType: '一室一厅',
                        area: 50,
                        progress: 100,
                        activities: [
                            { content: '合同创建', timestamp: '2022-05-20 14:00', type: 'primary' },
                            { content: '电子签约完成', timestamp: '2022-05-25 11:20', type: 'success' },
                            { content: '房屋交接', timestamp: '2022-05-30 15:45', type: 'success' },
                            { content: '合同到期', timestamp: '2022-06-01 00:00', type: 'warning' }
                        ]
                    }
                ],
                currentContract: null,
                contractDetailVisible: false,
                showContractDetail: false,
                showSignContract: false,
                
                // 签约相关数据
                currentContractForSigning: null,
                signStep: 0,
                contractRead: false,
                verificationComplete: false,
                idVerification: {
                    name: '王五',
                    idNumber: '120101199203035678'
                },
                signatureConfirm: false,
                
                // 共同居住人相关数据
                cohabitantDialogVisible: false,
                cohabitantForm: {
                    name: '',
                    relation: '',
                    idCard: '',
                    phone: ''
                },
                cohabitantFormRules: {
                    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
                    relation: [{ required: true, message: '请选择关系', trigger: 'change' }],
                    idCard: [{ required: true, message: '请输入身份证号', trigger: 'blur' }],
                    phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }]
                },
                
                // 终止合同相关数据
                terminateDialogVisible: false,
                terminateForm: {
                    reason: '',
                    description: '',
                    date: ''
                },
                terminateFormRules: {
                    reason: [{ required: true, message: '请选择终止原因', trigger: 'change' }],
                    description: [{ required: true, message: '请输入具体说明', trigger: 'blur' }],
                    date: [{ type: 'date', required: true, message: '请选择终止日期', trigger: 'change' }]
                },
                
                // 租金管理相关数据
                paymentFilterForm: {
                    status: '',
                    month: '',
                    tenant: '',
                    address: ''
                },

                // 账单列表
                bills: [
                    {
                        id: 'B202401001',
                        tenant: '张三',
                        community: '融创智慧公租房小区',
                        building: '1',
                        unit: '1',
                        room: '101',
                        month: '2024-01',
                        rentAmount: 800,
                        utilityAmount: 150,
                        totalAmount: 950,
                        dueDate: '2024-01-31',
                        status: '已支付'
                    },
                    {
                        id: 'B202401002',
                        tenant: '李四',
                        community: '龙湖椿山公租房小区',
                        building: '2',
                        unit: '1',
                        room: '201',
                        month: '2024-01',
                        rentAmount: 1200,
                        utilityAmount: 200,
                        totalAmount: 1400,
                        dueDate: '2024-01-31',
                        status: '待支付'
                    },
                    {
                        id: 'B202401003',
                        tenant: '王五',
                        community: '万科城市花园公租房',
                        building: '3',
                        unit: '1',
                        room: '401',
                        month: '2024-01',
                        rentAmount: 1500,
                        utilityAmount: 180,
                        totalAmount: 1680,
                        dueDate: '2024-01-31',
                        status: '已逾期'
                    }
                ],

                // 账单分页数据
                billCurrentPage: 1,
                billPageSize: 10,
                totalBills: 3,

                // 账单表单相关
                showBillDialog: false,
                billForm: {
                    rentRecordId: '',
                    month: '',
                    rentAmount: 0,
                    utilityAmount: 0,
                    dueDate: '',
                    remark: ''
                },
                billRules: {
                    rentRecordId: [{ required: true, message: '请选择租户', trigger: 'change' }],
                    month: [{ required: true, message: '请选择账单月份', trigger: 'change' }],
                    rentAmount: [{ required: true, message: '请输入租金金额', trigger: 'blur' }],
                    dueDate: [{ required: true, message: '请选择到期日期', trigger: 'change' }]
                },

                // 批量生成账单相关
                showBatchBillDialog: false,
                batchBillForm: {
                    month: '',
                    dueDate: '',
                    scope: 'all',
                    community: ''
                },
                batchBillRules: {
                    month: [{ required: true, message: '请选择账单月份', trigger: 'change' }],
                    dueDate: [{ required: true, message: '请选择到期日期', trigger: 'change' }]
                },
                paymentCurrentPage: 1,
                paymentPageSize: 10,
                totalBills: 50,
                billDetailVisible: false,
                currentBill: null,
                paymentMethod: 'wechat',
                showBillPayment: false,
                paymentStep: 0,
                bills: [
                    {
                        id: 'BILL001',
                        houseTitle: '融创·智慧公租房',
                        address: '滨海新区中央大道888号',
                        tenant: '张三',
                        phone: '13800138000',
                        month: '2023-06',
                        dueDate: '2023-06-05',
                        totalAmount: 800,
                        status: '已支付',
                        paymentTime: '2023-06-03 14:25:36',
                        paymentMethod: 'wechat',
                        transactionId: '202306031425360001',
                        items: [
                            { name: '房租', amount: 800, description: '2023年6月房租' }
                        ]
                    },
                    {
                        id: 'BILL002',
                        houseTitle: '融创·智慧公租房',
                        address: '滨海新区中央大道888号',
                        tenant: '张三',
                        phone: '13800138000',
                        month: '2023-07',
                        dueDate: '2023-07-05',
                        totalAmount: 950,
                        status: '待支付',
                        items: [
                            { name: '房租', amount: 800, description: '2023年7月房租' },
                            { name: '水费', amount: 50, description: '2023年6月水费' },
                            { name: '电费', amount: 100, description: '2023年6月电费' }
                        ]
                    },
                    {
                        id: 'BILL003',
                        houseTitle: '龙湖·椿山公租房',
                        address: '武清区京津路123号',
                        tenant: '王五',
                        phone: '13700137000',
                        month: '2023-06',
                        dueDate: '2023-06-05',
                        totalAmount: 1200,
                        status: '已支付',
                        paymentTime: '2023-06-04 09:15:22',
                        paymentMethod: 'alipay',
                        transactionId: '202306040915220002',
                        items: [
                            { name: '房租', amount: 1200, description: '2023年6月房租' }
                        ]
                    },
                    {
                        id: 'BILL004',
                        houseTitle: '万科·城市花园公租房',
                        address: '南开区卫津路456号',
                        tenant: '赵六',
                        phone: '13600136000',
                        month: '2023-06',
                        dueDate: '2023-06-05',
                        totalAmount: 1150,
                        status: '已逾期',
                        overdueDays: 15,
                        lateFee: 57.5,
                        items: [
                            { name: '房租', amount: 1000, description: '2023年6月房租' },
                            { name: '物业费', amount: 150, description: '2023年6月物业费' }
                        ]
                    }
                ],
                
                // 缴费记录相关数据
                paymentHistoryFilterForm: {
                    dateRange: [],
                    method: ''
                },
                historyCurrentPage: 1,
                historyPageSize: 10,
                totalHistory: 30,
                paymentHistory: [
                    {
                        id: 'BILL001',
                        houseTitle: '融创·智慧公租房',
                        month: '2023-06',
                        paymentTime: '2023-06-03 14:25:36',
                        amount: 800,
                        paymentMethod: 'wechat'
                    },
                    {
                        id: 'BILL003',
                        houseTitle: '龙湖·椿山公租房',
                        month: '2023-06',
                        paymentTime: '2023-06-04 09:15:22',
                        amount: 1200,
                        paymentMethod: 'alipay'
                    },
                    {
                        id: 'PAY001',
                        houseTitle: '融创·智慧公租房',
                        month: '2023-05',
                        paymentTime: '2023-05-02 10:35:46',
                        amount: 800,
                        paymentMethod: 'bank'
                    },
                    {
                        id: 'PAY002',
                        houseTitle: '龙湖·椿山公租房',
                        month: '2023-05',
                        paymentTime: '2023-05-03 16:42:18',
                        amount: 1200,
                        paymentMethod: 'offline'
                    }
                ],
                
                // 缴费统计相关数据
                paymentStatsYear: '2023',
                monthlyStats: [
                    { month: '1月', amount: 45000 },
                    { month: '2月', amount: 42000 },
                    { month: '3月', amount: 48000 },
                    { month: '4月', amount: 46000 },
                    { month: '5月', amount: 44000 },
                    { month: '6月', amount: 47000 },
                    { month: '7月', amount: 49000 },
                    { month: '8月', amount: 43000 },
                    { month: '9月', amount: 45000 },
                    { month: '10月', amount: 48000 },
                    { month: '11月', amount: 46000 },
                    { month: '12月', amount: 50000 }
                ],
                
                // 筛选表单
                filterForm: {
                    area: '',
                    roomType: '',
                    priceMin: '',
                    priceMax: '',
                },
                
                // 房源筛选表单
                houseFilterForm: {
                    community: '',
                    building: '',
                    unit: '',
                    room: '',
                    status: ''
                },

                // 房源列表
                houses: [
                    {
                        id: 1,
                        community: '融创智慧公租房小区',
                        building: '1',
                        unit: '1',
                        room: '101',
                        roomType: '一室一厅',
                        area: 45,
                        price: 800,
                        status: '可租'
                    },
                    {
                        id: 2,
                        community: '融创智慧公租房小区',
                        building: '1',
                        unit: '1',
                        room: '102',
                        roomType: '两室一厅',
                        area: 60,
                        price: 1200,
                        status: '可租'
                    },
                    {
                        id: 3,
                        community: '龙湖椿山公租房小区',
                        building: '2',
                        unit: '1',
                        room: '201',
                        roomType: '两室一厅',
                        area: 55,
                        price: 1000,
                        status: '可租'
                    },
                    {
                        id: 4,
                        community: '龙湖椿山公租房小区',
                        building: '2',
                        unit: '2',
                        room: '301',
                        roomType: '一室一厅',
                        area: 50,
                        price: 950,
                        status: '已租'
                    },
                    {
                        id: 5,
                        community: '万科城市花园公租房',
                        building: '3',
                        unit: '1',
                        room: '401',
                        roomType: '三室一厅',
                        area: 75,
                        price: 1500,
                        status: '可租'
                    },
                    {
                        id: 6,
                        community: '万科城市花园公租房',
                        building: '3',
                        unit: '2',
                        room: '501',
                        roomType: '两室一厅',
                        area: 65,
                        price: 1100,
                        status: '已租'
                    }
                ],

                // 房源分页数据
                houseCurrentPage: 1,
                housePageSize: 10,
                totalHouses: 6,

                // 房源表单相关
                showHouseDialog: false,
                houseForm: {
                    id: null,
                    community: '',
                    building: '',
                    unit: '',
                    room: '',
                    roomType: '',
                    area: 0,
                    price: 0,
                    status: '可租'
                },
                houseRules: {
                    community: [{ required: true, message: '请输入小区名称', trigger: 'blur' }],
                    building: [{ required: true, message: '请输入幢号', trigger: 'blur' }],
                    unit: [{ required: true, message: '请输入单元号', trigger: 'blur' }],
                    room: [{ required: true, message: '请输入房间号', trigger: 'blur' }],
                    roomType: [{ required: true, message: '请选择户型', trigger: 'change' }],
                    area: [{ required: true, message: '请输入面积', trigger: 'blur' }],
                    price: [{ required: true, message: '请输入月租金', trigger: 'blur' }],
                    status: [{ required: true, message: '请选择状态', trigger: 'change' }]
                },

                // 批量生成相关
                showBatchGenerateDialog: false,
                batchForm: {
                    community: '',
                    buildingStart: 1,
                    buildingEnd: 1,
                    unitCount: 2,
                    roomsPerFloor: 4,
                    floors: 6,
                    roomType: '两室一厅',
                    area: 60,
                    price: 1000
                },
                batchRules: {
                    community: [{ required: true, message: '请输入小区名称', trigger: 'blur' }],
                    buildingStart: [{ required: true, message: '请输入起始幢号', trigger: 'blur' }],
                    buildingEnd: [{ required: true, message: '请输入结束幢号', trigger: 'blur' }],
                    unitCount: [{ required: true, message: '请输入单元数量', trigger: 'blur' }],
                    roomsPerFloor: [{ required: true, message: '请输入每层房间数', trigger: 'blur' }],
                    floors: [{ required: true, message: '请输入楼层数', trigger: 'blur' }],
                    roomType: [{ required: true, message: '请选择户型', trigger: 'change' }],
                    area: [{ required: true, message: '请输入面积', trigger: 'blur' }],
                    price: [{ required: true, message: '请输入月租金', trigger: 'blur' }]
                },

                // 人员管理相关数据
                personCurrentView: 'applicant', // 'applicant' | 'family'
                currentApplicantId: null,
                currentApplicantName: '',

                // 主申请人相关数据
                applicantFilterForm: {
                    name: '',
                    idCard: '',
                    status: '',
                    dateRange: []
                },
                applicants: [
                    {
                        id: 1,
                        name: '张三',
                        gender: '男',
                        age: 35,
                        idCard: '120101198801011234',
                        phone: '13800138001',
                        address: '天津市南开区卫津路123号',
                        income: 5000,
                        familySize: 3,
                        applyDate: '2023-10-15',
                        status: '已通过',
                        remark: '符合公租房申请条件'
                    },
                    {
                        id: 2,
                        name: '李四',
                        gender: '女',
                        age: 28,
                        idCard: '120102199501021234',
                        phone: '13800138002',
                        address: '天津市河西区友谊路456号',
                        income: 4200,
                        familySize: 2,
                        applyDate: '2023-10-20',
                        status: '待审核',
                        remark: '新申请用户'
                    },
                    {
                        id: 3,
                        name: '王五',
                        gender: '男',
                        age: 42,
                        idCard: '120103198101031234',
                        phone: '13800138003',
                        address: '天津市武清区京津路789号',
                        income: 3800,
                        familySize: 4,
                        applyDate: '2023-09-28',
                        status: '已配租',
                        remark: '已分配房源'
                    },
                    {
                        id: 4,
                        name: '赵六',
                        gender: '女',
                        age: 31,
                        idCard: '120104199201041234',
                        phone: '13800138004',
                        address: '天津市滨海新区中央大道321号',
                        income: 4500,
                        familySize: 1,
                        applyDate: '2023-11-02',
                        status: '已拒绝',
                        remark: '收入超标'
                    }
                ],
                applicantCurrentPage: 1,
                applicantPageSize: 10,
                totalApplicants: 4,
                showApplicantDialog: false,
                applicantForm: {
                    id: null,
                    name: '',
                    gender: '男',
                    age: 18,
                    idCard: '',
                    phone: '',
                    address: '',
                    income: 0,
                    familySize: 1,
                    status: '待审核',
                    remark: ''
                },
                applicantRules: {
                    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
                    gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
                    age: [{ required: true, message: '请输入年龄', trigger: 'blur' }],
                    idCard: [
                        { required: true, message: '请输入身份证号', trigger: 'blur' },
                        { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '身份证号格式不正确', trigger: 'blur' }
                    ],
                    phone: [
                        { required: true, message: '请输入联系电话', trigger: 'blur' },
                        { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
                    ],
                    address: [{ required: true, message: '请输入现住址', trigger: 'blur' }],
                    income: [{ required: true, message: '请输入月收入', trigger: 'blur' }],
                    familySize: [{ required: true, message: '请输入家庭人数', trigger: 'blur' }],
                    status: [{ required: true, message: '请选择申请状态', trigger: 'change' }]
                },

                // 家庭成员相关数据
                familyFilterForm: {
                    name: '',
                    applicantId: '',
                    relationship: ''
                },
                familyMembers: [
                    {
                        id: 1,
                        name: '张丽',
                        gender: '女',
                        age: 32,
                        idCard: '120101198901011234',
                        phone: '13800138011',
                        relationship: '配偶',
                        applicantId: 1,
                        applicantName: '张三',
                        occupation: '教师',
                        income: 4000,
                        remark: '小学教师'
                    },
                    {
                        id: 2,
                        name: '张小明',
                        gender: '男',
                        age: 8,
                        idCard: '120101201501011234',
                        phone: '',
                        relationship: '子女',
                        applicantId: 1,
                        applicantName: '张三',
                        occupation: '学生',
                        income: 0,
                        remark: '小学三年级'
                    },
                    {
                        id: 3,
                        name: '李明',
                        gender: '男',
                        age: 30,
                        idCard: '120102199301021234',
                        phone: '13800138012',
                        relationship: '配偶',
                        applicantId: 2,
                        applicantName: '李四',
                        occupation: '工程师',
                        income: 6000,
                        remark: '软件工程师'
                    },
                    {
                        id: 4,
                        name: '王小红',
                        gender: '女',
                        age: 38,
                        idCard: '120103198501031234',
                        phone: '13800138013',
                        relationship: '配偶',
                        applicantId: 3,
                        applicantName: '王五',
                        occupation: '会计',
                        income: 3500,
                        remark: '企业会计'
                    }
                ],
                familyCurrentPage: 1,
                familyPageSize: 10,
                totalFamilyMembers: 4,
                showFamilyDialog: false,
                familyForm: {
                    id: null,
                    name: '',
                    gender: '男',
                    age: 0,
                    idCard: '',
                    phone: '',
                    relationship: '',
                    applicantId: '',
                    occupation: '',
                    income: 0,
                    remark: ''
                },
                familyRules: {
                    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
                    gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
                    age: [{ required: true, message: '请输入年龄', trigger: 'blur' }],
                    idCard: [
                        { required: true, message: '请输入身份证号', trigger: 'blur' },
                        { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '身份证号格式不正确', trigger: 'blur' }
                    ],
                    relationship: [{ required: true, message: '请选择与申请人关系', trigger: 'change' }],
                    applicantId: [{ required: true, message: '请选择主申请人', trigger: 'change' }]
                },

                // 批量导入相关数据
                showApplicantImportDialog: false,
                showFamilyImportDialog: false,
                applicantFileList: [],
                familyFileList: [],

                // 配租管理相关数据
                rentFilterForm: {
                    address: '',
                    idCard: '',
                    name: ''
                },

                // 配租记录列表
                rentRecords: [
                    {
                        id: 1,
                        applicantName: '张三',
                        idCard: '110101199001011234',
                        phone: '13800138001',
                        community: '融创智慧公租房小区',
                        building: '1',
                        unit: '1',
                        room: '101',
                        roomType: '一室一厅',
                        area: 45,
                        price: 800,
                        rentDate: '2024-01-15'
                    },
                    {
                        id: 2,
                        applicantName: '李四',
                        idCard: '110101199002021234',
                        phone: '13800138002',
                        community: '龙湖椿山公租房小区',
                        building: '2',
                        unit: '1',
                        room: '201',
                        roomType: '两室一厅',
                        area: 60,
                        price: 1200,
                        rentDate: '2024-02-20'
                    },
                    {
                        id: 3,
                        applicantName: '王五',
                        idCard: '110101199003031234',
                        phone: '13800138003',
                        community: '万科城市花园公租房',
                        building: '3',
                        unit: '1',
                        room: '401',
                        roomType: '三室一厅',
                        area: 75,
                        price: 1500,
                        rentDate: '2024-03-10'
                    }
                ],

                // 配租分页数据
                rentCurrentPage: 1,
                rentPageSize: 10,
                totalRentRecords: 3,

                // 配租表单相关
                showRentDialog: false,
                rentForm: {
                    applicantId: '',
                    community: '',
                    building: '',
                    unit: '',
                    room: '',
                    rentDate: '',
                    remark: ''
                },
                rentRules: {
                    applicantId: [{ required: true, message: '请选择主申请人', trigger: 'change' }],
                    community: [{ required: true, message: '请选择小区', trigger: 'change' }],
                    building: [{ required: true, message: '请选择幢', trigger: 'change' }],
                    unit: [{ required: true, message: '请选择单元', trigger: 'change' }],
                    room: [{ required: true, message: '请选择房间', trigger: 'change' }],
                    rentDate: [{ required: true, message: '请选择配租时间', trigger: 'change' }]
                },

                // 批量导入相关
                showRentImportDialog: false,
                rentFileList: [],

                // 调房相关
                showTransferDialog: false,
                transferForm: {
                    rentRecordId: '',
                    reason: '',
                    targetHouseId: '',
                    transferDate: '',
                    remark: ''
                },
                transferRules: {
                    rentRecordId: [{ required: true, message: '请选择租户', trigger: 'change' }],
                    reason: [{ required: true, message: '请选择调房原因', trigger: 'change' }],
                    targetHouseId: [{ required: true, message: '请选择目标房源', trigger: 'change' }],
                    transferDate: [{ required: true, message: '请选择调房时间', trigger: 'change' }]
                },

                // 换房相关
                showExchangeDialog: false,
                exchangeForm: {
                    rentRecordIdA: '',
                    rentRecordIdB: '',
                    reason: '',
                    exchangeDate: '',
                    remark: ''
                },
                exchangeRules: {
                    rentRecordIdA: [{ required: true, message: '请选择租户A', trigger: 'change' }],
                    rentRecordIdB: [{ required: true, message: '请选择租户B', trigger: 'change' }],
                    reason: [{ required: true, message: '请选择换房原因', trigger: 'change' }],
                    exchangeDate: [{ required: true, message: '请选择换房时间', trigger: 'change' }]
                },

                // 主申请人变更相关
                showChangeApplicantDialog: false,
                changeApplicantForm: {
                    rentRecordId: '',
                    newApplicantId: '',
                    reason: '',
                    changeDate: '',
                    remark: ''
                },
                changeApplicantRules: {
                    rentRecordId: [{ required: true, message: '请选择配租记录', trigger: 'change' }],
                    newApplicantId: [{ required: true, message: '请选择新主申请人', trigger: 'change' }],
                    reason: [{ required: true, message: '请选择变更原因', trigger: 'change' }],
                    changeDate: [{ required: true, message: '请选择变更时间', trigger: 'change' }]
                },
                applyForm: {
                    applicantName: '',
                    idCard: '',
                    phone: '',
                    familySize: 1,
                    preferredArea: '',
                    preferredRoomType: '',
                    monthlyIncome: '',
                    reason: '',
                    fileList: [],
                    agreed: false
                },
                editApplicationForm: {
                    applicantName: '',
                    idCard: '',
                    phone: '',
                    roomType: '',
                    houseAddress: ''
                },
                applications: [
                    {
                        id: 'APP001',
                        applicantName: '张三',
                        idCard: '110101199001011234',
                        phone: '13800138001',
                        houseAddress: '滨海新区海河东路公租房小区A栋',
                        roomType: '两室一厅',
                        applyDate: '2023-10-15',
                        status: '待审核'
                    },
                    {
                        id: 'APP002',
                        applicantName: '李四',
                        idCard: '110101199002021234',
                        phone: '13800138002',
                        houseAddress: '武清区建设路公租房小区B栋',
                        roomType: '一室一厅',
                        applyDate: '2023-10-12',
                        status: '审核通过'
                    },
                    {
                        id: 'APP003',
                        applicantName: '王五',
                        idCard: '110101199003031234',
                        phone: '13800138003',
                        houseAddress: '南开区长江道公租房小区C栋',
                        roomType: '三室一厅',
                        applyDate: '2023-10-10',
                        status: '已配租'
                    },
                    {
                        id: 'APP004',
                        applicantName: '赵六',
                        idCard: '110101199004041234',
                        phone: '13800138004',
                        houseAddress: '河西区友谊路公租房小区D栋',
                        roomType: '两室一厅',
                        applyDate: '2023-10-08',
                        status: '审核拒绝'
                    }
                ],
                applyCurrentPage: 1,
                applyPageSize: 10,
                totalApplications: 4,

                // 合同管理相关数据
                contractActiveTab: 'list',
                contractFilterForm: {
                    status: '',
                    dateRange: null,
                    tenant: ''
                },
                contracts: [
                    {
                        id: 'CT001',
                        houseTitle: '滨海新区海河东路公租房小区A栋101',
                        address: '滨海新区海河东路123号A栋101室',
                        tenant: '张三',
                        phone: '13800138001',
                        startDate: '2023-11-01',
                        endDate: '2026-10-31',
                        rent: 800,
                        status: '执行中',
                        progress: 75,
                        activities: [
                            { content: '合同签署完成', timestamp: '2023-11-01 10:30', type: 'success' },
                            { content: '首次缴费完成', timestamp: '2023-11-01 14:20', type: 'success' },
                            { content: '房屋交接完成', timestamp: '2023-11-02 09:15', type: 'success' },
                            { content: '11月份租金缴费', timestamp: '2023-11-05 16:45', type: 'success' }
                        ],
                        cohabitants: [
                            { name: '李梅', relation: '配偶', idCard: '110101199002021234', phone: '13800138002' },
                            { name: '张小明', relation: '子女', idCard: '110101201501011234', phone: '' }
                        ]
                    },
                    {
                        id: 'CT002',
                        houseTitle: '武清区建设路公租房小区B栋201',
                        address: '武清区建设路456号B栋201室',
                        tenant: '李四',
                        phone: '13800138003',
                        startDate: '2023-10-15',
                        endDate: '2026-10-14',
                        rent: 650,
                        status: '待签约',
                        progress: 0,
                        activities: [
                            { content: '合同生成完成', timestamp: '2023-10-10 14:30', type: 'success' },
                            { content: '等待承租人签约', timestamp: '2023-10-10 14:35', type: 'warning' }
                        ],
                        cohabitants: []
                    },
                    {
                        id: 'CT003',
                        houseTitle: '南开区长江道公租房小区C栋301',
                        tenant: '王五',
                        startDate: '2023-09-01',
                        endDate: '2026-08-31',
                        rent: 950,
                        status: '执行中'
                    },
                    {
                        id: 'CT004',
                        houseTitle: '河西区友谊路公租房小区D栋401',
                        tenant: '刘七',
                        startDate: '2022-08-01',
                        endDate: '2025-07-31',
                        rent: 750,
                        status: '已到期'
                    },
                    {
                        id: 'CT005',
                        houseTitle: '和平区南京路公租房小区E栋501',
                        tenant: '陈八',
                        startDate: '2023-06-01',
                        endDate: '2024-05-31',
                        rent: 900,
                        status: '已终止'
                    }
                ],
                contractCurrentPage: 1,
                contractPageSize: 10,
                totalContracts: 5,
                currentContractForSigning: null,
                signStep: 0,

                // 租金管理相关数据
                paymentActiveTab: 'list',
                currentBill: null,
                paymentFilterForm: {
                    status: '',
                    month: null,
                    tenant: ''
                },
                bills: [
                    {
                        id: 'BILL001',
                        houseTitle: '滨海新区海河东路公租房小区A栋101',
                        address: '滨海新区海河东路123号A栋101室',
                        tenant: '张三',
                        phone: '13800138001',
                        month: '2023-11',
                        dueDate: '2023-11-05',
                        totalAmount: 850,
                        rent: 800,
                        utilities: 50,
                        status: '待支付',
                        items: [
                            { name: '房租', amount: 800, description: '2023年11月房租' },
                            { name: '物业费', amount: 30, description: '2023年11月物业管理费' },
                            { name: '水费', amount: 15, description: '2023年11月用水费用' },
                            { name: '电费', amount: 5, description: '2023年11月用电费用' }
                        ]
                    },
                    {
                        id: 'BILL002',
                        houseTitle: '武清区建设路公租房小区B栋201',
                        tenant: '李四',
                        month: '2023-11',
                        dueDate: '2023-11-05',
                        totalAmount: 700,
                        rent: 650,
                        utilities: 50,
                        status: '已支付'
                    },
                    {
                        id: 'BILL003',
                        houseTitle: '南开区长江道公租房小区C栋301',
                        tenant: '王五',
                        month: '2023-10',
                        dueDate: '2023-10-05',
                        totalAmount: 1000,
                        rent: 950,
                        utilities: 50,
                        status: '已逾期'
                    },
                    {
                        id: 'BILL004',
                        houseTitle: '河西区友谊路公租房小区D栋401',
                        tenant: '刘七',
                        month: '2023-11',
                        dueDate: '2023-11-05',
                        totalAmount: 800,
                        rent: 750,
                        utilities: 50,
                        status: '已支付'
                    }
                ],
                paymentRecords: [
                    {
                        id: 'PAY001',
                        billId: 'BILL002',
                        tenant: '李四',
                        amount: 700,
                        paymentDate: '2023-11-03',
                        paymentMethod: '微信支付',
                        status: '成功'
                    },
                    {
                        id: 'PAY002',
                        billId: 'BILL004',
                        tenant: '刘七',
                        amount: 800,
                        paymentDate: '2023-11-02',
                        paymentMethod: '银行转账',
                        status: '成功'
                    }
                ],
                paymentStats: {
                    totalBills: 4,
                    paidBills: 2,
                    unpaidBills: 1,
                    overdueBills: 1,
                    totalAmount: 3350,
                    paidAmount: 1500,
                    unpaidAmount: 1850
                },
                billCurrentPage: 1,
                billPageSize: 10,
                totalBills: 4,
                paymentCurrentPage: 1,
                paymentPageSize: 10,
                totalPayments: 2,
                historyCurrentPage: 1,
                historyPageSize: 10,
                totalHistory: 2,

                // 维修服务相关数据
                repairActiveTab: 'list',
                showRepairDialog: false,
                showRepairDetailDialog: false,
                showAddWorkerDialog: false,
                showWorkerDetailDialog: false,
                showEditWorkerDialog: false,
                currentRepair: null,
                currentWorker: null,
                repairSearch: {
                    status: '',
                    type: '',
                    urgency: ''
                },
                repairForm: {
                    houseAddress: '',
                    tenant: '',
                    type: '',
                    urgency: '',
                    description: '',
                    phone: '',
                    fileList: []
                },
                repairs: [
                    {
                        id: 'REP001',
                        houseAddress: '滨海新区海河东路公租房小区A栋101',
                        tenant: '张三',
                        phone: '13800138001',
                        type: '水电',
                        description: '厨房水龙头漏水，需要更换',
                        urgency: '一般',
                        createTime: '2023-11-15',
                        status: '待处理'
                    },
                    {
                        id: 'REP002',
                        houseAddress: '武清区建设路公租房小区B栋201',
                        tenant: '李四',
                        phone: '13800138003',
                        type: '家具',
                        description: '卧室衣柜门脱落',
                        urgency: '不急',
                        createTime: '2023-11-14',
                        status: '处理中',
                        worker: {
                            name: '王师傅',
                            phone: '13900139001',
                            specialty: '家具维修',
                            rating: 4.5
                        }
                    },
                    {
                        id: 'REP003',
                        houseAddress: '南开区长江道公租房小区C栋301',
                        tenant: '王五',
                        type: '结构',
                        description: '客厅墙面出现裂缝',
                        urgency: '紧急',
                        createTime: '2023-11-13',
                        status: '已完成'
                    }
                ],
                repairWorkers: [
                    {
                        id: 'W001',
                        name: '刘师傅',
                        phone: '13800138001',
                        specialty: '水电维修',
                        workload: 3,
                        rating: 4.8,
                        status: '在线'
                    },
                    {
                        id: 'W002',
                        name: '陈师傅',
                        phone: '13800138002',
                        specialty: '家具维修',
                        workload: 2,
                        rating: 4.6,
                        status: '忙碌'
                    }
                ],
                repairCurrentPage: 1,
                repairPageSize: 10,
                totalRepairs: 3,

                // 维修员相关数据
                workerForm: {
                    name: '',
                    phone: '',
                    specialty: '',
                    experience: 0
                },
                editWorkerForm: {
                    name: '',
                    phone: '',
                    specialty: '',
                    status: ''
                },

                // 退租管理相关数据
                exitActiveTab: 'list',
                showExitDialog: false,
                showExitDetailDialog: false,
                currentExit: null,
                exitSearch: {
                    status: '',
                    reason: '',
                    tenant: ''
                },
                exitForm: {
                    houseAddress: '',
                    tenant: '',
                    reason: '',
                    expectedDate: '',
                    description: '',
                    phone: '',
                    fileList: []
                },
                exits: [
                    {
                        id: 'EXIT001',
                        houseAddress: '滨海新区海河东路公租房小区A栋101',
                        tenant: '张三',
                        reason: '购房',
                        applyDate: '2023-11-10',
                        expectedDate: '2023-12-01',
                        deposit: 1600,
                        status: '待审核'
                    },
                    {
                        id: 'EXIT002',
                        houseAddress: '武清区建设路公租房小区B栋201',
                        tenant: '李四',
                        reason: '迁离',
                        applyDate: '2023-11-08',
                        expectedDate: '2023-11-30',
                        deposit: 1300,
                        status: '审核通过'
                    }
                ],
                inspections: [
                    {
                        id: 'INS001',
                        houseAddress: '武清区建设路公租房小区B栋201',
                        tenant: '李四',
                        inspector: '王验房员',
                        scheduledDate: '2023-11-25 14:00',
                        status: '待验房'
                    }
                ],
                deposits: [
                    {
                        id: 'DEP001',
                        tenant: '赵六',
                        houseAddress: '河西区友谊路公租房小区D栋401',
                        originalDeposit: 1500,
                        deduction: 200,
                        refundAmount: 1300,
                        status: '待退款'
                    }
                ],
                exitCurrentPage: 1,
                exitPageSize: 10,
                totalExits: 2,

                // 动态监管相关数据
                monitorActiveTab: 'credit',
                showCreditDetailDialog: false,
                showAdjustCreditDialog: false,
                showVerificationDetailDialog: false,
                showAlertDetailDialog: false,
                currentCredit: null,
                currentVerification: null,
                currentAlert: null,
                creditSearch: {
                    level: '',
                    tenant: ''
                },
                adjustCreditForm: {
                    type: '',
                    amount: 0,
                    reason: ''
                },
                creditStats: {
                    averageScore: 85.6,
                    lowCreditCount: 12,
                    excellentCount: 156
                },
                credits: [
                    {
                        tenant: '张三',
                        houseAddress: '滨海新区海河东路公租房小区A栋101',
                        score: 92,
                        level: '优秀',
                        lastUpdate: '2023-11-15'
                    },
                    {
                        tenant: '李四',
                        houseAddress: '武清区建设路公租房小区B栋201',
                        score: 78,
                        level: '良好',
                        lastUpdate: '2023-11-14'
                    },
                    {
                        tenant: '王五',
                        houseAddress: '南开区长江道公租房小区C栋301',
                        score: 65,
                        level: '一般',
                        lastUpdate: '2023-11-13'
                    }
                ],
                creditCurrentPage: 1,
                creditPageSize: 10,
                totalCredits: 3,

                verificationStats: {
                    totalChecked: 245,
                    passedCount: 198,
                    warningCount: 32,
                    failedCount: 15
                },
                verifications: [
                    {
                        tenant: '张三',
                        idCard: '110101199001011234',
                        houseAddress: '滨海新区海河东路公租房小区A栋101',
                        publicSecurity: '通过',
                        civilAffairs: '通过',
                        realEstate: '通过',
                        lastCheck: '2023-11-15 10:30'
                    },
                    {
                        tenant: '李四',
                        idCard: '110101199002021234',
                        houseAddress: '武清区建设路公租房小区B栋201',
                        publicSecurity: '通过',
                        civilAffairs: '异常',
                        realEstate: '通过',
                        lastCheck: '2023-11-14 15:20'
                    }
                ],
                verificationCurrentPage: 1,
                verificationPageSize: 10,
                totalVerifications: 2,

                alerts: [
                    {
                        id: 'ALT001',
                        tenant: '李四',
                        type: '收入超标',
                        description: '民政系统返回收入异常，月收入超过申请标准',
                        level: '高',
                        createTime: '2023-11-14 15:25',
                        status: '待处理'
                    },
                    {
                        id: 'ALT002',
                        tenant: '王五',
                        type: '新增房产',
                        description: '不动产系统检测到新增房产信息',
                        level: '中',
                        createTime: '2023-11-13 09:15',
                        status: '已处理'
                    }
                ]
            },
            methods: {
                // 切换标签页
                handleTabChange(tab) {
                    console.log('切换到标签页:', tab);
                    this.activeTab = tab;
                },
                
                // 筛选房源
                filterHouses() {
                    this.$message({
                        message: '筛选条件已应用',
                        type: 'success'
                    });
                },
                
                // 重置筛选条件
                resetFilter() {
                    this.filterForm = {
                        area: '',
                        roomType: '',
                        priceMin: '',
                        priceMax: '',
                    };
                },
                
                // ========== 房源管理相关方法 ==========

                // 搜索房源
                searchHouses() {
                    this.$message.success('搜索房源完成');
                    // 实际应用中会调用API进行搜索
                },

                // 重置房源筛选
                resetHouseFilter() {
                    this.houseFilterForm = {
                        community: '',
                        building: '',
                        unit: '',
                        room: '',
                        status: ''
                    };
                    this.$message.info('筛选条件已重置');
                },

                // 编辑房源
                editHouse(house) {
                    this.houseForm = { ...house };
                    this.showHouseDialog = true;
                },

                // 删除房源
                deleteHouse(house) {
                    this.$confirm(`确定要删除房源"${house.community} ${house.building}幢${house.unit}单元${house.room}室"吗？`, '删除确认', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        const index = this.houses.findIndex(h => h.id === house.id);
                        if (index > -1) {
                            this.houses.splice(index, 1);
                            this.totalHouses--;
                            this.$message.success('删除成功');
                        }
                    }).catch(() => {
                        this.$message.info('已取消删除');
                    });
                },

                // 保存房源
                saveHouse() {
                    this.$refs.houseForm.validate((valid) => {
                        if (valid) {
                            if (this.houseForm.id) {
                                // 编辑
                                const index = this.houses.findIndex(h => h.id === this.houseForm.id);
                                if (index > -1) {
                                    this.houses.splice(index, 1, { ...this.houseForm });
                                    this.$message.success('编辑成功');
                                }
                            } else {
                                // 新增
                                const newHouse = {
                                    ...this.houseForm,
                                    id: this.houses.length + 1
                                };
                                this.houses.unshift(newHouse);
                                this.totalHouses++;
                                this.$message.success('新增成功');
                            }
                            this.showHouseDialog = false;
                            this.resetHouseForm();
                        }
                    });
                },

                // 重置房源表单
                resetHouseForm() {
                    this.houseForm = {
                        id: null,
                        community: '',
                        building: '',
                        unit: '',
                        room: '',
                        roomType: '',
                        area: 0,
                        price: 0,
                        status: '可租'
                    };
                    if (this.$refs.houseForm) {
                        this.$refs.houseForm.resetFields();
                    }
                },

                // 获取房源对话框标题
                getHouseDialogTitle() {
                    return this.houseForm.id ? '编辑房源' : '新增房源';
                },

                // 房源分页变化
                handleHouseCurrentChange(val) {
                    this.houseCurrentPage = val;
                    this.$message.info(`加载第 ${val} 页房源数据`);
                },

                // 批量生成房源
                batchGenerateHouses() {
                    this.$refs.batchForm.validate((valid) => {
                        if (valid) {
                            if (this.batchForm.buildingStart > this.batchForm.buildingEnd) {
                                this.$message.error('起始幢号不能大于结束幢号');
                                return;
                            }

                            let generatedCount = 0;
                            let currentId = this.houses.length + 1;

                            // 生成房源
                            for (let building = this.batchForm.buildingStart; building <= this.batchForm.buildingEnd; building++) {
                                for (let unit = 1; unit <= this.batchForm.unitCount; unit++) {
                                    for (let floor = 1; floor <= this.batchForm.floors; floor++) {
                                        for (let roomIndex = 1; roomIndex <= this.batchForm.roomsPerFloor; roomIndex++) {
                                            const room = floor.toString().padStart(2, '0') + roomIndex.toString().padStart(2, '0');

                                            const newHouse = {
                                                id: currentId++,
                                                community: this.batchForm.community,
                                                building: building.toString(),
                                                unit: unit.toString(),
                                                room: room,
                                                roomType: this.batchForm.roomType,
                                                area: this.batchForm.area,
                                                price: this.batchForm.price,
                                                status: '可租'
                                            };

                                            this.houses.push(newHouse);
                                            generatedCount++;
                                        }
                                    }
                                }
                            }

                            this.totalHouses += generatedCount;
                            this.showBatchGenerateDialog = false;
                            this.resetBatchForm();
                            this.$message.success(`成功生成 ${generatedCount} 套房源`);
                        }
                    });
                },

                // 重置批量生成表单
                resetBatchForm() {
                    this.batchForm = {
                        community: '',
                        buildingStart: 1,
                        buildingEnd: 1,
                        unitCount: 2,
                        roomsPerFloor: 4,
                        floors: 6,
                        roomType: '两室一厅',
                        area: 60,
                        price: 1000
                    };
                    if (this.$refs.batchForm) {
                        this.$refs.batchForm.resetFields();
                    }
                },

                // 申请配租
                applyRent(id) {
                    this.currentHouse = this.houses.find(h => h.id === id);
                    this.activeTab = 'apply';
                },

                // ========== 人员管理相关方法 ==========

                // 主申请人相关方法
                searchApplicants() {
                    this.$message.success('搜索主申请人完成');
                    // 实际应用中会调用API进行搜索
                },

                resetApplicantFilter() {
                    this.applicantFilterForm = {
                        name: '',
                        idCard: '',
                        status: '',
                        dateRange: []
                    };
                    this.$message.info('筛选条件已重置');
                },

                viewApplicant(applicant) {
                    this.$alert(`
                        <div style="text-align: left;">
                            <p><strong>姓名：</strong>${applicant.name}</p>
                            <p><strong>性别：</strong>${applicant.gender}</p>
                            <p><strong>年龄：</strong>${applicant.age}岁</p>
                            <p><strong>身份证号：</strong>${applicant.idCard}</p>
                            <p><strong>联系电话：</strong>${applicant.phone}</p>
                            <p><strong>现住址：</strong>${applicant.address}</p>
                            <p><strong>月收入：</strong>${applicant.income.toLocaleString()}元</p>
                            <p><strong>家庭人数：</strong>${applicant.familySize}人</p>
                            <p><strong>申请时间：</strong>${applicant.applyDate}</p>
                            <p><strong>申请状态：</strong>${applicant.status}</p>
                            <p><strong>备注：</strong>${applicant.remark || '无'}</p>
                        </div>
                    `, '主申请人详情', {
                        dangerouslyUseHTMLString: true,
                        confirmButtonText: '关闭'
                    });
                },

                editApplicant(applicant) {
                    this.applicantForm = { ...applicant };
                    this.showApplicantDialog = true;
                },

                deleteApplicant(applicant) {
                    this.$confirm(`确定要删除主申请人"${applicant.name}"吗？`, '删除确认', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        const index = this.applicants.findIndex(a => a.id === applicant.id);
                        if (index > -1) {
                            this.applicants.splice(index, 1);
                            this.totalApplicants--;
                            this.$message.success('删除成功');
                        }
                    }).catch(() => {
                        this.$message.info('已取消删除');
                    });
                },

                viewFamilyMembers(applicant) {
                    this.currentApplicantId = applicant.id;
                    this.currentApplicantName = applicant.name;
                    this.personCurrentView = 'family';
                    this.familyFilterForm.applicantId = applicant.id;
                    this.$message.info(`已切换到${applicant.name}的家庭成员列表`);
                },

                // 返回主申请人列表
                backToApplicantList() {
                    this.personCurrentView = 'applicant';
                    this.currentApplicantId = null;
                    this.currentApplicantName = '';
                    this.familyFilterForm.applicantId = '';
                },

                saveApplicant() {
                    this.$refs.applicantForm.validate((valid) => {
                        if (valid) {
                            if (this.applicantForm.id) {
                                // 编辑
                                const index = this.applicants.findIndex(a => a.id === this.applicantForm.id);
                                if (index > -1) {
                                    this.applicants.splice(index, 1, { ...this.applicantForm });
                                    this.$message.success('编辑成功');
                                }
                            } else {
                                // 新增
                                const newApplicant = {
                                    ...this.applicantForm,
                                    id: this.applicants.length + 1,
                                    applyDate: new Date().toISOString().split('T')[0]
                                };
                                this.applicants.unshift(newApplicant);
                                this.totalApplicants++;
                                this.$message.success('新增成功');
                            }
                            this.showApplicantDialog = false;
                            this.resetApplicantForm();
                        }
                    });
                },

                resetApplicantForm() {
                    this.applicantForm = {
                        id: null,
                        name: '',
                        gender: '男',
                        age: 18,
                        idCard: '',
                        phone: '',
                        address: '',
                        income: 0,
                        familySize: 1,
                        status: '待审核',
                        remark: ''
                    };
                    if (this.$refs.applicantForm) {
                        this.$refs.applicantForm.resetFields();
                    }
                },

                getApplicantStatusType(status) {
                    const typeMap = {
                        '待审核': 'warning',
                        '已通过': 'success',
                        '已拒绝': 'danger',
                        '已配租': 'info'
                    };
                    return typeMap[status] || 'info';
                },

                handleApplicantCurrentChange(val) {
                    this.applicantCurrentPage = val;
                    this.$message.info(`加载第 ${val} 页主申请人数据`);
                },

                // 家庭成员相关方法
                searchFamilyMembers() {
                    this.$message.success('搜索家庭成员完成');
                    // 实际应用中会调用API进行搜索
                },

                resetFamilyFilter() {
                    this.familyFilterForm = {
                        name: '',
                        applicantId: '',
                        relationship: ''
                    };
                    this.$message.info('筛选条件已重置');
                },

                viewFamilyMember(member) {
                    this.$alert(`
                        <div style="text-align: left;">
                            <p><strong>姓名：</strong>${member.name}</p>
                            <p><strong>性别：</strong>${member.gender}</p>
                            <p><strong>年龄：</strong>${member.age}岁</p>
                            <p><strong>身份证号：</strong>${member.idCard}</p>
                            <p><strong>联系电话：</strong>${member.phone || '无'}</p>
                            <p><strong>与申请人关系：</strong>${member.relationship}</p>
                            <p><strong>主申请人：</strong>${member.applicantName}</p>
                            <p><strong>职业：</strong>${member.occupation || '无'}</p>
                            <p><strong>月收入：</strong>${member.income ? member.income.toLocaleString() + '元' : '无'}</p>
                            <p><strong>备注：</strong>${member.remark || '无'}</p>
                        </div>
                    `, '家庭成员详情', {
                        dangerouslyUseHTMLString: true,
                        confirmButtonText: '关闭'
                    });
                },

                editFamilyMember(member) {
                    this.familyForm = { ...member };
                    this.showFamilyDialog = true;
                },

                deleteFamilyMember(member) {
                    this.$confirm(`确定要删除家庭成员"${member.name}"吗？`, '删除确认', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        const index = this.familyMembers.findIndex(m => m.id === member.id);
                        if (index > -1) {
                            this.familyMembers.splice(index, 1);
                            this.totalFamilyMembers--;
                            this.$message.success('删除成功');
                        }
                    }).catch(() => {
                        this.$message.info('已取消删除');
                    });
                },

                saveFamilyMember() {
                    this.$refs.familyForm.validate((valid) => {
                        if (valid) {
                            // 获取主申请人姓名
                            const applicant = this.applicants.find(a => a.id === this.familyForm.applicantId);
                            if (!applicant) {
                                this.$message.error('请选择有效的主申请人');
                                return;
                            }

                            if (this.familyForm.id) {
                                // 编辑
                                const index = this.familyMembers.findIndex(m => m.id === this.familyForm.id);
                                if (index > -1) {
                                    this.familyMembers.splice(index, 1, {
                                        ...this.familyForm,
                                        applicantName: applicant.name
                                    });
                                    this.$message.success('编辑成功');
                                }
                            } else {
                                // 新增
                                const newMember = {
                                    ...this.familyForm,
                                    id: this.familyMembers.length + 1,
                                    applicantName: applicant.name
                                };
                                this.familyMembers.unshift(newMember);
                                this.totalFamilyMembers++;
                                this.$message.success('新增成功');
                            }
                            this.showFamilyDialog = false;
                            this.resetFamilyForm();
                        }
                    });
                },

                resetFamilyForm() {
                    this.familyForm = {
                        id: null,
                        name: '',
                        gender: '男',
                        age: 0,
                        idCard: '',
                        phone: '',
                        relationship: '',
                        applicantId: '',
                        occupation: '',
                        income: 0,
                        remark: ''
                    };
                    if (this.$refs.familyForm) {
                        this.$refs.familyForm.resetFields();
                    }
                },

                handleFamilyCurrentChange(val) {
                    this.familyCurrentPage = val;
                    this.$message.info(`加载第 ${val} 页家庭成员数据`);
                },

                getApplicantDialogTitle() {
                    return this.applicantForm.id ? '编辑主申请人' : '新增主申请人';
                },

                getFamilyDialogTitle() {
                    return this.familyForm.id ? '编辑家庭成员' : '新增家庭成员';
                },

                // 批量导入相关方法
                handleApplicantFileChange(file, fileList) {
                    this.applicantFileList = fileList;
                },

                handleFamilyFileChange(file, fileList) {
                    this.familyFileList = fileList;
                },

                downloadApplicantTemplate() {
                    // 模拟下载模板文件
                    this.$message.success('主申请人导入模板下载成功');
                    // 实际应用中会生成并下载Excel模板文件
                },

                downloadFamilyTemplate() {
                    // 模拟下载模板文件
                    this.$message.success('家庭成员导入模板下载成功');
                    // 实际应用中会生成并下载Excel模板文件
                },

                importApplicants() {
                    if (this.applicantFileList.length === 0) {
                        this.$message.error('请选择要导入的文件');
                        return;
                    }

                    // 模拟导入过程
                    this.$message.success('正在导入主申请人数据...');
                    setTimeout(() => {
                        // 模拟导入成功
                        const importCount = Math.floor(Math.random() * 50) + 10;
                        this.$message.success(`成功导入 ${importCount} 条主申请人记录`);
                        this.showApplicantImportDialog = false;
                        this.applicantFileList = [];
                        // 实际应用中会解析Excel文件并导入数据
                    }, 2000);
                },

                importFamilyMembers() {
                    if (this.familyFileList.length === 0) {
                        this.$message.error('请选择要导入的文件');
                        return;
                    }

                    // 模拟导入过程
                    this.$message.success('正在导入家庭成员数据...');
                    setTimeout(() => {
                        // 模拟导入成功
                        const importCount = Math.floor(Math.random() * 30) + 5;
                        this.$message.success(`成功导入 ${importCount} 条家庭成员记录`);
                        this.showFamilyImportDialog = false;
                        this.familyFileList = [];
                        // 实际应用中会解析Excel文件并导入数据
                    }, 2000);
                },

                // 分页变化
                handleCurrentChange(val) {
                    this.currentPage = val;
                    this.$message({
                        message: `加载第 ${val} 页数据`,
                        type: 'info'
                    });
                },

                // ========== 配租管理相关方法 ==========

                // 搜索配租记录
                searchRentRecords() {
                    this.$message.success('搜索配租记录完成');
                    // 实际应用中会调用API进行搜索
                },

                // 重置配租筛选
                resetRentFilter() {
                    this.rentFilterForm = {
                        address: '',
                        idCard: '',
                        name: ''
                    };
                    this.$message.info('筛选条件已重置');
                },

                // 查看配租记录
                viewRentRecord(record) {
                    this.$message.info(`查看${record.applicantName}的配租记录`);
                    // 实际应用中会显示详细信息对话框
                },

                // 保存配租记录
                saveRentRecord() {
                    this.$refs.rentForm.validate((valid) => {
                        if (valid) {
                            // 获取选中的申请人和房源信息
                            const applicant = this.applicants.find(a => a.id === this.rentForm.applicantId);
                            const house = this.houses.find(h =>
                                h.community === this.rentForm.community &&
                                h.building === this.rentForm.building &&
                                h.unit === this.rentForm.unit &&
                                h.room === this.rentForm.room
                            );

                            if (!applicant || !house) {
                                this.$message.error('申请人或房源信息不完整');
                                return;
                            }

                            const newRecord = {
                                id: this.rentRecords.length + 1,
                                applicantName: applicant.name,
                                idCard: applicant.idCard,
                                phone: applicant.phone,
                                community: this.rentForm.community,
                                building: this.rentForm.building,
                                unit: this.rentForm.unit,
                                room: this.rentForm.room,
                                roomType: house.roomType,
                                area: house.area,
                                price: house.price,
                                rentDate: this.rentForm.rentDate
                            };

                            this.rentRecords.unshift(newRecord);
                            this.totalRentRecords++;

                            // 更新房源状态为已租
                            house.status = '已租';

                            this.showRentDialog = false;
                            this.resetRentForm();
                            this.$message.success('配租记录添加成功');
                        }
                    });
                },

                // 重置配租表单
                resetRentForm() {
                    this.rentForm = {
                        applicantId: '',
                        community: '',
                        building: '',
                        unit: '',
                        room: '',
                        rentDate: '',
                        remark: ''
                    };
                    if (this.$refs.rentForm) {
                        this.$refs.rentForm.resetFields();
                    }
                },

                // 配租分页变化
                handleRentCurrentChange(val) {
                    this.rentCurrentPage = val;
                    this.$message.info(`加载第 ${val} 页配租记录`);
                },

                // 批量导入配租记录相关方法
                handleRentFileChange(file, fileList) {
                    this.rentFileList = fileList;
                },

                downloadRentTemplate() {
                    this.$message.success('配租记录导入模板下载成功');
                    // 实际应用中会生成并下载Excel模板文件
                },

                importRentRecords() {
                    if (this.rentFileList.length === 0) {
                        this.$message.error('请选择要导入的文件');
                        return;
                    }

                    this.$message.success('正在导入配租记录...');
                    setTimeout(() => {
                        const importCount = Math.floor(Math.random() * 20) + 5;
                        this.$message.success(`成功导入 ${importCount} 条配租记录`);
                        this.showRentImportDialog = false;
                        this.rentFileList = [];
                    }, 2000);
                },

                // 调房相关方法
                submitTransfer() {
                    this.$refs.transferForm.validate((valid) => {
                        if (valid) {
                            this.$message.success('调房申请提交成功');
                            this.showTransferDialog = false;
                            this.resetTransferForm();
                        }
                    });
                },

                resetTransferForm() {
                    this.transferForm = {
                        rentRecordId: '',
                        reason: '',
                        targetHouseId: '',
                        transferDate: '',
                        remark: ''
                    };
                    if (this.$refs.transferForm) {
                        this.$refs.transferForm.resetFields();
                    }
                },

                // 换房相关方法
                submitExchange() {
                    this.$refs.exchangeForm.validate((valid) => {
                        if (valid) {
                            if (this.exchangeForm.rentRecordIdA === this.exchangeForm.rentRecordIdB) {
                                this.$message.error('不能选择相同的租户');
                                return;
                            }
                            this.$message.success('换房申请提交成功');
                            this.showExchangeDialog = false;
                            this.resetExchangeForm();
                        }
                    });
                },

                resetExchangeForm() {
                    this.exchangeForm = {
                        rentRecordIdA: '',
                        rentRecordIdB: '',
                        reason: '',
                        exchangeDate: '',
                        remark: ''
                    };
                    if (this.$refs.exchangeForm) {
                        this.$refs.exchangeForm.resetFields();
                    }
                },

                // 主申请人变更相关方法
                submitChangeApplicant() {
                    this.$refs.changeApplicantForm.validate((valid) => {
                        if (valid) {
                            this.$message.success('主申请人变更申请提交成功');
                            this.showChangeApplicantDialog = false;
                            this.resetChangeApplicantForm();
                        }
                    });
                },

                resetChangeApplicantForm() {
                    this.changeApplicantForm = {
                        rentRecordId: '',
                        newApplicantId: '',
                        reason: '',
                        changeDate: '',
                        remark: ''
                    };
                    if (this.$refs.changeApplicantForm) {
                        this.$refs.changeApplicantForm.resetFields();
                    }
                },

                resetApplySearch() {
                    this.applySearch = {
                        idCard: '',
                        address: '',
                        status: ''
                    };
                },

                submitApplication() {
                    if (!this.applyForm.applicantName || !this.applyForm.idCard || !this.applyForm.phone) {
                        this.$message.error('请填写必填信息');
                        return;
                    }
                    if (!this.applyForm.agreed) {
                        this.$message.error('请先同意相关协议');
                        return;
                    }

                    // 生成新的申请记录
                    const newApplication = {
                        id: 'APP' + String(this.applications.length + 1).padStart(3, '0'),
                        applicantName: this.applyForm.applicantName,
                        idCard: this.applyForm.idCard,
                        phone: this.applyForm.phone,
                        houseAddress: this.applyForm.preferredArea + '公租房小区',
                        roomType: this.applyForm.preferredRoomType,
                        applyDate: new Date().toISOString().split('T')[0],
                        status: '待审核'
                    };

                    this.applications.unshift(newApplication);
                    this.totalApplications++;
                    this.showApplyDialog = false;

                    // 重置表单
                    this.applyForm = {
                        applicantName: '',
                        idCard: '',
                        phone: '',
                        familySize: 1,
                        preferredArea: '',
                        preferredRoomType: '',
                        monthlyIncome: '',
                        reason: '',
                        fileList: [],
                        agreed: false
                    };

                    this.$message.success('申请提交成功，请等待审核');
                },

                viewApplication(row) {
                    this.currentApplication = row;
                    this.showViewApplicationDialog = true;
                },

                editApplication(row) {
                    this.currentApplication = row;
                    this.editApplicationForm = {
                        applicantName: row.applicantName,
                        idCard: row.idCard,
                        phone: row.phone,
                        roomType: row.roomType,
                        houseAddress: row.houseAddress
                    };
                    this.showEditApplicationDialog = true;
                },

                updateApplication() {
                    if (!this.editApplicationForm.applicantName || !this.editApplicationForm.idCard || !this.editApplicationForm.phone) {
                        this.$message.error('请填写必填信息');
                        return;
                    }

                    // 更新当前申请信息
                    Object.assign(this.currentApplication, this.editApplicationForm);
                    this.showEditApplicationDialog = false;
                    this.$message.success('申请信息更新成功');
                },

                getApplicationStep(status) {
                    const stepMap = {
                        '待审核': 1,
                        '审核通过': 2,
                        '已配租': 3,
                        '审核拒绝': 1
                    };
                    return stepMap[status] || 0;
                },

                deleteApplication(id) {
                    this.$confirm('确认删除该申请吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        const index = this.applications.findIndex(app => app.id === id);
                        if (index > -1) {
                            this.applications.splice(index, 1);
                            this.totalApplications--;
                            this.$message.success('删除成功');
                        }
                    });
                },

                getStatusType(status) {
                    const typeMap = {
                        '待审核': 'warning',
                        '审核通过': 'success',
                        '审核拒绝': 'danger',
                        '已配租': 'info'
                    };
                    return typeMap[status] || 'info';
                },

                handleApplyPageChange(val) {
                    this.applyCurrentPage = val;
                    this.$message({
                        message: `加载第 ${val} 页申请数据`,
                        type: 'info'
                    });
                },

                // 合同管理相关方法
                resetContractFilter() {
                this.contractFilterForm = {
                    status: '',
                    dateRange: [],
                    tenant: ''
                };
            },
            
            filterContracts() {
                this.$message({
                    message: '合同筛选条件已应用',
                    type: 'success'
                });
            },
            
            handleContractCurrentChange(val) {
                this.contractCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页合同数据`,
                    type: 'info'
                });
            },

            signContract(contract) {
                this.currentContractForSigning = contract;
                this.contractActiveTab = 'sign';
                this.signStep = 0;
            },

            terminateContract(contract) {
                this.$confirm('确认终止该合同吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    contract.status = '已终止';
                    this.$message.success('合同已终止');
                });
            },

            getContractStatusType(status) {
                const typeMap = {
                    '待签约': 'warning',
                    '执行中': 'success',
                    '已到期': 'info',
                    '已终止': 'danger'
                };
                return typeMap[status] || 'info';
            },

            nextSignStep() {
                if (this.signStep < 3) {
                    this.signStep++;
                    if (this.signStep === 3) {
                        // 签约完成
                        this.currentContractForSigning.status = '执行中';
                        this.$message.success('合同签约完成！');
                    }
                }
            },

            prevSignStep() {
                if (this.signStep > 0) {
                    this.signStep--;
                }
            },

            // 租金管理相关方法
            resetPaymentFilter() {
                this.paymentFilterForm = {
                    status: '',
                    month: null,
                    tenant: ''
                };
            },

            filterPayments() {
                this.$message({
                    message: '账单筛选条件已应用',
                    type: 'success'
                });
            },

            payBill(bill) {
                this.$confirm('确认支付该账单吗？', '支付确认', {
                    confirmButtonText: '确定支付',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    bill.status = '已支付';

                    // 添加支付记录
                    const paymentRecord = {
                        id: 'PAY' + String(this.paymentRecords.length + 1).padStart(3, '0'),
                        billId: bill.id,
                        tenant: bill.tenant,
                        amount: bill.totalAmount,
                        paymentDate: new Date().toISOString().split('T')[0],
                        paymentMethod: '在线支付',
                        status: '成功'
                    };

                    this.paymentRecords.unshift(paymentRecord);
                    this.totalPayments++;

                    // 更新统计数据
                    this.paymentStats.paidBills++;
                    this.paymentStats.unpaidBills--;
                    this.paymentStats.paidAmount += bill.totalAmount;
                    this.paymentStats.unpaidAmount -= bill.totalAmount;

                    this.$message.success('支付成功！');
                });
            },

            getBillStatusType(status) {
                const typeMap = {
                    '待支付': 'warning',
                    '已支付': 'success',
                    '已逾期': 'danger'
                };
                return typeMap[status] || 'info';
            },

            handleBillCurrentChange(val) {
                this.billCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页账单数据`,
                    type: 'info'
                });
            },

            handlePaymentCurrentChange(val) {
                this.paymentCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页支付记录`,
                    type: 'info'
                });
            },

            viewBill(bill) {
                this.currentBill = bill;
                this.showBillPayment = true;
                this.paymentStep = 0;
            },

            downloadInvoice(record) {
                this.$message.success('发票下载成功！');
            },

            getPaymentMethodName(method) {
                const methodMap = {
                    '微信支付': '微信支付',
                    '银行转账': '银行转账',
                    '在线支付': '在线支付',
                    'wechat': '微信支付',
                    'alipay': '支付宝',
                    'bank': '银行卡',
                    'offline': '线下代缴'
                };
                return methodMap[method] || method;
            },

            filterPaymentHistory() {
                this.$message({
                    message: '缴费记录筛选条件已应用',
                    type: 'success'
                });
            },

            handleHistoryCurrentChange(val) {
                this.historyCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页缴费记录`,
                    type: 'info'
                });
            },

            // 维修服务相关方法
            searchRepairs() {
                this.$message({
                    message: '搜索维修工单',
                    type: 'info'
                });
            },

            resetRepairSearch() {
                this.repairSearch = {
                    status: '',
                    type: '',
                    urgency: ''
                };
            },

            submitRepair() {
                if (!this.repairForm.houseAddress || !this.repairForm.tenant || !this.repairForm.type) {
                    this.$message.error('请填写必填信息');
                    return;
                }

                const newRepair = {
                    id: 'REP' + String(this.repairs.length + 1).padStart(3, '0'),
                    houseAddress: this.repairForm.houseAddress,
                    tenant: this.repairForm.tenant,
                    type: this.repairForm.type,
                    description: this.repairForm.description,
                    urgency: this.repairForm.urgency,
                    createTime: new Date().toISOString().split('T')[0],
                    status: '待处理'
                };

                this.repairs.unshift(newRepair);
                this.totalRepairs++;
                this.showRepairDialog = false;

                // 重置表单
                this.repairForm = {
                    houseAddress: '',
                    tenant: '',
                    type: '',
                    urgency: '',
                    description: '',
                    phone: '',
                    fileList: []
                };

                this.$message.success('报修提交成功');
            },

            viewRepair(repair) {
                this.currentRepair = repair;
                this.showRepairDetailDialog = true;
            },

            assignRepair(repair) {
                this.$message.success('工单已派发给维修员');
                repair.status = '处理中';
            },

            getRepairStep(status) {
                const stepMap = {
                    '待处理': 1,
                    '处理中': 2,
                    '已完成': 3
                };
                return stepMap[status] || 0;
            },

            // 维修员管理方法
            addWorker() {
                if (!this.workerForm.name || !this.workerForm.phone || !this.workerForm.specialty) {
                    this.$message.error('请填写必填信息');
                    return;
                }

                const newWorker = {
                    id: 'W' + String(Date.now()).slice(-3),
                    name: this.workerForm.name,
                    phone: this.workerForm.phone,
                    specialty: this.workerForm.specialty,
                    workload: 0,
                    rating: 5.0,
                    status: '在线'
                };

                this.workers.unshift(newWorker);
                this.showAddWorkerDialog = false;

                // 重置表单
                this.workerForm = {
                    name: '',
                    phone: '',
                    specialty: '',
                    experience: 0
                };

                this.$message.success('维修员添加成功');
            },

            viewWorker(worker) {
                this.currentWorker = worker;
                this.showWorkerDetailDialog = true;
            },

            editWorker(worker) {
                this.currentWorker = worker;
                this.editWorkerForm = {
                    name: worker.name,
                    phone: worker.phone,
                    specialty: worker.specialty,
                    status: worker.status
                };
                this.showEditWorkerDialog = true;
            },

            updateWorker() {
                if (!this.editWorkerForm.name || !this.editWorkerForm.phone || !this.editWorkerForm.specialty) {
                    this.$message.error('请填写必填信息');
                    return;
                }

                Object.assign(this.currentWorker, this.editWorkerForm);
                this.showEditWorkerDialog = false;
                this.$message.success('维修员信息更新成功');
            },

            completeRepair(repair) {
                this.$confirm('确认完成该维修工单吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    repair.status = '已完成';
                    this.$message.success('维修工单已完成');
                });
            },

            getUrgencyType(urgency) {
                const typeMap = {
                    '紧急': 'danger',
                    '一般': 'warning',
                    '不急': 'info'
                };
                return typeMap[urgency] || 'info';
            },

            getRepairStatusType(status) {
                const typeMap = {
                    '待处理': 'warning',
                    '处理中': 'primary',
                    '已完成': 'success',
                    '已取消': 'info'
                };
                return typeMap[status] || 'info';
            },

            viewWorker(worker) {
                this.$message.info('查看维修员详情：' + worker.name);
            },

            editWorker(worker) {
                this.$message.info('编辑维修员信息：' + worker.name);
            },

            handleRepairPageChange(val) {
                this.repairCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页维修工单`,
                    type: 'info'
                });
            },

            // 退租管理相关方法
            searchExits() {
                this.$message({
                    message: '搜索退租申请',
                    type: 'info'
                });
            },

            resetExitSearch() {
                this.exitSearch = {
                    status: '',
                    reason: '',
                    tenant: ''
                };
            },

            submitExit() {
                if (!this.exitForm.houseAddress || !this.exitForm.tenant || !this.exitForm.reason) {
                    this.$message.error('请填写必填信息');
                    return;
                }

                const newExit = {
                    id: 'EXIT' + String(this.exits.length + 1).padStart(3, '0'),
                    houseAddress: this.exitForm.houseAddress,
                    tenant: this.exitForm.tenant,
                    reason: this.exitForm.reason,
                    applyDate: new Date().toISOString().split('T')[0],
                    expectedDate: this.exitForm.expectedDate,
                    deposit: 1500, // 默认押金
                    status: '待审核'
                };

                this.exits.unshift(newExit);
                this.totalExits++;
                this.showExitDialog = false;

                // 重置表单
                this.exitForm = {
                    houseAddress: '',
                    tenant: '',
                    reason: '',
                    expectedDate: '',
                    description: '',
                    phone: '',
                    fileList: []
                };

                this.$message.success('退租申请提交成功');
            },

            viewExit(exit) {
                this.currentExit = exit;
                this.showExitDetailDialog = true;
            },

            getExitStep(status) {
                const stepMap = {
                    '待审核': 1,
                    '审核通过': 2,
                    '验收中': 3,
                    '押金退还中': 4,
                    '已完成': 5,
                    '审核拒绝': 1
                };
                return stepMap[status] || 0;
            },

            getExitRecords(exit) {
                const records = [
                    { content: '退租申请已提交', time: exit.applyDate, type: 'success' }
                ];

                if (exit.status !== '待审核') {
                    records.push({ content: '申请审核通过', time: '2023-11-16 10:30', type: 'success' });
                }

                if (exit.status === '验收中' || exit.status === '押金退还中' || exit.status === '已完成') {
                    records.push({ content: '开始房屋验收', time: '2023-11-17 14:20', type: 'primary' });
                }

                if (exit.status === '押金退还中' || exit.status === '已完成') {
                    records.push({ content: '房屋验收完成', time: '2023-11-18 16:45', type: 'success' });
                    records.push({ content: '押金退还处理中', time: '2023-11-19 09:15', type: 'primary' });
                }

                if (exit.status === '已完成') {
                    records.push({ content: '押金已退还，退租完成', time: '2023-11-20 11:30', type: 'success' });
                }

                return records;
            },

            approveExit() {
                this.currentExit.status = '审核通过';
                this.showExitDetailDialog = false;
                this.$message.success('退租申请审核通过');
            },

            rejectExit() {
                this.currentExit.status = '审核拒绝';
                this.showExitDetailDialog = false;
                this.$message.success('退租申请已拒绝');
            },

            approveExit(exit) {
                this.$confirm('确认审核通过该退租申请吗？', '审核确认', {
                    confirmButtonText: '通过',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    exit.status = '审核通过';
                    this.$message.success('审核通过');
                });
            },

            scheduleInspection(exit) {
                this.$message.success('已安排验房，请等待验房员联系');
                exit.status = '待验房';
            },

            getExitStatusType(status) {
                const typeMap = {
                    '待审核': 'warning',
                    '审核通过': 'success',
                    '待验房': 'primary',
                    '已完成': 'info',
                    '已拒绝': 'danger'
                };
                return typeMap[status] || 'info';
            },

            viewInspection(inspection) {
                this.$message.info('查看验房详情：' + inspection.id);
            },

            startInspection(inspection) {
                this.$message.success('开始验房');
                inspection.status = '验房中';
            },

            completeInspection(inspection) {
                this.$message.success('验房完成');
                inspection.status = '已完成';
            },

            getInspectionStatusType(status) {
                const typeMap = {
                    '待验房': 'warning',
                    '验房中': 'primary',
                    '已完成': 'success'
                };
                return typeMap[status] || 'info';
            },

            viewDeposit(deposit) {
                this.$message.info('查看押金详情：' + deposit.id);
            },

            processRefund(deposit) {
                this.$message.success('押金退款处理中');
                deposit.status = '处理中';
            },

            getDepositStatusType(status) {
                const typeMap = {
                    '待退款': 'warning',
                    '处理中': 'primary',
                    '已退款': 'success'
                };
                return typeMap[status] || 'info';
            },

            handleExitPageChange(val) {
                this.exitCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页退租申请`,
                    type: 'info'
                });
            },

            // 动态监管相关方法
            searchCredits() {
                this.$message({
                    message: '搜索信用积分',
                    type: 'info'
                });
            },

            resetCreditSearch() {
                this.creditSearch = {
                    level: '',
                    tenant: ''
                };
            },

            viewCreditDetail(credit) {
                this.currentCredit = credit;
                this.showCreditDetailDialog = true;
            },

            adjustCredit(credit) {
                this.currentCredit = credit;
                this.adjustCreditForm = {
                    type: '',
                    amount: 0,
                    reason: ''
                };
                this.showAdjustCreditDialog = true;
            },

            getCreditHistory(credit) {
                return [
                    { date: '2023-11-01', description: '按时缴费', change: 5, type: 'success' },
                    { date: '2023-10-01', description: '按时缴费', change: 5, type: 'success' },
                    { date: '2023-09-15', description: '逾期缴费', change: -10, type: 'danger' },
                    { date: '2023-09-01', description: '按时缴费', change: 5, type: 'success' },
                    { date: '2023-08-01', description: '初始积分', change: 100, type: 'primary' }
                ];
            },

            submitCreditAdjustment() {
                if (!this.adjustCreditForm.type || !this.adjustCreditForm.amount || !this.adjustCreditForm.reason) {
                    this.$message.error('请填写完整信息');
                    return;
                }

                const change = this.adjustCreditForm.type === 'add' ? this.adjustCreditForm.amount : -this.adjustCreditForm.amount;
                this.currentCredit.score += change;

                // 更新信用等级
                if (this.currentCredit.score >= 90) {
                    this.currentCredit.level = '优秀';
                } else if (this.currentCredit.score >= 80) {
                    this.currentCredit.level = '良好';
                } else if (this.currentCredit.score >= 70) {
                    this.currentCredit.level = '一般';
                } else {
                    this.currentCredit.level = '较差';
                }

                this.showAdjustCreditDialog = false;
                this.showCreditDetailDialog = false;
                this.$message.success('积分调整成功');
            },

            getCreditScoreColor(score) {
                if (score >= 90) return '#67C23A';
                if (score >= 80) return '#409EFF';
                if (score >= 70) return '#E6A23C';
                return '#F56C6C';
            },

            getCreditLevelType(level) {
                const typeMap = {
                    '优秀': 'success',
                    '良好': 'primary',
                    '一般': 'warning',
                    '较差': 'danger'
                };
                return typeMap[level] || 'info';
            },

            handleCreditPageChange(val) {
                this.creditCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页信用数据`,
                    type: 'info'
                });
            },

            refreshVerification() {
                this.$message.success('数据刷新成功');
            },

            viewVerificationDetail(verification) {
                this.currentVerification = verification;
                this.showVerificationDetailDialog = true;
            },

            getVerificationDetails(verification) {
                return [
                    { department: '公安部门', item: '身份信息核验', result: '通过', checkTime: '2023-11-15 10:30' },
                    { department: '民政部门', item: '婚姻状况核验', result: '通过', checkTime: '2023-11-15 11:20' },
                    { department: '不动产部门', item: '房产信息核验', result: verification.status === '异常' ? '异常' : '通过', checkTime: '2023-11-15 14:45' },
                    { department: '税务部门', item: '收入信息核验', result: '通过', checkTime: '2023-11-15 16:15' }
                ];
            },

            recheckVerification(verification) {
                verification.status = '待核验';
                verification.lastCheck = new Date().toLocaleString();
                this.showVerificationDetailDialog = false;
                this.$message.success('重新核验已启动');
            },

            getVerificationStatusType(status) {
                const typeMap = {
                    '通过': 'success',
                    '异常': 'danger',
                    '待核验': 'warning'
                };
                return typeMap[status] || 'info';
            },

            handleVerificationPageChange(val) {
                this.verificationCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页核验数据`,
                    type: 'info'
                });
            },

            viewAlert(alert) {
                this.currentAlert = alert;
                this.showAlertDetailDialog = true;
            },

            getAlertHistory(alert) {
                const history = [
                    { content: '异常情况发现', time: alert.createTime, type: 'warning' }
                ];

                if (alert.status !== '待处理') {
                    history.push({ content: '开始处理异常', time: '2023-11-16 09:30', type: 'primary' });
                }

                if (alert.status === '已处理') {
                    history.push({ content: '异常处理完成', time: '2023-11-17 15:20', type: 'success' });
                }

                return history;
            },

            processAlert() {
                this.currentAlert.status = '处理中';
                this.currentAlert.handler = '管理员';
                this.showAlertDetailDialog = false;
                this.$message.success('异常处理已开始');
            },

            completeAlert() {
                this.currentAlert.status = '已处理';
                this.showAlertDetailDialog = false;
                this.$message.success('异常处理已完成');
            },

            handleAlert(alert) {
                this.$message.success('异常处理中...');
                alert.status = '处理中';
            },

            getAlertType(type) {
                const typeMap = {
                    '收入超标': 'danger',
                    '新增房产': 'warning',
                    '违规转租': 'danger',
                    '其他': 'info'
                };
                return typeMap[type] || 'info';
            },

            getAlertLevelType(level) {
                const typeMap = {
                    '高': 'danger',
                    '中': 'warning',
                    '低': 'info'
                };
                return typeMap[level] || 'info';
            },

            getAlertStatusType(status) {
                const typeMap = {
                    '待处理': 'warning',
                    '处理中': 'primary',
                    '已处理': 'success'
                };
                return typeMap[status] || 'info';
            },
            
            getContractStatusType(status) {
                switch (status) {
                    case '待签约': return 'warning';
                    case '执行中': return 'success';
                    case '已到期': return 'info';
                    case '已终止': return 'danger';
                    default: return '';
                }
            },
            
            viewContract(contract) {
                this.currentContract = contract;
                this.showContractDetail = true;
                this.showSignContract = false;
            },

            signContract(contract) {
                this.currentContract = contract;
                this.showSignContract = true;
                this.showContractDetail = false;
                this.signStep = 0;
                this.contractRead = false;
                this.verificationComplete = false;
                this.signatureConfirm = false;
            },

            // 返回合同列表
            backToContractList() {
                this.showContractDetail = false;
                this.showSignContract = false;
                this.currentContract = null;
                this.signStep = 0;
            },

            // 签约步骤控制
            nextSignStep() {
                if (this.signStep < 2) {
                    this.signStep++;
                } else {
                    // 完成签约
                    this.currentContract.status = '执行中';
                    this.currentContract.signTime = new Date().toLocaleString();
                    this.$message.success('签约成功！');
                }
            },

            prevSignStep() {
                if (this.signStep > 0) {
                    this.signStep--;
                }
            },

            // 下载合同
            downloadContract() {
                this.$message.success('合同下载成功！');
            },

            terminateContract(contract) {
                this.currentContract = contract;
                this.terminateForm = {
                    reason: '',
                    description: '',
                    date: new Date()
                };
                this.terminateDialogVisible = true;
            },
            
            confirmTerminate() {
                this.$refs.terminateForm.validate(valid => {
                    if (valid) {
                        this.$confirm('确认终止该合同？此操作不可逆', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(() => {
                            // 模拟终止合同逻辑
                            const index = this.contracts.findIndex(c => c.id === this.currentContract.id);
                            if (index !== -1) {
                                this.contracts[index].status = '已终止';
                                this.contracts[index].activities.push({
                                    content: `合同终止：${this.terminateForm.reason}`,
                                    timestamp: new Date().toLocaleString(),
                                    type: 'danger'
                                });
                            }
                            
                            this.$message({
                                message: '合同已终止',
                                type: 'success'
                            });
                            this.terminateDialogVisible = false;
                        }).catch(() => {
                            // 用户取消操作
                        });
                    } else {
                        this.$message({
                            message: '请完善终止信息',
                            type: 'error'
                        });
                    }
                });
            },
            
            // 电子签约相关方法
            nextStep() {
                if (this.signStep < 3) {
                    this.signStep++;
                    
                    // 模拟身份验证完成
                    if (this.signStep === 2) {
                        this.verificationComplete = true;
                    }
                }
            },
            
            prevStep() {
                if (this.signStep > 0) {
                    this.signStep--;
                }
            },
            
            cancelSign() {
                this.contractActiveTab = 'list';
                this.currentContractForSigning = null;
            },
            
            startFaceRecognition() {
                // 模拟人脸识别过程
                this.$message({
                    message: '人脸识别成功',
                    type: 'success'
                });
                this.verificationComplete = true;
            },
            
            clearSignature() {
                this.$message({
                    message: '签名已清除',
                    type: 'info'
                });
            },
            
            downloadContract() {
                this.$message({
                    message: '合同下载中...',
                    type: 'success'
                });
            },
            
            finishSigning() {
                // 模拟签约完成逻辑
                const index = this.contracts.findIndex(c => c.id === this.currentContractForSigning.id);
                if (index !== -1) {
                    this.contracts[index].status = '执行中';
                    this.contracts[index].signDate = new Date().toISOString().slice(0, 10);
                    this.contracts[index].progress = 25;
                    this.contracts[index].activities = [
                        { content: '合同创建', timestamp: new Date(new Date().getTime() - 5*24*60*60*1000).toLocaleString(), type: 'primary' },
                        { content: '电子签约完成', timestamp: new Date().toLocaleString(), type: 'success' }
                    ];
                }
                
                this.contractActiveTab = 'list';
                this.currentContractForSigning = null;
                
                this.$message({
                    message: '签约流程已完成',
                    type: 'success'
                });
            },
            
            // 共同居住人相关方法
            addCohabitant() {
                this.cohabitantForm = {
                    name: '',
                    relation: '',
                    idCard: '',
                    phone: ''
                };
                this.cohabitantDialogVisible = true;
            },
            
            submitCohabitant() {
                this.$refs.cohabitantForm.validate(valid => {
                    if (valid) {
                        // 模拟添加共同居住人逻辑
                        if (!this.currentContract.cohabitants) {
                            this.currentContract.cohabitants = [];
                        }
                        
                        this.currentContract.cohabitants.push({
                            ...this.cohabitantForm,
                            status: '未授权'
                        });
                        
                        this.$message({
                            message: '共同居住人添加成功',
                            type: 'success'
                        });
                        
                        this.cohabitantDialogVisible = false;
                    } else {
                        this.$message({
                            message: '请完善共同居住人信息',
                            type: 'error'
                        });
                    }
                });
            },
            
            // 租金管理相关方法
            resetPaymentFilter() {
                this.paymentFilterForm = {
                    status: '',
                    month: '',
                    tenant: ''
                };
            },
            
            filterPayments() {
                this.$message({
                    message: '账单筛选条件已应用',
                    type: 'success'
                });
            },
            
            handlePaymentCurrentChange(val) {
                this.paymentCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页账单数据`,
                    type: 'info'
                });
            },
            
            getBillStatusType(status) {
                switch (status) {
                    case '待支付': return 'warning';
                    case '已支付': return 'success';
                    case '已逾期': return 'danger';
                    default: return '';
                }
            },
            
            payBill(bill) {
                this.currentBill = bill;
                this.showBillPayment = true;
                this.paymentStep = 0;
                this.paymentMethod = 'wechat';
            },

            // 返回账单列表
            backToBillList() {
                this.showBillPayment = false;
                this.currentBill = null;
                this.paymentStep = 0;
            },

            // 支付步骤控制
            nextPaymentStep() {
                if (this.paymentStep < 2) {
                    this.paymentStep++;
                } else {
                    // 完成支付
                    const index = this.bills.findIndex(b => b.id === this.currentBill.id);
                    if (index !== -1) {
                        this.bills[index].status = '已支付';
                        this.bills[index].paymentTime = new Date().toLocaleString();
                    }
                    this.$message.success('支付成功！');
                }
            },

            prevPaymentStep() {
                if (this.paymentStep > 0) {
                    this.paymentStep--;
                }
            },

            // 下载收据
            downloadReceipt() {
                this.$message.success('收据下载成功！');
            },

            confirmPayment() {
                // 模拟支付逻辑
                this.$confirm('确认支付该账单？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    const index = this.bills.findIndex(b => b.id === this.currentBill.id);
                    if (index !== -1) {
                        const now = new Date();
                        const paymentTime = now.toLocaleString();
                        const transactionId = now.getFullYear().toString() +
                            (now.getMonth() + 1).toString().padStart(2, '0') +
                            now.getDate().toString().padStart(2, '0') +
                            now.getHours().toString().padStart(2, '0') +
                            now.getMinutes().toString().padStart(2, '0') +
                            now.getSeconds().toString().padStart(2, '0') +
                            Math.floor(Math.random() * 10000).toString().padStart(4, '0');
                        
                        this.bills[index].status = '已支付';
                        this.bills[index].paymentTime = paymentTime;
                        this.bills[index].paymentMethod = this.paymentMethod;
                        this.bills[index].transactionId = transactionId;
                        
                        // 添加到支付历史
                        this.paymentHistory.unshift({
                            id: this.currentBill.id,
                            houseTitle: this.currentBill.houseTitle,
                            month: this.currentBill.month,
                            paymentTime: paymentTime,
                            amount: this.currentBill.totalAmount + (this.currentBill.lateFee || 0),
                            paymentMethod: this.paymentMethod
                        });
                        
                        this.$message({
                            message: '支付成功',
                            type: 'success'
                        });
                        
                        // 更新当前账单
                        this.currentBill = { ...this.bills[index] };
                    }
                }).catch(() => {
                    // 用户取消支付
                });
            },
            
            cancelPayment() {
                this.paymentActiveTab = 'list';
                this.currentBill = null;
            },
            
            downloadInvoice(bill) {
                this.$message({
                    message: '电子发票下载中...',
                    type: 'success'
                });
            },
            
            getPaymentMethodName(method) {
                switch (method) {
                    case 'wechat': return '微信支付';
                    case 'alipay': return '支付宝';
                    case 'bank': return '银行卡';
                    case 'offline': return '线下代缴';
                    default: return '未知';
                }
            },
            
            filterPaymentHistory() {
                this.$message({
                    message: '缴费记录筛选条件已应用',
                    type: 'success'
                });
            },
            
            handleHistoryCurrentChange(val) {
                this.historyCurrentPage = val;
                this.$message({
                    message: `加载第 ${val} 页缴费记录`,
                    type: 'info'
                });
            },

            // ========== UI优化相关方法 ==========

            // 显示加载状态
            showLoading(message = '加载中...') {
                this.loading = this.$loading({
                    lock: true,
                    text: message,
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
            },

            // 隐藏加载状态
            hideLoading() {
                if (this.loading) {
                    this.loading.close();
                }
            },

            // 成功提示
            showSuccess(message) {
                this.$message({
                    message: message,
                    type: 'success',
                    duration: 3000,
                    showClose: true
                });
            },

            // 错误提示
            showError(message) {
                this.$message({
                    message: message,
                    type: 'error',
                    duration: 5000,
                    showClose: true
                });
            },

            // 警告提示
            showWarning(message) {
                this.$message({
                    message: message,
                    type: 'warning',
                    duration: 4000,
                    showClose: true
                });
            },

            // 信息提示
            showInfo(message) {
                this.$message({
                    message: message,
                    type: 'info',
                    duration: 3000,
                    showClose: true
                });
            },

            // 确认对话框
            showConfirm(message, title = '确认操作') {
                return this.$confirm(message, title, {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    center: true
                });
            },

            // 格式化日期
            formatDate(date, format = 'YYYY-MM-DD') {
                if (!date) return '';
                const d = new Date(date);
                const year = d.getFullYear();
                const month = String(d.getMonth() + 1).padStart(2, '0');
                const day = String(d.getDate()).padStart(2, '0');
                const hour = String(d.getHours()).padStart(2, '0');
                const minute = String(d.getMinutes()).padStart(2, '0');
                const second = String(d.getSeconds()).padStart(2, '0');

                return format
                    .replace('YYYY', year)
                    .replace('MM', month)
                    .replace('DD', day)
                    .replace('HH', hour)
                    .replace('mm', minute)
                    .replace('ss', second);
            },

            // 格式化金额
            formatMoney(amount) {
                if (amount === null || amount === undefined) return '0.00';
                return Number(amount).toLocaleString('zh-CN', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });
            },

            // 复制到剪贴板
            copyToClipboard(text) {
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(text).then(() => {
                        this.showSuccess('已复制到剪贴板');
                    }).catch(() => {
                        this.showError('复制失败');
                    });
                } else {
                    // 兼容旧浏览器
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showSuccess('已复制到剪贴板');
                    } catch (err) {
                        this.showError('复制失败');
                    }
                    document.body.removeChild(textArea);
                }
            },

            // 导出Excel
            exportToExcel(data, filename = 'export') {
                this.showLoading('正在导出数据...');
                setTimeout(() => {
                    this.hideLoading();
                    this.showSuccess(`${filename}.xlsx 导出成功`);
                    // 实际应用中会调用真实的导出功能
                }, 2000);
            },

            // 验证身份证号
            validateIdCard(idCard) {
                const reg = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
                return reg.test(idCard);
            },

            // 验证手机号
            validatePhone(phone) {
                const reg = /^1[3-9]\d{9}$/;
                return reg.test(phone);
            },

            // 防抖函数
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            },

            // 节流函数
            throttle(func, limit) {
                let inThrottle;
                return function() {
                    const args = arguments;
                    const context = this;
                    if (!inThrottle) {
                        func.apply(context, args);
                        inThrottle = true;
                        setTimeout(() => inThrottle = false, limit);
                    }
                };
            },
                // 应用初始化
                initializeApp() {
                    // 设置Element UI全局配置
                    this.$ELEMENT.size = 'small';

                    // 显示欢迎消息
                    this.$notify({
                        title: '欢迎使用',
                        message: '公租房智慧社区云平台',
                        type: 'success',
                        duration: 3000
                    });

                    // 模拟加载数据
                    this.loadInitialData();
                },

                // 加载初始数据
                loadInitialData() {
                    this.showLoading('正在加载数据...');

                    // 模拟异步加载
                    setTimeout(() => {
                        this.hideLoading();
                        this.showSuccess('数据加载完成');
                    }, 1500);
                },

                // 添加键盘快捷键
                addKeyboardShortcuts() {
                    document.addEventListener('keydown', (e) => {
                        // Ctrl + S 保存
                        if (e.ctrlKey && e.key === 's') {
                            e.preventDefault();
                            this.showInfo('快捷键保存功能');
                        }

                        // Ctrl + F 搜索
                        if (e.ctrlKey && e.key === 'f') {
                            e.preventDefault();
                            this.showInfo('快捷键搜索功能');
                        }

                        // ESC 关闭对话框
                        if (e.key === 'Escape') {
                            this.closeAllDialogs();
                        }
                    });
                },

                // 关闭所有对话框
                closeAllDialogs() {
                    this.showHouseDialog = false;
                    this.showBatchGenerateDialog = false;
                    this.showApplicantDialog = false;
                    this.showFamilyDialog = false;
                    this.showApplicantImportDialog = false;
                    this.showFamilyImportDialog = false;
                    this.showRentDialog = false;
                    this.showRentImportDialog = false;
                    this.showTransferDialog = false;
                    this.showExchangeDialog = false;
                    this.showChangeApplicantDialog = false;
                    this.showBillDialog = false;
                    this.showBatchBillDialog = false;
                },

                // 窗口大小变化处理
                handleWindowResize: function() {
                    // 响应式处理
                    const width = window.innerWidth;
                    if (width < 768) {
                        this.isMobile = true;
                    } else {
                        this.isMobile = false;
                    }
                },

                // 设置全局错误处理
                setupErrorHandling() {
                    window.addEventListener('error', (e) => {
                        console.error('全局错误:', e.error);
                        this.showError('系统发生错误，请刷新页面重试');
                    });

                    window.addEventListener('unhandledrejection', (e) => {
                        console.error('未处理的Promise错误:', e.reason);
                        this.showError('网络请求失败，请检查网络连接');
                    });
                },

                // 快捷操作方法
                toggleFullscreen() {
                    if (!document.fullscreenElement) {
                        document.documentElement.requestFullscreen().then(() => {
                            this.isFullscreen = true;
                            this.showSuccess('已进入全屏模式');
                        }).catch(() => {
                            this.showError('全屏模式不支持');
                        });
                    } else {
                        document.exitFullscreen().then(() => {
                            this.isFullscreen = false;
                            this.showSuccess('已退出全屏模式');
                        });
                    }
                },

                refreshData() {
                    this.showLoading('正在刷新数据...');
                    setTimeout(() => {
                        this.hideLoading();
                        this.showSuccess('数据刷新完成');
                        // 实际应用中会重新加载所有数据
                    }, 1500);
                },

                showSettings() {
                    this.$alert('系统设置功能开发中...', '提示', {
                        confirmButtonText: '确定',
                        type: 'info'
                    });
                },

                showHelp() {
                    this.$alert('帮助文档功能开发中...', '提示', {
                        confirmButtonText: '确定',
                        type: 'info'
                    });
                },

                // 获取页面标题
                getPageTitle() {
                    const titleMap = {
                        'home': '首页',
                        'house': '房源管理',
                        'person': '人员管理',
                        'apply': '配租管理',
                        'contract': '合同管理',
                        'payment': '租金管理',
                        'repair': '维修服务',
                        'exit': '退租管理',
                        'monitor': '动态监管'
                    };
                    return titleMap[this.activeTab] || '未知页面';
                }
            },

            // 计算属性
            computed: {
                // 当前申请人的家庭成员
                currentFamilyMembers() {
                    if (!this.currentApplicantId) return [];
                    return this.familyMembers.filter(member => member.applicantId === this.currentApplicantId);
                },

                // 当前家庭成员总数
                currentFamilyMembersTotal() {
                    return this.currentFamilyMembers.length;
                },

                // 可用小区列表
                communities() {
                    return [...new Set(this.houses.map(house => house.community))];
                },

                // 可用幢列表
                availableBuildings() {
                    if (!this.rentForm.community) return [];
                    return [...new Set(this.houses
                        .filter(house => house.community === this.rentForm.community && house.status === '可租')
                        .map(house => house.building))];
                },

                // 可用单元列表
                availableUnits() {
                    if (!this.rentForm.community || !this.rentForm.building) return [];
                    return [...new Set(this.houses
                        .filter(house =>
                            house.community === this.rentForm.community &&
                            house.building === this.rentForm.building &&
                            house.status === '可租')
                        .map(house => house.unit))];
                },

                // 可用房间列表
                availableRooms() {
                    if (!this.rentForm.community || !this.rentForm.building || !this.rentForm.unit) return [];
                    return [...new Set(this.houses
                        .filter(house =>
                            house.community === this.rentForm.community &&
                            house.building === this.rentForm.building &&
                            house.unit === this.rentForm.unit &&
                            house.status === '可租')
                        .map(house => house.room))];
                },

                // 可用房源列表（用于调房）
                availableHouses() {
                    return this.houses.filter(house => house.status === '可租');
                },

                filteredApplications() {
                    let filtered = this.applications;

                    if (this.applySearch.idCard) {
                        filtered = filtered.filter(app =>
                            app.idCard.includes(this.applySearch.idCard)
                        );
                    }

                    if (this.applySearch.address) {
                        filtered = filtered.filter(app =>
                            app.houseAddress.includes(this.applySearch.address)
                        );
                    }

                    if (this.applySearch.status) {
                        filtered = filtered.filter(app =>
                            app.status === this.applySearch.status
                        );
                    }

                    return filtered;
                },

                filteredRepairs() {
                    let filtered = this.repairs;

                    if (this.repairSearch.status) {
                        filtered = filtered.filter(repair =>
                            repair.status === this.repairSearch.status
                        );
                    }

                    if (this.repairSearch.type) {
                        filtered = filtered.filter(repair =>
                            repair.type === this.repairSearch.type
                        );
                    }

                    if (this.repairSearch.urgency) {
                        filtered = filtered.filter(repair =>
                            repair.urgency === this.repairSearch.urgency
                        );
                    }

                    return filtered;
                },

                filteredExits() {
                    let filtered = this.exits;

                    if (this.exitSearch.status) {
                        filtered = filtered.filter(exit =>
                            exit.status === this.exitSearch.status
                        );
                    }

                    if (this.exitSearch.reason) {
                        filtered = filtered.filter(exit =>
                            exit.reason === this.exitSearch.reason
                        );
                    }

                    if (this.exitSearch.tenant) {
                        filtered = filtered.filter(exit =>
                            exit.tenant.includes(this.exitSearch.tenant)
                        );
                    }

                    return filtered;
                },

                filteredCredits() {
                    let filtered = this.credits;

                    if (this.creditSearch.level) {
                        filtered = filtered.filter(credit =>
                            credit.level === this.creditSearch.level
                        );
                    }

                    if (this.creditSearch.tenant) {
                        filtered = filtered.filter(credit =>
                            credit.tenant.includes(this.creditSearch.tenant)
                        );
                    }

                    return filtered;
                }
            },

            // Vue生命周期钩子
            mounted() {
                console.log('应用已加载，当前标签页:', this.activeTab);

                // 页面加载完成后的初始化
                this.initializeApp();

                // 添加键盘快捷键
                this.addKeyboardShortcuts();

                // 添加窗口大小变化监听
                window.addEventListener('resize', this.handleWindowResize);

                // 设置全局错误处理
                this.setupErrorHandling();
            },

            beforeDestroy() {
                // 清理事件监听器
                window.removeEventListener('resize', this.handleWindowResize);
            }
        });
    </script>
</body>
</html> 