<svg width="300" height="180" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="300" height="180" fill="url(#grad1)"/>
  <rect x="50" y="60" width="200" height="80" fill="#ffffff" opacity="0.9" rx="8"/>
  <rect x="60" y="80" width="30" height="40" fill="#4a90e2" rx="4"/>
  <rect x="100" y="80" width="30" height="40" fill="#4a90e2" rx="4"/>
  <rect x="140" y="80" width="30" height="40" fill="#4a90e2" rx="4"/>
  <rect x="180" y="80" width="30" height="40" fill="#4a90e2" rx="4"/>
  <rect x="220" y="80" width="20" height="40" fill="#8b4513" rx="4"/>
  <polygon points="50,60 150,30 250,60" fill="#d2691e"/>
  <text x="150" y="110" font-family="Arial, sans-serif" font-size="12" fill="#333" text-anchor="middle">融创·智慧公租房</text>
  <text x="150" y="125" font-family="Arial, sans-serif" font-size="10" fill="#666" text-anchor="middle">一室一厅 | 45㎡</text>
</svg>
