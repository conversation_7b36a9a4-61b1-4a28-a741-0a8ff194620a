<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公租房经租管理平台</title>
    <link rel="stylesheet" href="assets/element-ui.css">
    <link rel="stylesheet" href="element-ui.css">
    <style>
        body { font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f7fa; overflow-x: hidden; }
        #app { min-height: 100vh; display: flex; }
        .header { background-color: #001529; color: white; padding: 12px 16px; }
        .logo { display: flex; align-items: center; }
        .logo i { font-size: 18px; margin-right: 6px; }
        .logo span { font-size: 14px; font-weight: bold; white-space: nowrap; }
        .sidebar { width: 200px !important; min-width: 200px !important; max-width: 200px !important; background-color: #001529; min-height: 100vh; display: flex; flex-direction: column; box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1); flex-shrink: 0; }
        .main-container { flex: 1; display: flex; flex-direction: column; background-color: #f5f7fa; min-width: 0; overflow-x: hidden; }
        .top-header { background: white; padding: 8px 24px; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08); }
        .user-info { display: flex; align-items: center; }
        .content { flex: 1; padding: 24px 24px 24px 24px; overflow-y: auto; overflow-x: hidden; min-width: 0; }
        .tab-content h2 { margin-top: 0; margin-bottom: 20px; font-size: 18px; font-weight: 600; color: #262626; }
        .filter-section { margin-bottom: 20px; }
        .tab-content { padding-top: 0; }
        .sidebar-nav { flex: 1; padding: 0; margin-top: 10px; }
        .sidebar-nav .el-menu { background-color: #001529; border: none; }
        .sidebar-nav .el-menu-item { color: rgba(255, 255, 255, 0.65); font-size: 14px; height: 48px; line-height: 48px; border: none; margin: 2px 8px; border-radius: 6px; padding: 0 16px !important; position: relative; }
        .sidebar-nav .el-menu-item i { margin-right: 8px; font-size: 16px; width: 16px; text-align: center; }
        .sidebar-nav .el-menu-item:hover { background-color: rgba(24, 144, 255, 0.1); color: #1890ff; }
        .sidebar-nav .el-menu-item.is-active { background-color: #1890ff; color: white; font-weight: 500; }
        .sidebar-nav .el-menu-item.is-active::before { content: ''; position: absolute; left: 0; top: 50%; transform: translateY(-50%); width: 3px; height: 20px; background-color: white; border-radius: 0 2px 2px 0; }
        .el-table { box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); border-radius: 8px; overflow: hidden; }
        .el-table th { background: #f8f9fa; color: #495057; font-weight: 600; }
        .el-table .el-table__row:hover { background: #f8f9fa; }
        .el-button { border-radius: 6px; font-weight: 500; transition: all 0.3s ease; }
        .el-button:hover { transform: translateY(-1px); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); }
        .el-card { border-radius: 12px; box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); transition: all 0.3s ease; }
        .el-card:hover { box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15); }
        .el-form-item__label { font-weight: 500; color: #495057; }
        .el-input__inner, .el-select .el-input__inner, .el-textarea__inner { border-radius: 6px; border: 1px solid #e9ecef; transition: all 0.3s ease; }
        .el-input__inner:focus, .el-select .el-input__inner:focus, .el-textarea__inner:focus { border-color: #667eea; box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2); }
        .el-pagination { text-align: center; margin-top: 20px; }
        .el-pagination .el-pager li { border-radius: 6px; margin: 0 2px; }
        .el-tag { border-radius: 12px; font-weight: 500; }
        .el-dialog { border-radius: 12px; overflow: hidden; }
        .el-dialog__header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; }
        .el-dialog__title { font-weight: 600; font-size: 18px; }
        .el-dialog__headerbtn .el-dialog__close { color: white; font-size: 20px; }
        .el-dialog__body { padding: 30px; }
        .el-dialog__footer { padding: 20px 30px; background: #f8f9fa; }
        .breadcrumb-container { margin-bottom: 15px; padding: 10px 0; }
        .el-breadcrumb { font-size: 14px; }
        .el-breadcrumb__item { color: #909399; }
        .el-breadcrumb__item:last-child { color: #409EFF; font-weight: 500; }
        .table-container { overflow-x: auto; margin: -1px; }
        .el-table { min-width: 800px; }
        .el-card { overflow: hidden; }
        .tab-content { max-width: 100%; overflow-x: hidden; }
        .el-row { max-width: 100%; }
        .el-col { min-width: 0; }
    </style>
</head>
<body>
    <div id="app">
        <div class="sidebar">
            <div class="header">
                <div class="logo"><i class="el-icon-office-building"></i><span>公租房经租管理平台</span></div>
            </div>
            <div class="sidebar-nav">
                <el-menu mode="vertical" default-active="contract" @select="handleTabChange" :collapse="false">
                    <el-menu-item index="home"><i class="el-icon-s-home"></i><span>首页</span></el-menu-item>
                    <el-menu-item index="house"><i class="el-icon-house"></i><span>房源管理</span></el-menu-item>
                    <el-menu-item index="person"><i class="el-icon-user-solid"></i><span>人员管理</span></el-menu-item>
                    <el-menu-item index="apply"><i class="el-icon-document-add"></i><span>配租申请</span></el-menu-item>
                    <el-menu-item index="contract"><i class="el-icon-document"></i><span>合同管理</span></el-menu-item>
                    <el-menu-item index="payment"><i class="el-icon-money"></i><span>租金管理</span></el-menu-item>
                    <el-menu-item index="repair"><i class="el-icon-setting"></i><span>维修服务</span></el-menu-item>
                    <el-menu-item index="exit"><i class="el-icon-switch-button"></i><span>退租管理</span></el-menu-item>
                    <el-menu-item index="monitor"><i class="el-icon-view"></i><span>动态监管</span></el-menu-item>
                </el-menu>
            </div>
        </div>
        <div class="main-container">
            <div class="top-header">
                <div class="breadcrumb-container">
                    <el-breadcrumb separator="/"><el-breadcrumb-item><i class="el-icon-s-home"></i> 首页</el-breadcrumb-item><el-breadcrumb-item>合同管理</el-breadcrumb-item></el-breadcrumb>
                </div>
                <div class="user-info">
                    <el-dropdown>
                        <span class="el-dropdown-link" style="color: #606266; cursor: pointer;"><i class="el-icon-user"></i> 张三 <i class="el-icon-arrow-down"></i></span>
                        <el-dropdown-menu slot="dropdown"><el-dropdown-item>个人中心</el-dropdown-item><el-dropdown-item>退出登录</el-dropdown-item></el-dropdown-menu>
                    </el-dropdown>
                </div>
            </div>
            <div class="content">
                <div class="tab-content">
                    <el-card class="filter-section">
                        <el-form :inline="true" :model="contractFilterForm" size="small">
                            <el-form-item label="合同编号"><el-input v-model="contractFilterForm.contractId" placeholder="请输入合同编号" clearable></el-input></el-form-item>
                            <el-form-item label="租户姓名"><el-input v-model="contractFilterForm.tenant" placeholder="请输入租户姓名" clearable></el-input></el-form-item>
                            <el-form-item label="合同状态">
                                <el-select v-model="contractFilterForm.status" placeholder="选择状态" clearable>
                                    <el-option label="全部状态" value=""></el-option>
                                    <el-option label="待签约" value="待签约"></el-option>
                                    <el-option label="执行中" value="执行中"></el-option>
                                    <el-option label="已到期" value="已到期"></el-option>
                                    <el-option label="已终止" value="已终止"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="searchContracts"><i class="el-icon-search"></i> 搜索</el-button>
                                <el-button @click="resetContractFilter"><i class="el-icon-refresh-left"></i> 重置</el-button>
                            </el-form-item>
                            <el-form-item style="float: right;">
                                <el-button type="primary" @click="createContract"><i class="el-icon-plus"></i> 新建合同</el-button>
                            </el-form-item>
                        </el-form>
                    </el-card>
                    <el-card style="margin-top: 20px;">
                        <el-table :data="contracts" style="width: 100%">
                            <el-table-column prop="id" label="合同编号" width="120"></el-table-column>
                            <el-table-column prop="tenant" label="租户姓名" width="100"></el-table-column>
                            <el-table-column prop="houseTitle" label="房源" width="150"></el-table-column>
                            <el-table-column prop="startDate" label="开始日期" width="120"></el-table-column>
                            <el-table-column prop="endDate" label="结束日期" width="120"></el-table-column>
                            <el-table-column prop="rent" label="月租金(元)" width="100">
                                <template slot-scope="scope"><span>{{ scope.row.rent.toLocaleString() }}</span></template>
                            </el-table-column>
                            <el-table-column prop="status" label="状态" width="100">
                                <template slot-scope="scope">
                                    <el-tag :type="getContractStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="200" fixed="right">
                                <template slot-scope="scope">
                                    <el-button size="mini" @click="viewContract(scope.row)">详情</el-button>
                                    <el-button size="mini" type="primary" @click="editContract(scope.row)">编辑</el-button>
                                    <el-button size="mini" type="success" @click="signContract(scope.row)" v-if="scope.row.status === '待签约'">签署</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div style="text-align: center; margin-top: 20px;">
                            <el-pagination @current-change="handleCurrentChange" :current-page.sync="currentPage" :page-size="pageSize" layout="prev, pager, next, jumper" :total="totalContracts"></el-pagination>
                        </div>
                    </el-card>
                </div>
            </div>
        </div>
    </div>
    <script src="assets/vue.js"></script>
    <script src="assets/element-ui.js"></script>
    <script>
        new Vue({
            el: '#app',
            data: {
                contractFilterForm: { contractId: '', tenant: '', status: '' },
                contracts: [
                    { id: 'CT001', tenant: '张三', houseTitle: '融创·智慧公租房', startDate: '2023-01-01', endDate: '2026-01-01', rent: 800, status: '执行中' },
                    { id: 'CT002', tenant: '王五', houseTitle: '龙湖·椿山公租房', startDate: '2023-02-01', endDate: '2026-02-01', rent: 1200, status: '待签约' },
                    { id: 'CT003', tenant: '赵六', houseTitle: '万科·城市花园公租房', startDate: '2022-10-01', endDate: '2025-10-01', rent: 1000, status: '执行中' }
                ],
                currentPage: 1,
                pageSize: 10,
                totalContracts: 3
            },
            methods: {
                handleTabChange(tab) { if (tab !== 'contract') window.location.href = tab + '.html'; },
                searchContracts() { this.$message.success('搜索功能'); },
                resetContractFilter() { this.contractFilterForm = { contractId: '', tenant: '', status: '' }; },
                getContractStatusType(status) {
                    const statusMap = { '待签约': 'warning', '执行中': 'success', '已到期': 'info', '已终止': 'danger' };
                    return statusMap[status] || '';
                },
                createContract() { this.$message.info('新建合同功能'); },
                viewContract(row) { this.$message.info('查看详情功能'); },
                editContract(row) { this.$message.info('编辑功能'); },
                signContract(row) { this.$message.info('签署功能'); },
                handleCurrentChange(page) { this.currentPage = page; }
            }
        });
    </script>
</body>
</html>
