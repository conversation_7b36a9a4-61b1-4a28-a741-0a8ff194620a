# PowerShell脚本用于下载必要的资源文件

# 创建assets目录
if (!(Test-Path "assets")) {
    New-Item -ItemType Directory -Path "assets"
}

# 下载Vue.js
Write-Host "正在下载 Vue.js..."
Invoke-WebRequest -Uri "https://unpkg.com/vue@2/dist/vue.js" -OutFile "assets/vue.js"

# 下载Element UI CSS
Write-Host "正在下载 Element UI CSS..."
Invoke-WebRequest -Uri "https://unpkg.com/element-ui/lib/theme-chalk/index.css" -OutFile "assets/element-ui.css"

# 下载Element UI JS
Write-Host "正在下载 Element UI JS..."
Invoke-WebRequest -Uri "https://unpkg.com/element-ui/lib/index.js" -OutFile "assets/element-ui.js"

Write-Host "所有资源文件下载完成！"
