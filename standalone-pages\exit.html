<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公租房经租管理平台</title>
    <link rel="stylesheet" href="assets/element-ui.css">
    <link rel="stylesheet" href="element-ui.css">
    <style>
        body { font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f7fa; overflow-x: hidden; }
        #app { min-height: 100vh; display: flex; }
        .header { background-color: #001529; color: white; padding: 12px 16px; }
        .logo { display: flex; align-items: center; }
        .logo i { font-size: 18px; margin-right: 6px; }
        .logo span { font-size: 14px; font-weight: bold; white-space: nowrap; }
        .sidebar { width: 200px !important; min-width: 200px !important; max-width: 200px !important; background-color: #001529; min-height: 100vh; display: flex; flex-direction: column; box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1); flex-shrink: 0; }
        .main-container { flex: 1; display: flex; flex-direction: column; background-color: #f5f7fa; min-width: 0; overflow-x: hidden; }
        .top-header { background: white; padding: 8px 24px; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08); }
        .user-info { display: flex; align-items: center; }
        .content { flex: 1; padding: 24px 24px 24px 24px; overflow-y: auto; overflow-x: hidden; min-width: 0; }
        .tab-content h2 { margin-top: 0; margin-bottom: 20px; font-size: 18px; font-weight: 600; color: #262626; }
        .filter-section { margin-bottom: 20px; }
        .tab-content { padding-top: 0; }
        .sidebar-nav { flex: 1; padding: 0; margin-top: 10px; }
        .sidebar-nav .el-menu { background-color: #001529; border: none; }
        .sidebar-nav .el-menu-item { color: rgba(255, 255, 255, 0.65); font-size: 14px; height: 48px; line-height: 48px; border: none; margin: 2px 8px; border-radius: 6px; padding: 0 16px !important; position: relative; }
        .sidebar-nav .el-menu-item i { margin-right: 8px; font-size: 16px; width: 16px; text-align: center; }
        .sidebar-nav .el-menu-item:hover { background-color: rgba(24, 144, 255, 0.1); color: #1890ff; }
        .sidebar-nav .el-menu-item.is-active { background-color: #1890ff; color: white; font-weight: 500; }
        .sidebar-nav .el-menu-item.is-active::before { content: ''; position: absolute; left: 0; top: 50%; transform: translateY(-50%); width: 3px; height: 20px; background-color: white; border-radius: 0 2px 2px 0; }
        .el-table { box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); border-radius: 8px; overflow: hidden; }
        .el-table th { background: #f8f9fa; color: #495057; font-weight: 600; }
        .el-table .el-table__row:hover { background: #f8f9fa; }
        .el-button { border-radius: 6px; font-weight: 500; transition: all 0.3s ease; }
        .el-button:hover { transform: translateY(-1px); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); }
        .el-card { border-radius: 12px; box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); transition: all 0.3s ease; }
        .el-card:hover { box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15); }
        .el-form-item__label { font-weight: 500; color: #495057; }
        .el-input__inner, .el-select .el-input__inner, .el-textarea__inner { border-radius: 6px; border: 1px solid #e9ecef; transition: all 0.3s ease; }
        .el-input__inner:focus, .el-select .el-input__inner:focus, .el-textarea__inner:focus { border-color: #667eea; box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2); }
        .el-pagination { text-align: center; margin-top: 20px; }
        .el-pagination .el-pager li { border-radius: 6px; margin: 0 2px; }
        .el-tag { border-radius: 12px; font-weight: 500; }
        .el-dialog { border-radius: 12px; overflow: hidden; }
        .el-dialog__header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; }
        .el-dialog__title { font-weight: 600; font-size: 18px; }
        .el-dialog__headerbtn .el-dialog__close { color: white; font-size: 20px; }
        .el-dialog__body { padding: 30px; }
        .el-dialog__footer { padding: 20px 30px; background: #f8f9fa; }
        .breadcrumb-container { margin-bottom: 15px; padding: 10px 0; }
        .el-breadcrumb { font-size: 14px; }
        .el-breadcrumb__item { color: #909399; }
        .el-breadcrumb__item:last-child { color: #409EFF; font-weight: 500; }
        .table-container { overflow-x: auto; margin: -1px; }
        .el-table { min-width: 800px; }
        .el-card { overflow: hidden; }
        .tab-content { max-width: 100%; overflow-x: hidden; }
        .el-row { max-width: 100%; }
        .el-col { min-width: 0; }
    </style>
</head>
<body>
    <div id="app">
        <div class="sidebar">
            <div class="header">
                <div class="logo"><i class="el-icon-office-building"></i><span>公租房经租管理平台</span></div>
            </div>
            <div class="sidebar-nav">
                <el-menu mode="vertical" default-active="exit" @select="handleTabChange" :collapse="false">
                    <el-menu-item index="home"><i class="el-icon-s-home"></i><span>首页</span></el-menu-item>
                    <el-menu-item index="house"><i class="el-icon-house"></i><span>房源管理</span></el-menu-item>
                    <el-menu-item index="person"><i class="el-icon-user-solid"></i><span>人员管理</span></el-menu-item>
                    <el-menu-item index="apply"><i class="el-icon-document-add"></i><span>配租申请</span></el-menu-item>
                    <el-menu-item index="contract"><i class="el-icon-document"></i><span>合同管理</span></el-menu-item>
                    <el-menu-item index="payment"><i class="el-icon-money"></i><span>租金管理</span></el-menu-item>
                    <el-menu-item index="repair"><i class="el-icon-setting"></i><span>维修服务</span></el-menu-item>
                    <el-menu-item index="exit"><i class="el-icon-switch-button"></i><span>退租管理</span></el-menu-item>
                    <el-menu-item index="monitor"><i class="el-icon-view"></i><span>动态监管</span></el-menu-item>
                </el-menu>
            </div>
        </div>
        <div class="main-container">
            <div class="top-header">
                <div class="breadcrumb-container">
                    <el-breadcrumb separator="/"><el-breadcrumb-item><i class="el-icon-s-home"></i> 首页</el-breadcrumb-item><el-breadcrumb-item>退租管理</el-breadcrumb-item></el-breadcrumb>
                </div>
                <div class="user-info">
                    <el-dropdown>
                        <span class="el-dropdown-link" style="color: #606266; cursor: pointer;"><i class="el-icon-user"></i> 张三 <i class="el-icon-arrow-down"></i></span>
                        <el-dropdown-menu slot="dropdown"><el-dropdown-item>个人中心</el-dropdown-item><el-dropdown-item>退出登录</el-dropdown-item></el-dropdown-menu>
                    </el-dropdown>
                </div>
            </div>
            <div class="content">
                <div class="tab-content">
                    <el-card class="filter-section">
                        <el-form :inline="true" :model="exitFilterForm" size="small">
                            <el-form-item label="申请编号"><el-input v-model="exitFilterForm.exitId" placeholder="请输入申请编号" clearable></el-input></el-form-item>
                            <el-form-item label="租户姓名"><el-input v-model="exitFilterForm.tenant" placeholder="请输入租户姓名" clearable></el-input></el-form-item>
                            <el-form-item label="退租状态">
                                <el-select v-model="exitFilterForm.status" placeholder="选择状态" clearable>
                                    <el-option label="全部状态" value=""></el-option>
                                    <el-option label="申请中" value="申请中"></el-option>
                                    <el-option label="审核中" value="审核中"></el-option>
                                    <el-option label="已批准" value="已批准"></el-option>
                                    <el-option label="已完成" value="已完成"></el-option>
                                    <el-option label="已拒绝" value="已拒绝"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="退租原因">
                                <el-select v-model="exitFilterForm.reason" placeholder="选择原因" clearable>
                                    <el-option label="全部原因" value=""></el-option>
                                    <el-option label="主动退租" value="主动退租"></el-option>
                                    <el-option label="违约退租" value="违约退租"></el-option>
                                    <el-option label="到期退租" value="到期退租"></el-option>
                                    <el-option label="其他" value="其他"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="searchExits"><i class="el-icon-search"></i> 搜索</el-button>
                                <el-button @click="resetExitFilter"><i class="el-icon-refresh-left"></i> 重置</el-button>
                            </el-form-item>
                            <el-form-item style="float: right;">
                                <el-button type="primary" @click="createExit"><i class="el-icon-plus"></i> 新建退租申请</el-button>
                            </el-form-item>
                        </el-form>
                    </el-card>
                    <el-card style="margin-top: 20px;">
                        <el-table :data="exits" style="width: 100%">
                            <el-table-column prop="id" label="申请编号" width="120"></el-table-column>
                            <el-table-column prop="tenant" label="租户姓名" width="100"></el-table-column>
                            <el-table-column prop="houseAddress" label="房源地址" width="200"></el-table-column>
                            <el-table-column prop="reason" label="退租原因" width="120"></el-table-column>
                            <el-table-column prop="applyDate" label="申请时间" width="120"></el-table-column>
                            <el-table-column prop="expectedDate" label="预计退租时间" width="120"></el-table-column>
                            <el-table-column prop="status" label="状态" width="100">
                                <template slot-scope="scope">
                                    <el-tag :type="getExitStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="actualDate" label="实际退租时间" width="120"></el-table-column>
                            <el-table-column label="操作" width="200" fixed="right">
                                <template slot-scope="scope">
                                    <el-button size="mini" @click="viewExit(scope.row)">详情</el-button>
                                    <el-button size="mini" type="primary" @click="processExit(scope.row)" v-if="scope.row.status === '申请中'">审核</el-button>
                                    <el-button size="mini" type="success" @click="completeExit(scope.row)" v-if="scope.row.status === '已批准'">完成</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div style="text-align: center; margin-top: 20px;">
                            <el-pagination @current-change="handleCurrentChange" :current-page.sync="currentPage" :page-size="pageSize" layout="prev, pager, next, jumper" :total="totalExits"></el-pagination>
                        </div>
                    </el-card>
                </div>
            </div>
        </div>
    </div>
    <script src="assets/vue.js"></script>
    <script src="assets/element-ui.js"></script>
    <script>
        new Vue({
            el: '#app',
            data: {
                exitFilterForm: { exitId: '', tenant: '', status: '', reason: '' },
                exits: [
                    { id: 'E001', tenant: '张三', houseAddress: '鑫悦花园公租房小区1幢1单元101', reason: '主动退租', applyDate: '2023-01-15', expectedDate: '2023-02-15', status: '申请中', actualDate: '' },
                    { id: 'E002', tenant: '李四', houseAddress: '光明新区公租房小区2幢1单元201', reason: '到期退租', applyDate: '2023-02-20', expectedDate: '2023-03-20', status: '已批准', actualDate: '' },
                    { id: 'E003', tenant: '王五', houseAddress: '万科城市公寓小区3幢1单元301', reason: '违约退租', applyDate: '2023-03-10', expectedDate: '2023-04-10', status: '已完成', actualDate: '2023-04-08' }
                ],
                currentPage: 1,
                pageSize: 10,
                totalExits: 3
            },
            methods: {
                handleTabChange(tab) { if (tab !== 'exit') window.location.href = tab + '.html'; },
                searchExits() { this.$message.success('搜索功能'); },
                resetExitFilter() { this.exitFilterForm = { exitId: '', tenant: '', status: '', reason: '' }; },
                getExitStatusType(status) {
                    const statusMap = { '申请中': 'warning', '审核中': 'primary', '已批准': 'success', '已完成': 'info', '已拒绝': 'danger' };
                    return statusMap[status] || '';
                },
                createExit() { this.$message.info('新建退租申请功能'); },
                viewExit(row) { this.$message.info('查看详情功能'); },
                processExit(row) { this.$message.info('审核退租申请功能'); },
                completeExit(row) { this.$message.info('完成退租功能'); },
                handleCurrentChange(page) { this.currentPage = page; }
            }
        });
    </script>
</body>
</html>
