/* 主样式文件 - main.css */

/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    height: 100%;
    background-color: #f5f7fa;
    color: #333;
    font-size: 14px;
}

#app {
    height: 100%;
}

/* 布局样式 */
.el-container {
    height: 100%;
}

/* 侧边栏样式 */
.aside {
    background-color: #001529;
    color: #fff;
    box-shadow: 2px 0 8px rgba(0, 21, 41, 0.08);
    transition: width 0.3s, box-shadow 0.3s;
    border-top-right-radius: 12px;
    border-bottom-right-radius: 12px;
    min-width: 220px;
    max-width: 240px;
    z-index: 10;
}

.aside.collapsed {
    width: 64px !important;
}

.logo-container {
    height: 60px;
    padding: 0 15px;
    display: flex;
    align-items: center;
    background-color: #002140;
    border-top-right-radius: 12px;
}

.logo {
    width: 32px;
    height: 32px;
    margin-right: 10px;
    font-size: 28px;
    color: #409EFF;
}

.logo-container h1 {
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: opacity 0.3s;
}

.aside.collapsed .logo-container h1 {
    opacity: 0;
    width: 0;
    display: none;
}

.el-menu-vertical {
    border-right: none;
    background: transparent;
    padding-top: 10px;
}

.el-menu-vertical:not(.el-menu--collapse) {
    width: 220px;
}

.el-menu-vertical .el-menu-item {
    height: 48px;
    line-height: 48px;
    border-radius: 8px;
    margin: 4px 12px;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
    font-size: 16px;
    display: flex;
    align-items: center;
}

.el-menu-vertical .el-menu-item i {
    margin-right: 10px;
    font-size: 20px;
}

.el-menu-vertical .el-menu-item.is-active,
.el-menu-vertical .el-menu-item:hover {
    background: linear-gradient(90deg, #409EFF 0%, #66b1ff 100%);
    color: #fff !important;
    box-shadow: 0 2px 8px rgba(64,158,255,0.08);
}

.el-menu-vertical .el-menu-item.is-active {
    font-weight: bold;
    border-left: 4px solid #409EFF;
    border-radius: 8px 0 0 8px;
}

/* 头部样式 */
.header {
    background-color: #fff;
    border-bottom: 1px solid #e6e6e6;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
}

.header-left {
    display: flex;
    align-items: center;
}

.header-left i {
    font-size: 20px;
    margin-right: 20px;
    cursor: pointer;
    color: #606266;
}

.header-right {
    display: flex;
    align-items: center;
}

.notification-badge {
    margin-right: 20px;
    cursor: pointer;
}

.notification-badge i {
    font-size: 18px;
}

.user-dropdown {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 8px;
}

/* 主内容区域 */
.main-container {
    flex-direction: column;
    background-color: #f5f7fa;
}

.main-content {
    padding: 20px;
    overflow-y: auto;
}

/* 页脚 */
.footer {
    background-color: #fff;
    color: #909399;
    font-size: 12px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #e6e6e6;
}

/* 数据概览卡片 */
.data-overview {
    margin-bottom: 20px;
}

.data-card {
    position: relative;
    overflow: hidden;
    height: 108px;
    display: flex;
    align-items: center;
    padding: 20px;
    transition: all 0.3s;
}

.data-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.data-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(64, 158, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.data-icon i {
    font-size: 30px;
    color: #409EFF;
}

.data-icon.blue {
    background-color: rgba(64, 158, 255, 0.1);
}

.data-icon.blue i {
    color: #409EFF;
}

.data-icon.green {
    background-color: rgba(103, 194, 58, 0.1);
}

.data-icon.green i {
    color: #67C23A;
}

.data-icon.orange {
    background-color: rgba(230, 162, 60, 0.1);
}

.data-icon.orange i {
    color: #E6A23C;
}

.data-info {
    flex: 1;
}

.data-value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
    color: #303133;
}

.data-label {
    font-size: 14px;
    color: #909399;
}

/* 图表卡片 */
.chart-card {
    margin-bottom: 20px;
}

.chart-container {
    position: relative;
    width: 100%;
}

.chart-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #909399;
    font-size: 16px;
    background-color: #f8f8f8;
    border-radius: 4px;
}

/* 仪表板样式 */
.dashboard h2 {
    margin-bottom: 20px;
    font-weight: 500;
    color: #303133;
}

.dashboard-charts {
    margin-bottom: 20px;
}

/* 表格通用样式 */
.table-header-operations {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-pagination {
    margin-top: 20px;
    text-align: right;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .data-overview .el-col {
        width: 100%;
        margin-bottom: 15px;
    }
    
    .dashboard-charts .el-col {
        width: 100%;
    }
    
    .aside {
        width: 64px !important;
    }
    
    .logo-container h1 {
        display: none;
    }
}

/* 无障碍模式 */
.accessibility-mode {
    font-size: 16px;
}

.accessibility-mode .el-button,
.accessibility-mode .el-input__inner,
.accessibility-mode .el-select-dropdown__item {
    font-size: 16px;
    height: auto;
    padding: 12px 20px;
}

/* 老年人模式 */
.elderly-mode {
    font-size: 18px;
}

.elderly-mode .el-button,
.elderly-mode .el-input__inner,
.elderly-mode .el-select-dropdown__item {
    font-size: 18px;
    height: auto;
    padding: 14px 22px;
} 