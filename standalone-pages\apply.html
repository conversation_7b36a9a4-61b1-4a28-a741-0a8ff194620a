<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公租房经租管理平台</title>
    <link rel="stylesheet" href="assets/element-ui.css">
    <link rel="stylesheet" href="element-ui.css">
    <style>
        body {
            font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            overflow-x: hidden;
        }
        
        #app {
            min-height: 100vh;
            display: flex;
        }

        .header {
            background-color: #001529;
            color: white;
            padding: 12px 16px;
        }

        .logo {
            display: flex;
            align-items: center;
        }

        .logo i {
            font-size: 18px;
            margin-right: 6px;
        }

        .logo span {
            font-size: 14px;
            font-weight: bold;
            white-space: nowrap;
        }

        .sidebar {
            width: 200px !important;
            min-width: 200px !important;
            max-width: 200px !important;
            background-color: #001529;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
        }

        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: #f5f7fa;
            min-width: 0;
            overflow-x: hidden;
        }

        .top-header {
            background: white;
            padding: 8px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .content {
            flex: 1;
            padding: 24px 24px 24px 24px;
            overflow-y: auto;
            overflow-x: hidden;
            min-width: 0;
        }

        .tab-content h2 {
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
        
        .filter-section {
            margin-bottom: 20px;
        }
        
        .tab-content {
            padding-top: 0;
        }

        /* 导航栏样式 */
        .sidebar-nav {
            flex: 1;
            padding: 0;
            margin-top: 10px;
        }

        .sidebar-nav .el-menu {
            background-color: #001529;
            border: none;
        }

        .sidebar-nav .el-menu-item {
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            height: 48px;
            line-height: 48px;
            border: none;
            margin: 2px 8px;
            border-radius: 6px;
            padding: 0 16px !important;
            position: relative;
        }

        .sidebar-nav .el-menu-item i {
            margin-right: 8px;
            font-size: 16px;
            width: 16px;
            text-align: center;
        }

        .sidebar-nav .el-menu-item:hover {
            background-color: rgba(24, 144, 255, 0.1);
            color: #1890ff;
        }

        .sidebar-nav .el-menu-item.is-active {
            background-color: #1890ff;
            color: white;
            font-weight: 500;
        }

        .sidebar-nav .el-menu-item.is-active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background-color: white;
            border-radius: 0 2px 2px 0;
        }

        .el-table {
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .el-table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
        }

        .el-table .el-table__row:hover {
            background: #f8f9fa;
        }

        .el-button {
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .el-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .el-card {
            border-radius: 12px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .el-card:hover {
            box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
        }

        .el-form-item__label {
            font-weight: 500;
            color: #495057;
        }

        .el-input__inner, .el-select .el-input__inner, .el-textarea__inner {
            border-radius: 6px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .el-input__inner:focus, .el-select .el-input__inner:focus, .el-textarea__inner:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .el-pagination {
            text-align: center;
            margin-top: 20px;
        }

        .el-pagination .el-pager li {
            border-radius: 6px;
            margin: 0 2px;
        }

        .el-tag {
            border-radius: 12px;
            font-weight: 500;
        }

        .el-dialog {
            border-radius: 12px;
            overflow: hidden;
        }

        .el-dialog__header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
        }

        .el-dialog__title {
            font-weight: 600;
            font-size: 18px;
        }

        .el-dialog__headerbtn .el-dialog__close {
            color: white;
            font-size: 20px;
        }

        .el-dialog__body {
            padding: 30px;
        }

        .el-dialog__footer {
            padding: 20px 30px;
            background: #f8f9fa;
        }

        /* 面包屑导航样式 */
        .breadcrumb-container {
            margin-bottom: 15px;
            padding: 10px 0;
        }

        .el-breadcrumb {
            font-size: 14px;
        }

        .el-breadcrumb__item {
            color: #909399;
        }

        .el-breadcrumb__item:last-child {
            color: #409EFF;
            font-weight: 500;
        }

        /* 防止水平滚动条的样式 */
        .table-container {
            overflow-x: auto;
            margin: -1px;
        }

        .el-table {
            min-width: 800px;
        }

        .el-card {
            overflow: hidden;
        }

        .tab-content {
            max-width: 100%;
            overflow-x: hidden;
        }

        .el-row {
            max-width: 100%;
        }

        .el-col {
            min-width: 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <!-- 页面头部 -->
            <div class="header">
                <div class="logo">
                    <i class="el-icon-office-building"></i>
                    <span>公租房经租管理平台</span>
                </div>
            </div>

            <!-- 导航菜单 -->
            <div class="sidebar-nav">
                <el-menu mode="vertical" default-active="apply" @select="handleTabChange" :collapse="false">
                    <el-menu-item index="home">
                        <i class="el-icon-s-home"></i>
                        <span>首页</span>
                    </el-menu-item>
                    <el-menu-item index="house">
                        <i class="el-icon-house"></i>
                        <span>房源管理</span>
                    </el-menu-item>
                    <el-menu-item index="person">
                        <i class="el-icon-user-solid"></i>
                        <span>人员管理</span>
                    </el-menu-item>
                    <el-menu-item index="apply">
                        <i class="el-icon-document-add"></i>
                        <span>配租申请</span>
                    </el-menu-item>
                    <el-menu-item index="contract">
                        <i class="el-icon-document"></i>
                        <span>合同管理</span>
                    </el-menu-item>
                    <el-menu-item index="payment">
                        <i class="el-icon-money"></i>
                        <span>租金管理</span>
                    </el-menu-item>
                    <el-menu-item index="repair">
                        <i class="el-icon-setting"></i>
                        <span>维修服务</span>
                    </el-menu-item>
                    <el-menu-item index="exit">
                        <i class="el-icon-switch-button"></i>
                        <span>退租管理</span>
                    </el-menu-item>
                    <el-menu-item index="monitor">
                        <i class="el-icon-view"></i>
                        <span>动态监管</span>
                    </el-menu-item>
                </el-menu>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-container">
            <!-- 顶部标题栏 -->
            <div class="top-header">
                <!-- 面包屑导航 -->
                <div class="breadcrumb-container">
                    <el-breadcrumb separator="/">
                        <el-breadcrumb-item>
                            <i class="el-icon-s-home"></i> 首页
                        </el-breadcrumb-item>
                        <el-breadcrumb-item>
                            配租申请
                        </el-breadcrumb-item>
                    </el-breadcrumb>
                </div>

                <div class="user-info">
                    <el-dropdown>
                        <span class="el-dropdown-link" style="color: #606266; cursor: pointer;">
                            <i class="el-icon-user"></i> 张三 <i class="el-icon-arrow-down"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item>个人中心</el-dropdown-item>
                            <el-dropdown-item>退出登录</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
            </div>

            <!-- 页面内容 -->
            <div class="content">
                <!-- 配租管理内容 -->
                <div class="tab-content">
                    <!-- 搜索和操作区域 -->
                    <el-card class="filter-section">
                        <el-form :inline="true" :model="rentFilterForm" size="small">
                            <el-form-item label="房源地址">
                                <el-input v-model="rentFilterForm.address" placeholder="请输入房源地址" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="申请人身份证">
                                <el-input v-model="rentFilterForm.idCard" placeholder="请输入身份证号码" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="申请人姓名">
                                <el-input v-model="rentFilterForm.name" placeholder="请输入申请人姓名" clearable></el-input>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="searchRentRecords">
                                    <i class="el-icon-search"></i> 搜索
                                </el-button>
                                <el-button @click="resetRentFilter">
                                    <i class="el-icon-refresh-left"></i> 重置
                                </el-button>
                            </el-form-item>
                            <el-form-item style="float: right;">
                                <el-button type="primary" @click="showRentDialog = true">
                                    <i class="el-icon-plus"></i> 新增配租
                                </el-button>
                                <el-button type="success" @click="showRentImportDialog = true">
                                    <i class="el-icon-upload2"></i> 批量导入
                                </el-button>
                                <el-button type="warning" @click="showTransferDialog = true">
                                    <i class="el-icon-sort"></i> 调房
                                </el-button>
                                <el-button type="info" @click="showExchangeDialog = true">
                                    <i class="el-icon-refresh"></i> 换房
                                </el-button>
                                <el-button type="danger" @click="showChangeApplicantDialog = true">
                                    <i class="el-icon-user"></i> 主申请人变更
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </el-card>

                    <!-- 配租统计卡片 -->
                    <el-row :gutter="20" style="margin-top: 20px;">
                        <el-col :span="6">
                            <el-card shadow="hover">
                                <div style="display: flex; align-items: center;">
                                    <i class="el-icon-pie-chart" style="font-size: 40px; margin-right: 15px; color: #409EFF;"></i>
                                    <div>
                                        <div style="font-size: 24px; font-weight: bold;">{{ ((rentRecords.length / houses.length) * 100).toFixed(1) }}%</div>
                                        <div>配租率</div>
                                    </div>
                                </div>
                            </el-card>
                        </el-col>
                        <el-col :span="6">
                            <el-card shadow="hover">
                                <div style="display: flex; align-items: center;">
                                    <i class="el-icon-warning" style="font-size: 40px; margin-right: 15px; color: #E6A23C;"></i>
                                    <div>
                                        <div style="font-size: 24px; font-weight: bold;">{{ applications.filter(app => app.status === '待审核').length }}</div>
                                        <div>待审核申请</div>
                                    </div>
                                </div>
                            </el-card>
                        </el-col>
                        <el-col :span="6">
                            <el-card shadow="hover">
                                <div style="display: flex; align-items: center;">
                                    <i class="el-icon-wallet" style="font-size: 40px; margin-right: 15px; color: #67C23A;"></i>
                                    <div>
                                        <div style="font-size: 24px; font-weight: bold;">{{ rentRecords.length }}</div>
                                        <div>已配租户数</div>
                                    </div>
                                </div>
                            </el-card>
                        </el-col>
                        <el-col :span="6">
                            <el-card shadow="hover">
                                <div style="display: flex; align-items: center;">
                                    <i class="el-icon-house" style="font-size: 40px; margin-right: 15px; color: #F56C6C;"></i>
                                    <div>
                                        <div style="font-size: 24px; font-weight: bold;">{{ houses.filter(house => house.status === '可租').length }}</div>
                                        <div>可租房源</div>
                                    </div>
                                </div>
                            </el-card>
                        </el-col>
                    </el-row>

                    <!-- 配租记录列表 -->
                    <el-card style="margin-top: 20px;">
                        <el-table
                            :data="rentRecords"
                            style="width: 100%">
                            <el-table-column prop="applicantName" label="主申请人姓名" width="120"></el-table-column>
                            <el-table-column prop="idCard" label="身份证号" width="180">
                                <template slot-scope="scope">
                                    {{ scope.row.idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2') }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="phone" label="联系电话" width="120"></el-table-column>
                            <el-table-column prop="community" label="小区名称" width="150"></el-table-column>
                            <el-table-column prop="building" label="幢" width="80"></el-table-column>
                            <el-table-column prop="unit" label="单元" width="80"></el-table-column>
                            <el-table-column prop="room" label="房间" width="80"></el-table-column>
                            <el-table-column prop="roomType" label="户型" width="100"></el-table-column>
                            <el-table-column prop="area" label="面积(㎡)" width="100"></el-table-column>
                            <el-table-column prop="price" label="月租金(元)" width="120">
                                <template slot-scope="scope">
                                    <span>{{ scope.row.price.toLocaleString() }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="rentDate" label="配租时间" width="120"></el-table-column>
                            <el-table-column label="操作" width="100" fixed="right">
                                <template slot-scope="scope">
                                    <el-button size="mini" @click="viewRentRecord(scope.row)">查看</el-button>
                                </template>
                            </el-table-column>
                        </el-table>

                        <!-- 分页 -->
                        <div style="text-align: center; margin-top: 20px;">
                            <el-pagination
                                @current-change="handleRentCurrentChange"
                                :current-page.sync="rentCurrentPage"
                                :page-size="rentPageSize"
                                layout="prev, pager, next, jumper"
                                :total="totalRentRecords">
                            </el-pagination>
                        </div>
                    </el-card>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/vue.js"></script>
    <script src="assets/element-ui.js"></script>
    <script>
        new Vue({
            el: '#app',
            data: {
                rentFilterForm: {
                    address: '',
                    idCard: '',
                    name: ''
                },
                rentCurrentPage: 1,
                rentPageSize: 10,
                totalRentRecords: 50,
                showRentDialog: false,
                showRentImportDialog: false,
                showTransferDialog: false,
                showExchangeDialog: false,
                showChangeApplicantDialog: false,
                rentRecords: [
                    { applicantName: '张三', idCard: '110101198801011234', phone: '13800138000', community: '鑫悦花园公租房小区', building: 1, unit: 1, room: 101, roomType: '一室一厅', area: 45, price: 800, rentDate: '2023-01-15' },
                    { applicantName: '李四', idCard: '110101199501011234', phone: '13800138001', community: '光明新区公租房小区', building: 2, unit: 1, room: 201, roomType: '两室一厅', area: 60, price: 1200, rentDate: '2023-02-20' },
                    { applicantName: '王五', idCard: '110101198101011234', phone: '13800138002', community: '万科城市公寓小区', building: 3, unit: 1, room: 301, roomType: '三室一厅', area: 75, price: 1500, rentDate: '2023-03-10' }
                ],
                applications: [
                    { status: '待审核' },
                    { status: '待审核' },
                    { status: '已通过' }
                ],
                houses: [
                    { status: '可租' },
                    { status: '可租' },
                    { status: '已租' },
                    { status: '已租' },
                    { status: '可租' }
                ]
            },
            methods: {
                handleTabChange(tab) {
                    // 导航到对应页面
                    if (tab !== 'apply') {
                        window.location.href = tab + '.html';
                    }
                },
                searchRentRecords() {
                    this.$message.success('搜索功能');
                },
                resetRentFilter() {
                    this.rentFilterForm = {
                        address: '',
                        idCard: '',
                        name: ''
                    };
                },
                viewRentRecord(row) {
                    this.$message.info('查看配租记录: ' + row.applicantName);
                },
                handleRentCurrentChange(page) {
                    this.rentCurrentPage = page;
                }
            }
        });
    </script>
</body>
</html>
