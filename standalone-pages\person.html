<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人员管理 - 公租房智慧管理平台</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background-color: #001529;
            color: white;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: 1000;
            overflow-y: auto;
        }

        .header {
            background-color: #001529;
            color: white;
            padding: 12px 16px;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .logo i {
            margin-right: 8px;
            font-size: 24px;
            color: #1890ff;
        }

        .sidebar-nav {
            padding: 0;
        }

        .sidebar-nav .el-menu {
            background-color: #001529;
            border: none;
        }

        .sidebar-nav .el-menu-item {
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            line-height: 48px;
            border: none;
            margin: 2px 8px;
            border-radius: 6px;
            padding: 0 16px !important;
            transition: all 0.3s;
        }

        .sidebar-nav .el-menu-item i {
            margin-right: 8px;
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .sidebar-nav .el-menu-item:hover {
            background-color: rgba(24, 144, 255, 0.1);
            color: #1890ff;
        }

        .sidebar-nav .el-menu-item.is-active {
            background-color: #1890ff;
            color: white;
            position: relative;
        }

        .sidebar-nav .el-menu-item.is-active::before {
            content: '';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background-color: white;
            border-radius: 0 2px 2px 0;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            display: flex;
            flex-direction: column;
            min-width: 0;
            overflow-x: hidden;
        }

        .top-header {
            background: white;
            padding: 8px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
        }

        .user-info {
            display: flex;
            align-items: center;
            color: #666;
        }

        .user-info i {
            margin-right: 8px;
            color: #1890ff;
        }

        .content-area {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }

        .tab-content {
            background: transparent;
        }

        /* 面包屑导航样式 */
        .breadcrumb-container {
            margin-bottom: 15px;
            padding: 10px 0;
        }

        .el-breadcrumb {
            font-size: 14px;
        }

        .el-breadcrumb__item {
            color: #909399;
        }

        .el-breadcrumb__item:last-child {
            color: #409EFF;
            font-weight: 500;
        }

        /* 统计卡片样式优化 */
        .el-card {
            transition: all 0.3s ease;
        }

        .el-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        /* 表格样式优化 */
        .el-table {
            border-radius: 8px;
            overflow: hidden;
        }

        .el-table th {
            background-color: #f8f9fa;
            color: #495057;
            font-weight: 600;
        }

        .filter-section {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="header">
                    <div class="logo">
                        <i class="el-icon-s-home"></i>
                        公租房智慧管理平台
                    </div>
                </div>
                <div class="sidebar-nav">
                    <el-menu mode="vertical" default-active="person" :collapse="false">
                        <el-menu-item index="home" @click="navigateTo('home.html')">
                            <i class="el-icon-s-home"></i>
                            <span>首页</span>
                        </el-menu-item>
                        <el-menu-item index="house" @click="navigateTo('house.html')">
                            <i class="el-icon-house"></i>
                            <span>房源管理</span>
                        </el-menu-item>
                        <el-menu-item index="person" @click="navigateTo('person.html')">
                            <i class="el-icon-user-solid"></i>
                            <span>人员管理</span>
                        </el-menu-item>
                        <el-menu-item index="apply" @click="navigateTo('apply.html')">
                            <i class="el-icon-document-add"></i>
                            <span>配租申请</span>
                        </el-menu-item>
                        <el-menu-item index="contract" @click="navigateTo('contract.html')">
                            <i class="el-icon-document"></i>
                            <span>合同管理</span>
                        </el-menu-item>
                        <el-menu-item index="payment" @click="navigateTo('payment.html')">
                            <i class="el-icon-money"></i>
                            <span>租金管理</span>
                        </el-menu-item>
                        <el-menu-item index="repair" @click="navigateTo('repair.html')">
                            <i class="el-icon-setting"></i>
                            <span>维修服务</span>
                        </el-menu-item>
                        <el-menu-item index="exit" @click="navigateTo('exit.html')">
                            <i class="el-icon-switch-button"></i>
                            <span>退租管理</span>
                        </el-menu-item>
                        <el-menu-item index="monitor" @click="navigateTo('monitor.html')">
                            <i class="el-icon-view"></i>
                            <span>动态监管</span>
                        </el-menu-item>
                    </el-menu>
                </div>
            </div>

            <!-- 主内容区域 -->
            <div class="main-content">
                <!-- 顶部导航 -->
                <div class="top-header">
                    <div class="breadcrumb-container">
                        <el-breadcrumb separator="/">
                            <el-breadcrumb-item>
                                <i class="el-icon-s-home"></i>
                                首页
                            </el-breadcrumb-item>
                            <el-breadcrumb-item>
                                人员管理
                            </el-breadcrumb-item>
                        </el-breadcrumb>
                    </div>
                    <div class="user-info">
                        <i class="el-icon-user"></i>
                        <span>张三</span>
                    </div>
                </div>

                <!-- 内容区域 -->
                <div class="content-area">
                    <!-- 人员管理内容 -->
                    <div class="tab-content">
                        <!-- 搜索筛选区域 -->
                        <el-card class="filter-section">
                            <el-form :inline="true" :model="applicantFilterForm" size="small">
                                <el-form-item label="姓名">
                                    <el-input v-model="applicantFilterForm.name" placeholder="请输入姓名" clearable></el-input>
                                </el-form-item>
                                <el-form-item label="身份证号">
                                    <el-input v-model="applicantFilterForm.idCard" placeholder="请输入身份证号" clearable></el-input>
                                </el-form-item>
                                <el-form-item label="申请状态">
                                    <el-select v-model="applicantFilterForm.status" placeholder="选择状态" clearable>
                                        <el-option label="全部状态" value=""></el-option>
                                        <el-option label="待审核" value="待审核"></el-option>
                                        <el-option label="已通过" value="已通过"></el-option>
                                        <el-option label="已拒绝" value="已拒绝"></el-option>
                                        <el-option label="已配租" value="已配租"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="searchApplicants">
                                        <i class="el-icon-search"></i> 搜索
                                    </el-button>
                                    <el-button @click="resetApplicantFilter">
                                        <i class="el-icon-refresh-left"></i> 重置
                                    </el-button>
                                </el-form-item>
                                <el-form-item style="float: right;">
                                    <el-button type="primary" @click="showApplicantDialog = true">
                                        <i class="el-icon-plus"></i> 新增主申请人
                                    </el-button>
                                </el-form-item>
                            </el-form>
                        </el-card>

                        <!-- 主申请人列表 -->
                        <el-card style="margin-top: 20px;">
                            <el-table
                                :data="applicants"
                                style="width: 100%">
                                <el-table-column prop="name" label="姓名" width="100"></el-table-column>
                                <el-table-column prop="gender" label="性别" width="80"></el-table-column>
                                <el-table-column prop="age" label="年龄" width="80"></el-table-column>
                                <el-table-column prop="idCard" label="身份证号" width="180"></el-table-column>
                                <el-table-column prop="phone" label="联系电话" width="120"></el-table-column>
                                <el-table-column prop="address" label="现住址" min-width="200"></el-table-column>
                                <el-table-column prop="income" label="月收入(元)" width="100">
                                    <template slot-scope="scope">
                                        <span>{{ scope.row.income.toLocaleString() }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="familySize" label="家庭人数" width="100"></el-table-column>
                                <el-table-column prop="applyDate" label="申请时间" width="120"></el-table-column>
                                <el-table-column prop="status" label="状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getApplicantStatusType(scope.row.status)">
                                            {{ scope.row.status }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="240" fixed="right">
                                    <template slot-scope="scope">
                                        <el-button size="mini" @click="viewApplicant(scope.row)">详情</el-button>
                                        <el-button size="mini" type="primary" @click="editApplicant(scope.row)">编辑</el-button>
                                        <el-button size="mini" type="success" @click="viewFamilyMembers(scope.row)">家庭成员</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>

                            <!-- 分页 -->
                            <div style="text-align: center; margin-top: 20px;">
                                <el-pagination
                                    @current-change="handleApplicantCurrentChange"
                                    :current-page.sync="applicantCurrentPage"
                                    :page-size="applicantPageSize"
                                    layout="prev, pager, next, jumper"
                                    :total="totalApplicants">
                                </el-pagination>
                            </div>
                        </el-card>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    applicantFilterForm: {
                        name: '',
                        idCard: '',
                        status: ''
                    },
                    applicants: [
                        { name: '张三', gender: '男', age: 35, idCard: '110101198801011234', phone: '13800138000', address: '北京市朝阳区某某街道', income: 5000, familySize: 3, applyDate: '2023-01-15', status: '已通过' },
                        { name: '李四', gender: '女', age: 28, idCard: '110101199501011234', phone: '13800138001', address: '北京市海淀区某某小区', income: 4500, familySize: 2, applyDate: '2023-02-20', status: '待审核' },
                        { name: '王五', gender: '男', age: 42, idCard: '110101198101011234', phone: '13800138002', address: '北京市西城区某某胡同', income: 6000, familySize: 4, applyDate: '2023-03-10', status: '已配租' }
                    ],
                    applicantCurrentPage: 1,
                    applicantPageSize: 10,
                    totalApplicants: 3,
                    showApplicantDialog: false
                }
            },
            methods: {
                navigateTo(page) {
                    window.location.href = page;
                },
                searchApplicants() {
                    this.$message.success('搜索功能待实现');
                },
                resetApplicantFilter() {
                    this.applicantFilterForm = {
                        name: '',
                        idCard: '',
                        status: ''
                    };
                },
                getApplicantStatusType(status) {
                    const statusMap = {
                        '待审核': 'warning',
                        '已通过': 'success',
                        '已拒绝': 'danger',
                        '已配租': 'info'
                    };
                    return statusMap[status] || '';
                },
                viewApplicant(row) {
                    this.$message.info('查看详情功能待实现');
                },
                editApplicant(row) {
                    this.$message.info('编辑功能待实现');
                },
                viewFamilyMembers(row) {
                    this.$message.info('查看家庭成员功能待实现');
                },
                handleApplicantCurrentChange(page) {
                    this.applicantCurrentPage = page;
                }
            }
        });
    </script>
</body>
</html>
