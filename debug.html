<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面</title>
    <link rel="stylesheet" href="element-ui.css">
</head>
<body>
    <div id="app">
        <div class="header">
            <h1>公租房经租管理平台</h1>
        </div>
        
        <div class="main-content">
            <!-- 首页内容 -->
            <div v-if="activeTab === 'home'">
                <h2>欢迎使用公租房经租管理平台</h2>
                
                <!-- 统计数据 -->
                <div class="statistics">
                    <div v-for="item in statistics" :key="item.title" class="stat-item">
                        <div class="stat-value">{{ item.value }}</div>
                        <div class="stat-title">{{ item.title }}</div>
                    </div>
                </div>
                
                <!-- 系统模块 -->
                <div class="modules">
                    <div v-for="module in modules" :key="module.name" class="module-item" @click="handleTabChange(module.tab)">
                        <i :class="module.icon"></i>
                        <span>{{ module.name }}</span>
                    </div>
                </div>
            </div>
            
            <!-- 其他标签页内容 -->
            <div v-else>
                <h2>{{ activeTab }} 页面</h2>
                <p>当前标签页：{{ activeTab }}</p>
                <el-button @click="activeTab = 'home'">返回首页</el-button>
            </div>
        </div>
    </div>

    <script src="vue.js"></script>
    <script src="element-ui.js"></script>
    <script>
        new Vue({
            el: '#app',
            data: {
                activeTab: 'home',
                statistics: [
                    { title: '可用房源总数', value: '1,284', icon: 'el-icon-house' },
                    { title: '在租合同数量', value: '926', icon: 'el-icon-document' },
                    { title: '按时缴费率', value: '94.5%', icon: 'el-icon-money' },
                    { title: '待处理工单', value: '42', icon: 'el-icon-service' }
                ],
                modules: [
                    { name: '房源管理', tab: 'house', icon: 'el-icon-house' },
                    { name: '配租申请', tab: 'apply', icon: 'el-icon-document-checked' },
                    { name: '合同管理', tab: 'contract', icon: 'el-icon-document' },
                    { name: '租金管理', tab: 'payment', icon: 'el-icon-money' }
                ]
            },
            methods: {
                handleTabChange(tab) {
                    console.log('切换到标签页:', tab);
                    this.activeTab = tab;
                }
            }
        });
    </script>
    
    <style>
        .statistics {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .stat-item {
            padding: 20px;
            background: white;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #409EFF;
        }
        .modules {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .module-item {
            padding: 20px;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            text-align: center;
        }
        .module-item:hover {
            background: #f0f9ff;
        }
    </style>
</body>
</html>
