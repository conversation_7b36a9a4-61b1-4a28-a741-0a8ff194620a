/**
 * 公租房经租管理平台 - 主应用程序脚本
 */

// 主应用Vue实例
const app = new Vue({
    el: '#app',
    data: {
        // 菜单和导航状态
        activeMenu: 'home', // 当前活动菜单
        currentPath: '/', // 当前路径
        currentPathName: '首页', // 当前路径名称
        asideCollapsed: false, // 侧边栏是否折叠
        
        // 用户信息
        userInfo: {
            name: '张三',
            role: '租户',
            avatar: 'assets/images/avatar.png'
        },
        
        // 通知消息
        notifications: [
            { id: 1, title: '租金缴费提醒', content: '您有一笔租金即将到期，请及时缴纳', time: '2023-06-15', type: 'warning', read: false },
            { id: 2, title: '维修工单更新', content: '您的维修申请已受理，工程师将于明天上午9点到访', time: '2023-06-14', type: 'info', read: true },
            { id: 3, title: '合同到期提醒', content: '您的租赁合同将在30天后到期，请及时办理续租手续', time: '2023-06-10', type: 'warning', read: false }
        ],
        
        // 统计数据
        statistics: [
            { title: '可用房源总数', value: '1,284', icon: 'el-icon-house' },
            { title: '在租合同数量', value: '926', icon: 'el-icon-document' },
            { title: '按时缴费率', value: '94.5%', icon: 'el-icon-money' },
            { title: '待处理工单', value: '42', icon: 'el-icon-service' }
        ],
        
        // 模块列表
        modules: [
            { name: '房源管理', path: 'pages/house/list.html', icon: 'el-icon-house' },
            { name: '配租申请', path: 'pages/apply/form.html', icon: 'el-icon-document-checked' },
            { name: '合同管理', path: 'pages/contract/sign.html', icon: 'el-icon-document' },
            { name: '租金管理', path: 'pages/payment/bill.html', icon: 'el-icon-money' },
            { name: '维修服务', path: 'pages/repair/apply.html', icon: 'el-icon-service' },
            { name: '退租管理', path: 'pages/exit/apply.html', icon: 'el-icon-switch-button' },
            { name: '动态监管', path: 'pages/monitor/credit.html', icon: 'el-icon-data-line' }
        ]
    },
    
    methods: {
        // 导航到指定模块
        goToModule(path) {
            console.log('导航到:', path);
            window.location.href = path;
        }
    }
}); 