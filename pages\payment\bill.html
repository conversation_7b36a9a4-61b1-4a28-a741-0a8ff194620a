<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账单查询 - 公租房经租管理平台</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/theme-chalk/index.css">
    <style>
        body {
            font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        
        .page-header {
            margin-bottom: 20px;
        }
        
        .filter-card {
            margin-bottom: 20px;
        }
        
        .bill-summary {
            margin-bottom: 20px;
        }
        
        .bill-status {
            display: inline-block;
            padding: 2px 10px;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .status-paid {
            background-color: #f0f9eb;
            color: #67c23a;
        }
        
        .status-unpaid {
            background-color: #fef0f0;
            color: #f56c6c;
        }
        
        .status-processing {
            background-color: #f4f4f5;
            color: #909399;
        }
        
        .status-overdue {
            background-color: #fef0f0;
            color: #f56c6c;
            font-weight: bold;
        }
        
        .payment-method {
            display: flex;
            align-items: center;
            margin: 20px 0;
        }
        
        .payment-method-item {
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            padding: 15px 20px;
            margin-right: 15px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
        }
        
        .payment-method-item:hover {
            border-color: #409EFF;
        }
        
        .payment-method-item.active {
            border-color: #409EFF;
            background-color: #f0f9ff;
        }
        
        .payment-method-item i {
            font-size: 24px;
            margin-right: 10px;
        }
        
        .qr-code-container {
            text-align: center;
            margin: 20px 0;
        }
        
        .qr-code {
            width: 200px;
            height: 200px;
            margin: 0 auto;
            background-color: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div id="billApp">
        <div class="page-header">
            <h2>账单查询</h2>
            <el-alert
                title="请及时缴纳租金，避免产生滞纳金"
                type="info"
                :closable="false"
                show-icon>
            </el-alert>
        </div>
        
        <!-- 筛选条件 -->
        <el-card class="filter-card">
            <div slot="header">
                <span>筛选条件</span>
                <el-button style="float: right; padding: 3px 0" type="text" @click="resetFilter">重置</el-button>
            </div>
            
            <el-form :inline="true" :model="filterForm" size="small">
                <el-form-item label="账单状态">
                    <el-select v-model="filterForm.status" placeholder="全部状态">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="未缴费" value="未缴费"></el-option>
                        <el-option label="已缴费" value="已缴费"></el-option>
                        <el-option label="处理中" value="处理中"></el-option>
                        <el-option label="已逾期" value="已逾期"></el-option>
                    </el-select>
                </el-form-item>
                
                <el-form-item label="账单类型">
                    <el-select v-model="filterForm.type" placeholder="全部类型">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="租金" value="租金"></el-option>
                        <el-option label="物业费" value="物业费"></el-option>
                        <el-option label="水电费" value="水电费"></el-option>
                        <el-option label="押金" value="押金"></el-option>
                    </el-select>
                </el-form-item>
                
                <el-form-item label="时间范围">
                    <el-date-picker
                        v-model="filterForm.dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-form-item>
                
                <el-form-item>
                    <el-button type="primary" @click="searchBills">查询</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        
        <!-- 账单统计 -->
        <el-row :gutter="20" class="bill-summary">
            <el-col :span="8">
                <el-card shadow="hover">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div>
                            <div style="font-size: 14px; color: #909399;">未缴费账单</div>
                            <div style="font-size: 24px; font-weight: bold; margin-top: 5px;">{{ summary.unpaid }}笔</div>
                        </div>
                        <i class="el-icon-warning" style="font-size: 36px; color: #e6a23c;"></i>
                    </div>
                </el-card>
            </el-col>
            
            <el-col :span="8">
                <el-card shadow="hover">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div>
                            <div style="font-size: 14px; color: #909399;">待缴总金额</div>
                            <div style="font-size: 24px; font-weight: bold; margin-top: 5px;">¥{{ summary.totalAmount.toFixed(2) }}</div>
                        </div>
                        <i class="el-icon-money" style="font-size: 36px; color: #f56c6c;"></i>
                    </div>
                </el-card>
            </el-col>
            
            <el-col :span="8">
                <el-card shadow="hover">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div>
                            <div style="font-size: 14px; color: #909399;">即将逾期</div>
                            <div style="font-size: 24px; font-weight: bold; margin-top: 5px;">{{ summary.nearDue }}笔</div>
                        </div>
                        <i class="el-icon-time" style="font-size: 36px; color: #909399;"></i>
                    </div>
                </el-card>
            </el-col>
        </el-row>
        
        <!-- 账单列表 -->
        <el-card>
            <div slot="header">
                <span>账单列表</span>
                <el-button style="float: right; padding: 3px 0" type="text" @click="batchPayBills" :disabled="selectedBills.length === 0">
                    <i class="el-icon-wallet"></i> 批量缴费
                </el-button>
            </div>
            
            <el-table
                :data="bills"
                style="width: 100%"
                @selection-change="handleSelectionChange"
                :row-class-name="tableRowClassName">
                
                <el-table-column type="selection" width="55"></el-table-column>
                
                <el-table-column prop="id" label="账单编号" width="120"></el-table-column>
                
                <el-table-column prop="type" label="账单类型" width="100"></el-table-column>
                
                <el-table-column prop="period" label="账期/月份" width="120"></el-table-column>
                
                <el-table-column prop="amount" label="金额(元)" width="100">
                    <template slot-scope="scope">
                        <span style="color: #f56c6c;">{{ scope.row.amount.toFixed(2) }}</span>
                        <span v-if="scope.row.lateCharge > 0" style="color: #f56c6c;">
                            (+{{ scope.row.lateCharge.toFixed(2) }})
                        </span>
                    </template>
                </el-table-column>
                
                <el-table-column prop="dueDate" label="截止日期" width="120"></el-table-column>
                
                <el-table-column prop="status" label="状态" width="100">
                    <template slot-scope="scope">
                        <span :class="['bill-status', getBillStatusClass(scope.row.status)]">
                            {{ scope.row.status }}
                        </span>
                    </template>
                </el-table-column>
                
                <el-table-column label="操作" width="200">
                    <template slot-scope="scope">
                        <el-button
                            size="mini"
                            type="primary"
                            @click="payBill(scope.row)"
                            :disabled="scope.row.status !== '未缴费' && scope.row.status !== '已逾期'">
                            缴费
                        </el-button>
                        
                        <el-button
                            size="mini"
                            @click="viewBillDetail(scope.row)">
                            详情
                        </el-button>
                        
                        <el-button
                            size="mini"
                            type="text"
                            @click="getInvoice(scope.row)"
                            :disabled="scope.row.status !== '已缴费'">
                            电子发票
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            
            <div style="text-align: center; margin-top: 20px;">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page.sync="currentPage"
                    :page-size="10"
                    layout="prev, pager, next, jumper"
                    :total="totalBills">
                </el-pagination>
            </div>
        </el-card>
        
        <!-- 缴费对话框 -->
        <el-dialog :title="'账单缴费 - ' + (currentBill ? currentBill.id : '')" :visible.sync="paymentDialogVisible" width="50%">
            <div v-if="currentBill">
                <el-descriptions title="账单详情" :column="2" border>
                    <el-descriptions-item label="账单编号">{{ currentBill.id }}</el-descriptions-item>
                    <el-descriptions-item label="账单类型">{{ currentBill.type }}</el-descriptions-item>
                    <el-descriptions-item label="账期/月份">{{ currentBill.period }}</el-descriptions-item>
                    <el-descriptions-item label="截止日期">{{ currentBill.dueDate }}</el-descriptions-item>
                    <el-descriptions-item label="账单金额">¥{{ currentBill.amount.toFixed(2) }}</el-descriptions-item>
                    <el-descriptions-item label="滞纳金" v-if="currentBill.lateCharge > 0">
                        <span style="color: #f56c6c;">¥{{ currentBill.lateCharge.toFixed(2) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="应付总额">
                        <span style="color: #f56c6c; font-weight: bold;">
                            ¥{{ (currentBill.amount + (currentBill.lateCharge || 0)).toFixed(2) }}
                        </span>
                    </el-descriptions-item>
                </el-descriptions>
                
                <div style="margin: 20px 0;">
                    <h4>选择支付方式</h4>
                    <div class="payment-method">
                        <div 
                            class="payment-method-item" 
                            :class="{ active: paymentMethod === 'wechat' }"
                            @click="paymentMethod = 'wechat'">
                            <i class="el-icon-mobile-phone" style="color: #07c160;"></i>
                            <span>微信支付</span>
                        </div>
                        
                        <div 
                            class="payment-method-item" 
                            :class="{ active: paymentMethod === 'alipay' }"
                            @click="paymentMethod = 'alipay'">
                            <i class="el-icon-mobile-phone" style="color: #1677ff;"></i>
                            <span>支付宝</span>
                        </div>
                        
                        <div 
                            class="payment-method-item" 
                            :class="{ active: paymentMethod === 'bank' }"
                            @click="paymentMethod = 'bank'">
                            <i class="el-icon-bank-card"></i>
                            <span>银行卡</span>
                        </div>
                    </div>
                    
                    <el-divider>扫码支付</el-divider>
                    
                    <div class="qr-code-container">
                        <div class="qr-code">
                            <i class="el-icon-s-opportunity" style="font-size: 48px; color: #909399;"></i>
                        </div>
                        <p style="margin-top: 10px; color: #606266;">请使用{{ paymentMethodText }}扫描二维码完成支付</p>
                        <p style="color: #909399; font-size: 12px;">二维码有效期: 5分钟</p>
                    </div>
                </div>
            </div>
            
            <div slot="footer">
                <el-button @click="paymentDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmPayment">确认已支付</el-button>
                <el-button @click="showProxyPayment">代缴服务</el-button>
            </div>
        </el-dialog>
        
        <!-- 代缴服务对话框 -->
        <el-dialog title="代缴服务" :visible.sync="proxyDialogVisible" width="40%" append-to-body>
            <p>无法在线支付？我们提供以下便捷服务：</p>
            
            <el-card style="margin: 20px 0;">
                <div style="display: flex; align-items: center;">
                    <i class="el-icon-service" style="font-size: 36px; color: #409EFF; margin-right: 15px;"></i>
                    <div>
                        <h4>社区工作人员代缴</h4>
                        <p style="color: #606266; font-size: 14px;">您可以前往社区服务中心，工作人员将协助您完成线下缴费</p>
                    </div>
                </div>
                <div style="margin-top: 15px;">
                    <el-button size="small" type="primary" @click="generateProxyQrCode">生成代缴二维码</el-button>
                </div>
            </el-card>
            
            <div v-if="showProxyQr" style="text-align: center; margin-top: 20px;">
                <div class="qr-code">
                    <i class="el-icon-s-opportunity" style="font-size: 48px; color: #909399;"></i>
                </div>
                <p style="margin-top: 10px; color: #606266;">请向社区工作人员出示此二维码</p>
                <p style="color: #909399; font-size: 12px;">代缴二维码有效期: 24小时</p>
            </div>
            
            <div style="margin-top: 20px; color: #909399; font-size: 12px;">
                <i class="el-icon-info" style="margin-right: 5px;"></i>
                老年人、特殊人群可拨打热线电话: 400-123-4567 预约上门服务
            </div>
        </el-dialog>
        
        <!-- 账单详情对话框 -->
        <el-dialog title="账单详情" :visible.sync="detailDialogVisible" width="60%">
            <div v-if="currentBill">
                <el-descriptions title="基本信息" :column="2" border>
                    <el-descriptions-item label="账单编号">{{ currentBill.id }}</el-descriptions-item>
                    <el-descriptions-item label="账单类型">{{ currentBill.type }}</el-descriptions-item>
                    <el-descriptions-item label="账期/月份">{{ currentBill.period }}</el-descriptions-item>
                    <el-descriptions-item label="生成日期">{{ currentBill.createDate }}</el-descriptions-item>
                    <el-descriptions-item label="截止日期">{{ currentBill.dueDate }}</el-descriptions-item>
                    <el-descriptions-item label="状态">{{ currentBill.status }}</el-descriptions-item>
                    <el-descriptions-item label="账单金额">¥{{ currentBill.amount.toFixed(2) }}</el-descriptions-item>
                    <el-descriptions-item label="滞纳金" v-if="currentBill.lateCharge > 0">
                        <span style="color: #f56c6c;">¥{{ currentBill.lateCharge.toFixed(2) }}</span>
                    </el-descriptions-item>
                </el-descriptions>
                
                <el-divider content-position="left">房屋信息</el-divider>
                <div style="padding: 0 20px;">
                    <p><strong>小区名称：</strong>龙湖·椿山公租房</p>
                    <p><strong>房屋地址：</strong>武清区京津路123号B座1203室</p>
                    <p><strong>建筑面积：</strong>60㎡</p>
                </div>
                
                <el-divider content-position="left">缴费明细</el-divider>
                <el-table :data="currentBill.items" border style="width: 100%">
                    <el-table-column prop="name" label="项目名称"></el-table-column>
                    <el-table-column prop="unit" label="单价"></el-table-column>
                    <el-table-column prop="quantity" label="数量"></el-table-column>
                    <el-table-column prop="amount" label="金额">
                        <template slot-scope="scope">
                            ¥{{ scope.row.amount.toFixed(2) }}
                        </template>
                    </el-table-column>
                </el-table>
                
                <div style="margin-top: 20px; text-align: right; padding-right: 20px;">
                    <p style="font-size: 16px;"><strong>合计金额：</strong> <span style="color: #f56c6c; font-weight: bold;">¥{{ currentBill.amount.toFixed(2) }}</span></p>
                </div>
                
                <el-divider content-position="left" v-if="currentBill.status === '已缴费'">支付信息</el-divider>
                <div style="padding: 0 20px;" v-if="currentBill.status === '已缴费'">
                    <p><strong>支付时间：</strong>{{ currentBill.payDate }}</p>
                    <p><strong>支付方式：</strong>{{ currentBill.payMethod }}</p>
                    <p><strong>交易流水：</strong>{{ currentBill.transactionNo }}</p>
                </div>
            </div>
            
            <div slot="footer">
                <el-button @click="detailDialogVisible = false">关闭</el-button>
                <el-button 
                    type="primary" 
                    @click="payBill(currentBill)"
                    v-if="currentBill && (currentBill.status === '未缴费' || currentBill.status === '已逾期')">
                    缴费
                </el-button>
                <el-button 
                    type="success" 
                    @click="getInvoice(currentBill)"
                    v-if="currentBill && currentBill.status === '已缴费'">
                    查看电子发票
                </el-button>
            </div>
        </el-dialog>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/index.js"></script>
    
    <script>
        new Vue({
            el: '#billApp',
            data() {
                return {
                    // 筛选表单
                    filterForm: {
                        status: '',
                        type: '',
                        dateRange: []
                    },
                    
                    // 账单统计
                    summary: {
                        unpaid: 5,
                        totalAmount: 3800,
                        nearDue: 2
                    },
                    
                    // 账单列表
                    bills: [
                        {
                            id: 'B202306001',
                            type: '租金',
                            period: '2023年6月',
                            amount: 1200,
                            dueDate: '2023-06-15',
                            status: '未缴费',
                            createDate: '2023-06-01',
                            lateCharge: 0,
                            items: [
                                { name: '租金-龙湖·椿山公租房', unit: '1200元/月', quantity: '1个月', amount: 1200 }
                            ]
                        },
                        {
                            id: 'B202306002',
                            type: '物业费',
                            period: '2023年6月',
                            amount: 48,
                            dueDate: '2023-06-15',
                            status: '未缴费',
                            createDate: '2023-06-01',
                            lateCharge: 0,
                            items: [
                                { name: '物业费', unit: '0.8元/㎡/月', quantity: '60㎡', amount: 48 }
                            ]
                        },
                        {
                            id: 'B202306003',
                            type: '水电费',
                            period: '2023年5月',
                            amount: 152,
                            dueDate: '2023-06-10',
                            status: '已逾期',
                            createDate: '2023-06-01',
                            lateCharge: 15.20,
                            items: [
                                { name: '水费', unit: '4.8元/吨', quantity: '12吨', amount: 57.6 },
                                { name: '电费', unit: '0.58元/度', quantity: '163度', amount: 94.54 }
                            ]
                        },
                        {
                            id: 'B202305001',
                            type: '租金',
                            period: '2023年5月',
                            amount: 1200,
                            dueDate: '2023-05-15',
                            status: '已缴费',
                            createDate: '2023-05-01',
                            payDate: '2023-05-12',
                            payMethod: '微信支付',
                            transactionNo: 'WX20230512123456',
                            lateCharge: 0,
                            items: [
                                { name: '租金-龙湖·椿山公租房', unit: '1200元/月', quantity: '1个月', amount: 1200 }
                            ]
                        },
                        {
                            id: 'B202305002',
                            type: '物业费',
                            period: '2023年5月',
                            amount: 48,
                            dueDate: '2023-05-15',
                            status: '已缴费',
                            createDate: '2023-05-01',
                            payDate: '2023-05-12',
                            payMethod: '微信支付',
                            transactionNo: 'WX20230512123457',
                            lateCharge: 0,
                            items: [
                                { name: '物业费', unit: '0.8元/㎡/月', quantity: '60㎡', amount: 48 }
                            ]
                        },
                        {
                            id: 'B202304001',
                            type: '租金',
                            period: '2023年4月',
                            amount: 1200,
                            dueDate: '2023-04-15',
                            status: '已缴费',
                            createDate: '2023-04-01',
                            payDate: '2023-04-10',
                            payMethod: '支付宝',
                            transactionNo: 'ZFB20230410987654',
                            lateCharge: 0,
                            items: [
                                { name: '租金-龙湖·椿山公租房', unit: '1200元/月', quantity: '1个月', amount: 1200 }
                            ]
                        }
                    ],
                    
                    // 分页数据
                    currentPage: 1,
                    totalBills: 20,
                    
                    // 选中的账单
                    selectedBills: [],
                    
                    // 当前操作的账单
                    currentBill: null,
                    
                    // 对话框控制
                    paymentDialogVisible: false,
                    detailDialogVisible: false,
                    proxyDialogVisible: false,
                    
                    // 支付方式
                    paymentMethod: 'wechat',
                    
                    // 代缴二维码
                    showProxyQr: false
                };
            },
            computed: {
                // 支付方式文本
                paymentMethodText() {
                    const methodMap = {
                        'wechat': '微信',
                        'alipay': '支付宝',
                        'bank': '银行APP'
                    };
                    return methodMap[this.paymentMethod] || '';
                }
            },
            methods: {
                // 重置筛选条件
                resetFilter() {
                    this.filterForm = {
                        status: '',
                        type: '',
                        dateRange: []
                    };
                },
                
                // 查询账单
                searchBills() {
                    // 模拟查询操作
                    this.$message({
                        message: '查询条件已应用',
                        type: 'success'
                    });
                    // 实际应用中会调用API
                },
                
                // 行样式
                tableRowClassName({row}) {
                    if (row.status === '已逾期') {
                        return 'warning-row';
                    }
                    return '';
                },
                
                // 获取账单状态样式
                getBillStatusClass(status) {
                    const classMap = {
                        '未缴费': 'status-unpaid',
                        '已缴费': 'status-paid',
                        '处理中': 'status-processing',
                        '已逾期': 'status-overdue'
                    };
                    return classMap[status] || '';
                },
                
                // 处理表格选择变化
                handleSelectionChange(val) {
                    this.selectedBills = val;
                },
                
                // 批量缴费
                batchPayBills() {
                    const unpaidBills = this.selectedBills.filter(bill => 
                        bill.status === '未缴费' || bill.status === '已逾期'
                    );
                    
                    if (unpaidBills.length === 0) {
                        this.$message.warning('请选择未缴费的账单');
                        return;
                    }
                    
                    const totalAmount = unpaidBills.reduce((sum, bill) => 
                        sum + bill.amount + (bill.lateCharge || 0), 0
                    );
                    
                    this.$confirm(`您选择了 ${unpaidBills.length} 笔账单，总金额 ¥${totalAmount.toFixed(2)}，是否确认缴费？`, '批量缴费', {
                        confirmButtonText: '确认',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.currentBill = {
                            id: '批量缴费',
                            type: '多笔账单',
                            amount: totalAmount,
                            lateCharge: 0,
                            period: unpaidBills.map(bill => bill.period).join('、')
                        };
                        this.paymentDialogVisible = true;
                    }).catch(() => {});
                },
                
                // 缴费单笔账单
                payBill(bill) {
                    this.currentBill = bill;
                    this.paymentDialogVisible = true;
                },
                
                // 查看账单详情
                viewBillDetail(bill) {
                    this.currentBill = bill;
                    this.detailDialogVisible = true;
                },
                
                // 查看电子发票
                getInvoice(bill) {
                    if (bill.status !== '已缴费') {
                        this.$message.warning('请先完成缴费');
                        return;
                    }
                    
                    this.$message({
                        message: '电子发票下载中...',
                        type: 'success'
                    });
                    // 实际应用中处理发票下载或查看逻辑
                },
                
                // 确认支付
                confirmPayment() {
                    // 模拟支付成功流程
                    this.$message({
                        message: '支付成功',
                        type: 'success'
                    });
                    
                    // 更新账单状态
                    if (this.currentBill.id === '批量缴费') {
                        this.selectedBills.forEach(bill => {
                            if (bill.status === '未缴费' || bill.status === '已逾期') {
                                bill.status = '已缴费';
                                bill.payDate = new Date().toLocaleDateString();
                                bill.payMethod = this.paymentMethodText;
                                bill.transactionNo = this.generateTransactionNo();
                            }
                        });
                    } else {
                        const bill = this.bills.find(b => b.id === this.currentBill.id);
                        if (bill) {
                            bill.status = '已缴费';
                            bill.payDate = new Date().toLocaleDateString();
                            bill.payMethod = this.paymentMethodText;
                            bill.transactionNo = this.generateTransactionNo();
                        }
                    }
                    
                    // 更新统计数据
                    this.updateSummary();
                    
                    // 关闭对话框
                    this.paymentDialogVisible = false;
                },
                
                // 更新统计数据
                updateSummary() {
                    const unpaidBills = this.bills.filter(bill => bill.status === '未缴费' || bill.status === '已逾期');
                    const totalAmount = unpaidBills.reduce((sum, bill) => sum + bill.amount + (bill.lateCharge || 0), 0);
                    const nearDueBills = unpaidBills.filter(bill => {
                        const dueDate = new Date(bill.dueDate);
                        const now = new Date();
                        const diffDays = Math.ceil((dueDate - now) / (1000 * 60 * 60 * 24));
                        return diffDays >= 0 && diffDays <= 3;
                    });
                    
                    this.summary.unpaid = unpaidBills.length;
                    this.summary.totalAmount = totalAmount;
                    this.summary.nearDue = nearDueBills.length;
                },
                
                // 生成交易流水号
                generateTransactionNo() {
                    const prefix = this.paymentMethod === 'wechat' ? 'WX' : 
                                  this.paymentMethod === 'alipay' ? 'ZFB' : 'BANK';
                    const date = new Date().toISOString().replace(/[-:T.]/g, '').substring(0, 14);
                    const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
                    return `${prefix}${date}${random}`;
                },
                
                // 显示代缴服务
                showProxyPayment() {
                    this.proxyDialogVisible = true;
                    this.showProxyQr = false;
                },
                
                // 生成代缴二维码
                generateProxyQrCode() {
                    this.showProxyQr = true;
                },
                
                // 分页变化
                handleCurrentChange(val) {
                    this.currentPage = val;
                    // 实际应用中会加载对应页的数据
                }
            },
            mounted() {
                // 初始化时更新统计数据
                this.updateSummary();
            }
        });
    </script>
</body>
</html> 