<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue测试</title>
    <link rel="stylesheet" href="element-ui.css">
</head>
<body>
    <div id="app">
        <h1>{{ message }}</h1>
        <el-button @click="changeMessage">点击测试</el-button>
        <p>当前时间：{{ currentTime }}</p>
    </div>

    <script src="vue.js"></script>
    <script src="element-ui.js"></script>
    <script>
        new Vue({
            el: '#app',
            data: {
                message: 'Vue.js 正常工作！',
                currentTime: new Date().toLocaleString()
            },
            methods: {
                changeMessage() {
                    this.message = 'Vue.js 响应正常！';
                    this.currentTime = new Date().toLocaleString();
                }
            }
        });
    </script>
</body>
</html>
