<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电子签约 - 公租房经租管理平台</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/theme-chalk/index.css">
    <style>
        body {
            font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        
        .contract-header {
            margin-bottom: 20px;
        }
        
        .contract-step {
            margin-bottom: 30px;
        }
        
        .contract-content {
            background-color: #fff;
            padding: 20px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
            line-height: 1.8;
        }
        
        .highlight {
            background-color: #fdf5e6;
            padding: 5px;
        }
        
        .signature-pad {
            border: 1px dashed #ccc;
            height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            background-color: #fff;
            cursor: pointer;
        }
        
        .signature-preview {
            height: 100%;
            width: 100%;
        }
        
        .face-scan {
            border: 1px dashed #ccc;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            background-color: #fff;
        }
        
        .timer {
            color: #f56c6c;
            font-weight: bold;
        }
        
        .contract-action {
            margin-top: 20px;
            text-align: center;
        }
        
        .resident-list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ebeef5;
        }
    </style>
</head>
<body>
    <div id="contractSignApp">
        <div class="contract-header">
            <h2>电子签约</h2>
            <el-alert
                title="签约前请确认个人信息准确无误，一旦签署将具有法律效力"
                type="warning"
                :closable="false"
                show-icon>
            </el-alert>
        </div>
        
        <!-- 步骤条 -->
        <div class="contract-step">
            <el-steps :active="active" finish-status="success" align-center>
                <el-step title="确认房源信息"></el-step>
                <el-step title="确认合同条款"></el-step>
                <el-step title="人脸识别认证"></el-step>
                <el-step title="电子签名"></el-step>
                <el-step title="签约成功"></el-step>
            </el-steps>
        </div>
        
        <!-- 步骤1: 确认房源信息 -->
        <div v-show="active === 0">
            <el-card>
                <div slot="header">
                    <span>房源信息确认</span>
                </div>
                
                <el-row :gutter="20">
                    <el-col :span="16">
                        <el-descriptions title="基本信息" :column="2" border>
                            <el-descriptions-item label="合同编号">CT20230002</el-descriptions-item>
                            <el-descriptions-item label="申请编号">A20230015</el-descriptions-item>
                            <el-descriptions-item label="小区名称">龙湖·椿山公租房</el-descriptions-item>
                            <el-descriptions-item label="房屋编号">B座-1203</el-descriptions-item>
                            <el-descriptions-item label="房屋地址">武清区京津路123号B座1203室</el-descriptions-item>
                            <el-descriptions-item label="建筑面积">60㎡</el-descriptions-item>
                            <el-descriptions-item label="户型">两室一厅</el-descriptions-item>
                            <el-descriptions-item label="朝向">南</el-descriptions-item>
                            <el-descriptions-item label="楼层">12层/18层</el-descriptions-item>
                            <el-descriptions-item label="装修状况">简装</el-descriptions-item>
                            <el-descriptions-item label="月租金">1200元</el-descriptions-item>
                            <el-descriptions-item label="租赁期限">3年</el-descriptions-item>
                            <el-descriptions-item label="物业管理费">0.8元/月/㎡</el-descriptions-item>
                            <el-descriptions-item label="其他费用">水电气按实际使用收取</el-descriptions-item>
                        </el-descriptions>
                        
                        <el-divider content-position="left">配套设施</el-divider>
                        <el-row>
                            <el-col :span="6" v-for="item in facilities" :key="item">
                                <div style="padding: 5px 0;"><i class="el-icon-check" style="color: #67c23a; margin-right: 5px;"></i>{{ item }}</div>
                            </el-col>
                        </el-row>
                    </el-col>
                    
                    <el-col :span="8">
                        <div style="background-color: #f0f0f0; height: 200px; display: flex; align-items: center; justify-content: center; margin-bottom: 15px;">
                            <i class="el-icon-picture-outline" style="font-size: 36px; color: #909399;"></i>
                        </div>
                        
                        <el-alert
                            title="请确认房源信息，如有疑问请及时咨询管理员"
                            type="info"
                            :closable="false"
                            show-icon>
                        </el-alert>
                        
                        <el-divider content-position="left">承租人信息</el-divider>
                        <div>
                            <p><strong>姓名：</strong>李四</p>
                            <p><strong>身份证号：</strong>12010119880101****</p>
                            <p><strong>联系电话：</strong>135****8888</p>
                        </div>
                    </el-col>
                </el-row>
                
                <div style="text-align: center; margin-top: 20px;">
                    <el-checkbox v-model="confirmInfo">我已确认房源信息无误，同意签约</el-checkbox>
                </div>
            </el-card>
            
            <div class="contract-action">
                <el-button type="primary" @click="next" :disabled="!confirmInfo">下一步</el-button>
            </div>
        </div>
        
        <!-- 步骤2: 确认合同条款 -->
        <div v-show="active === 1">
            <el-card>
                <div slot="header">
                    <span>合同条款确认</span>
                    <span style="float: right; color: #909399; font-size: 14px;">
                        阅读时间: <span class="timer">{{ readTime }}s</span>
                    </span>
                </div>
                
                <div class="contract-content">
                    <h3 style="text-align: center;">公共租赁住房租赁合同</h3>
                    <p>合同编号：CT20230002</p>
                    <p>出租方（甲方）：XX市住房保障中心</p>
                    <p>承租方（乙方）：李四</p>
                    
                    <h4>第一条 房屋基本情况</h4>
                    <p>甲方将位于武清区京津路123号B座1203室的公共租赁住房出租给乙方使用。该房屋建筑面积60平方米，户型为两室一厅。</p>
                    
                    <h4>第二条 租赁期限</h4>
                    <p>租赁期限为3年，自2023年7月1日起至2026年6月30日止。</p>
                    
                    <h4>第三条 租金及支付方式</h4>
                    <p>1. 月租金标准为人民币1200元。</p>
                    <p>2. 租金支付方式为按季度支付，乙方应于每季度首月的10日前支付该季度的租金。</p>
                    <p>3. 乙方可通过线上支付系统或银行转账方式支付租金。</p>
                    
                    <h4>第四条 押金</h4>
                    <p>1. 乙方应在签订本合同时向甲方支付相当于2个月租金的押金，共计人民币2400元。</p>
                    <p>2. 租赁期满或合同解除后，甲方应在乙方交还房屋并结清相关费用后7个工作日内，将押金退还乙方，不计利息。</p>
                    
                    <h4>第五条 各项费用的缴纳</h4>
                    <p>1. 乙方应按时缴纳水、电、燃气、物业等费用。</p>
                    <p>2. 物业管理费标准为0.8元/月/平方米，由乙方按月支付。</p>
                    
                    <h4>第六条 居住人口变更</h4>
                    <p class="highlight">1. 乙方及其共同居住的家庭成员应在甲方处进行实名登记，未经甲方同意，不得擅自增加居住人口。</p>
                    <p class="highlight">2. 乙方需变更同住家庭成员的，应提前15日向甲方提出书面申请，并提供相关证明材料。</p>
                    
                    <h4>第七条 房屋使用要求</h4>
                    <p class="highlight">1. 乙方应合理使用并爱护房屋及其附属设施，不得擅自改变房屋结构和用途。</p>
                    <p class="highlight">2. 乙方不得利用承租房屋从事违法活动，不得损害公共利益，不得擅自转租或转借他人。</p>
                    <p class="highlight">3. 违反上述规定，甲方有权解除合同，收回房屋，并由乙方承担相应责任。</p>
                    
                    <h4>第八条 房屋维修责任</h4>
                    <p>1. 房屋及其附属设施的日常维修由乙方负责。</p>
                    <p>2. 房屋主体结构的维修由甲方负责。</p>
                    <p>3. 因乙方使用不当造成的损坏，维修费用由乙方承担。</p>
                    
                    <h4>第九条 合同解除条件</h4>
                    <p class="highlight">有下列情形之一的，甲方可以解除合同，收回房屋：</p>
                    <p class="highlight">1. 乙方擅自转租、转借承租的公共租赁住房；</p>
                    <p class="highlight">2. 乙方改变公共租赁住房用途；</p>
                    <p class="highlight">3. 乙方拖欠租金累计达3个月；</p>
                    <p class="highlight">4. 乙方在公共租赁住房内从事违法活动；</p>
                    <p class="highlight">5. 乙方破坏承租公共租赁住房，在甲方规定时间内拒不恢复原状；</p>
                    <p class="highlight">6. 乙方取得其他住房或不再符合公共租赁住房保障条件；</p>
                    <p class="highlight">7. 法律、法规规定的其他情形。</p>
                    
                    <h4>第十条 合同期满续租</h4>
                    <p>合同期满，乙方需继续承租的，应当在合同期满前30日向甲方提出书面续租申请。乙方符合续租条件的，甲方应当与乙方续签租赁合同。</p>
                    
                    <h4>第十一条 其他约定</h4>
                    <p>本合同未尽事宜，由甲乙双方协商解决；协商不成的，提交房屋所在地人民法院诉讼解决。</p>
                    
                    <p style="margin-top: 30px;">甲方（盖章）：</p>
                    <p>代表人（签字）：</p>
                    <p>日期：2023年7月1日</p>
                    
                    <p style="margin-top: 30px;">乙方（签字）：</p>
                    <p>日期：2023年7月1日</p>
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <el-checkbox v-model="confirmContract" :disabled="readTime > 0">我已阅读并理解以上合同条款，同意遵守</el-checkbox>
                </div>
            </el-card>
            
            <div class="contract-action">
                <el-button @click="prev">上一步</el-button>
                <el-button type="primary" @click="next" :disabled="!confirmContract">下一步</el-button>
            </div>
        </div>
        
        <!-- 步骤3: 人脸识别认证 -->
        <div v-show="active === 2">
            <el-card>
                <div slot="header">
                    <span>人脸识别认证</span>
                </div>
                
                <el-row :gutter="20">
                    <el-col :span="12">
                        <div class="face-scan">
                            <div style="text-align: center;">
                                <i class="el-icon-user" style="font-size: 48px; color: #909399;"></i>
                                <p style="margin-top: 10px;">请将脸部对准摄像头</p>
                                <el-button type="primary" @click="startFaceScan">开始识别</el-button>
                            </div>
                        </div>
                        
                        <el-alert
                            title="请确保光线充足，面部无遮挡"
                            type="info"
                            :closable="false"
                            show-icon>
                        </el-alert>
                    </el-col>
                    
                    <el-col :span="12">
                        <el-card shadow="never">
                            <div slot="header">
                                <span>身份验证</span>
                            </div>
                            
                            <div style="padding: 10px 0;">
                                <p><strong>姓名：</strong>李四</p>
                                <p><strong>身份证号：</strong>12010119880101****</p>
                                <p><strong>比对结果：</strong><span style="color: #67c23a;" v-if="faceVerified">验证成功</span><span v-else>等待验证</span></p>
                            </div>
                        </el-card>
                        
                        <el-divider content-position="left">共同居住人</el-divider>
                        
                        <div class="resident-list">
                            <div class="resident-list-item" v-for="(resident, index) in residents" :key="index">
                                <span>{{ resident.name }} ({{ resident.relation }})</span>
                                <div>
                                    <el-button size="mini" type="text" @click="editResident(index)">编辑</el-button>
                                    <el-button size="mini" type="text" style="color: #f56c6c;" @click="removeResident(index)">删除</el-button>
                                </div>
                            </div>
                            
                            <div style="margin-top: 15px;">
                                <el-button size="small" icon="el-icon-plus" @click="addResident">添加共同居住人</el-button>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </el-card>
            
            <div class="contract-action">
                <el-button @click="prev">上一步</el-button>
                <el-button type="primary" @click="next" :disabled="!faceVerified">下一步</el-button>
            </div>
            
            <!-- 添加/编辑共同居住人对话框 -->
            <el-dialog :title="isEditingResident ? '编辑共同居住人' : '添加共同居住人'" :visible.sync="residentDialogVisible" width="50%">
                <el-form :model="residentForm" label-width="100px">
                    <el-form-item label="姓名">
                        <el-input v-model="residentForm.name"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="身份证号">
                        <el-input v-model="residentForm.idCard"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="与承租人关系">
                        <el-select v-model="residentForm.relation" style="width: 100%;">
                            <el-option label="配偶" value="配偶"></el-option>
                            <el-option label="子女" value="子女"></el-option>
                            <el-option label="父母" value="父母"></el-option>
                            <el-option label="其他亲属" value="其他亲属"></el-option>
                        </el-select>
                    </el-form-item>
                    
                    <el-form-item label="联系电话">
                        <el-input v-model="residentForm.phone"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="人脸识别">
                        <div class="face-scan" style="height: 150px;">
                            <div style="text-align: center;">
                                <i class="el-icon-user" style="font-size: 36px; color: #909399;"></i>
                                <p style="margin-top: 10px;">请上传人脸照片或进行识别</p>
                                <el-button size="small" type="primary">开始识别</el-button>
                            </div>
                        </div>
                    </el-form-item>
                </el-form>
                
                <div slot="footer">
                    <el-button @click="residentDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="saveResident">保存</el-button>
                </div>
            </el-dialog>
        </div>
        
        <!-- 步骤4: 电子签名 -->
        <div v-show="active === 3">
            <el-card>
                <div slot="header">
                    <span>电子签名</span>
                </div>
                
                <el-row :gutter="20">
                    <el-col :span="12">
                        <p>请在下方签名框内进行签名：</p>
                        <div class="signature-pad" @click="startSignature">
                            <div v-if="!signature">
                                <i class="el-icon-edit" style="font-size: 36px; color: #909399;"></i>
                                <p style="margin-top: 10px; color: #909399;">点击此处进行签名</p>
                            </div>
                            <img v-else :src="signature" class="signature-preview" alt="签名">
                        </div>
                        
                        <div style="text-align: right; margin-top: 10px;">
                            <el-button size="small" @click="clearSignature" :disabled="!signature">清除签名</el-button>
                        </div>
                        
                        <el-alert
                            title="电子签名具有法律效力，等同于纸质合同上的手写签名"
                            type="warning"
                            :closable="false"
                            show-icon>
                        </el-alert>
                    </el-col>
                    
                    <el-col :span="12">
                        <el-card shadow="never">
                            <div slot="header">
                                <span>签约信息</span>
                            </div>
                            
                            <div style="padding: 10px 0;">
                                <p><strong>合同编号：</strong>CT20230002</p>
                                <p><strong>承租人：</strong>李四</p>
                                <p><strong>房源地址：</strong>武清区京津路123号B座1203室</p>
                                <p><strong>租期：</strong>2023年7月1日 至 2026年6月30日</p>
                                <p><strong>月租金：</strong>1200元</p>
                                <p><strong>押金金额：</strong>2400元</p>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
            </el-card>
            
            <div class="contract-action">
                <el-button @click="prev">上一步</el-button>
                <el-button type="primary" @click="next" :disabled="!signature">提交签约</el-button>
            </div>
        </div>
        
        <!-- 步骤5: 签约成功 -->
        <div v-show="active === 4">
            <el-card>
                <div style="text-align: center; padding: 30px 0;">
                    <i class="el-icon-success" style="font-size: 72px; color: #67c23a;"></i>
                    <h2>恭喜，签约成功！</h2>
                    <p style="margin: 20px 0; color: #606266;">您已完成电子签约流程，合同将存储于区块链，确保安全可靠</p>
                    <p style="color: #606266;">请按约定支付押金，钥匙交付将在押金缴纳后安排</p>
                    
                    <div style="margin: 30px 0;">
                        <el-button type="primary" @click="goToPayment">立即支付押金</el-button>
                        <el-button @click="downloadContract">下载合同</el-button>
                    </div>
                    
                    <el-alert
                        title="合同信息已发送到您的手机，请注意查收"
                        type="success"
                        :closable="false"
                        show-icon>
                    </el-alert>
                </div>
            </el-card>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/index.js"></script>
    
    <script>
        new Vue({
            el: '#contractSignApp',
            data() {
                return {
                    active: 0, // 当前步骤
                    
                    // 步骤1数据
                    confirmInfo: false,
                    facilities: ['电梯', '家具', '热水器', '空调', '洗衣机', '宽带', '燃气灶', '油烟机'],
                    
                    // 步骤2数据
                    confirmContract: false,
                    readTime: 30, // 剩余阅读时间（秒）
                    readTimer: null,
                    
                    // 步骤3数据
                    faceVerified: false,
                    residents: [
                        { name: '张三', relation: '配偶', idCard: '12010119901010****', phone: '139****5678' }
                    ],
                    residentDialogVisible: false,
                    residentForm: {
                        name: '',
                        idCard: '',
                        relation: '',
                        phone: ''
                    },
                    isEditingResident: false,
                    editingResidentIndex: -1,
                    
                    // 步骤4数据
                    signature: ''
                };
            },
            methods: {
                // 下一步
                next() {
                    // 处理特殊步骤
                    if (this.active === 1) {
                        // 进入步骤2后，清除计时器
                        clearInterval(this.readTimer);
                    }
                    
                    // 移动到下一步
                    if (this.active++ > 4) this.active = 0;
                    
                    // 特殊步骤处理
                    if (this.active === 1) {
                        // 设置阅读合同的计时器
                        this.startContractTimer();
                    }
                },
                
                // 上一步
                prev() {
                    // 处理特殊步骤
                    if (this.active === 1) {
                        // 退出步骤2时，清除计时器
                        clearInterval(this.readTimer);
                    }
                    
                    // 移动到上一步
                    if (this.active-- < 0) this.active = 0;
                },
                
                // 开始合同阅读计时器
                startContractTimer() {
                    this.readTime = 30;
                    this.confirmContract = false;
                    
                    this.readTimer = setInterval(() => {
                        if (this.readTime > 0) {
                            this.readTime--;
                        } else {
                            clearInterval(this.readTimer);
                        }
                    }, 1000);
                },
                
                // 人脸识别
                startFaceScan() {
                    // 模拟人脸识别过程
                    this.$message({
                        message: '正在进行人脸识别...',
                        type: 'info'
                    });
                    
                    setTimeout(() => {
                        this.faceVerified = true;
                        this.$message({
                            message: '人脸识别成功！',
                            type: 'success'
                        });
                    }, 2000);
                },
                
                // 添加共同居住人
                addResident() {
                    this.residentForm = {
                        name: '',
                        idCard: '',
                        relation: '',
                        phone: ''
                    };
                    this.isEditingResident = false;
                    this.residentDialogVisible = true;
                },
                
                // 编辑共同居住人
                editResident(index) {
                    this.residentForm = JSON.parse(JSON.stringify(this.residents[index]));
                    this.isEditingResident = true;
                    this.editingResidentIndex = index;
                    this.residentDialogVisible = true;
                },
                
                // 删除共同居住人
                removeResident(index) {
                    this.$confirm('确定要删除该共同居住人吗?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.residents.splice(index, 1);
                        this.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                    }).catch(() => {});
                },
                
                // 保存共同居住人
                saveResident() {
                    if (!this.residentForm.name || !this.residentForm.idCard || !this.residentForm.relation) {
                        this.$message.error('请填写完整信息');
                        return;
                    }
                    
                    if (this.isEditingResident) {
                        // 更新现有居住人
                        this.$set(this.residents, this.editingResidentIndex, JSON.parse(JSON.stringify(this.residentForm)));
                    } else {
                        // 添加新居住人
                        this.residents.push(JSON.parse(JSON.stringify(this.residentForm)));
                    }
                    
                    this.residentDialogVisible = false;
                },
                
                // 开始签名
                startSignature() {
                    // 模拟签名过程
                    // 实际应用中应接入签名板或手写板组件
                    this.signature = 'https://via.placeholder.com/400x150?text=电子签名';
                },
                
                // 清除签名
                clearSignature() {
                    this.signature = '';
                },
                
                // 前往支付页面
                goToPayment() {
                    this.$message({
                        message: '即将跳转到押金支付页面...',
                        type: 'success'
                    });
                    // 实际应用中跳转到支付页面
                    // window.location.href = '../payment/deposit.html';
                },
                
                // 下载合同
                downloadContract() {
                    this.$message({
                        message: '合同下载中...',
                        type: 'success'
                    });
                    // 实际应用中处理合同下载逻辑
                }
            },
            // 组件销毁前清除定时器
            beforeDestroy() {
                if (this.readTimer) {
                    clearInterval(this.readTimer);
                }
            }
        });
    </script>
</body>
</html> 