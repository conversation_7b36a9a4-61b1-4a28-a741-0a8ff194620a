# 公租房智慧管理平台 - 独立页面版本

本文件夹包含公租房智慧管理平台的独立页面版本，每个功能模块都是一个独立的HTML文件，可以单独运行。

## 📁 文件结构

```
standalone-pages/
├── assets/                    # 本地资源文件
│   ├── vue.js                # Vue.js 框架
│   ├── element-ui.js         # Element UI 组件库
│   ├── element-ui.css        # Element UI 样式
│   └── README.md             # 资源说明
├── index.html                # 首页（统计概览和模块导航）
├── house.html                # 房源管理页面
├── person.html               # 人员管理页面
├── apply.html                # 配租申请页面
├── contract.html             # 合同管理页面
├── payment.html              # 租金管理页面
├── repair.html               # 维修服务页面
├── exit.html                 # 退租管理页面
├── monitor.html              # 动态监管页面
├── download-assets.ps1       # 资源下载脚本
└── README.md                 # 本说明文件
```

## 🚀 快速开始

### 1. 确保资源文件已下载
所有必要的资源文件（Vue.js、Element UI）已经下载到 `assets/` 文件夹中，无需额外下载。

### 2. 直接打开页面
每个HTML文件都可以直接在浏览器中打开：
- 双击任意HTML文件
- 或者在浏览器中打开文件路径

### 3. 页面导航
- 每个页面左侧都有完整的导航菜单
- 点击菜单项可以跳转到对应的页面
- 面包屑导航显示当前页面位置

## 📋 页面功能说明

### 🏠 首页 (index.html)
- 系统统计数据展示
- 各功能模块快速入口
- 数据概览卡片

### 🏘️ 房源管理 (house.html)
- 房源信息查询和筛选
- 新增/编辑房源信息
- 批量生成房源功能
- 房源状态管理

### 👥 人员管理 (person.html)
- 主申请人信息管理
- 家庭成员信息管理
- 申请人状态跟踪
- 批量导入功能

### 📝 配租申请 (apply.html)
- 配租记录查询
- 配租统计数据
- 新增配租申请
- 调房、换房、主申请人变更

### 📄 合同管理 (contract.html)
- 合同信息查询
- 合同状态管理
- 合同签署流程
- 合同编辑功能

### 💰 租金管理 (payment.html)
- 租金账单管理
- 支付状态跟踪
- 账单生成功能
- 收据管理

### 🔧 维修服务 (repair.html)
- 维修工单管理
- 工单状态跟踪
- 紧急程度分类
- 维修进度管理

### 🚪 退租管理 (exit.html)
- 退租申请管理
- 退租流程跟踪
- 退租原因分析
- 退租审核功能

### 📊 动态监管 (monitor.html)
- 风险预警系统
- 信用记录管理
- 监管统计数据
- 预警处理功能

## 🎨 技术特点

### 前端技术栈
- **Vue.js 2.x**: 响应式前端框架
- **Element UI**: 企业级UI组件库
- **原生CSS**: 自定义样式和响应式设计

### 设计特色
- **响应式布局**: 适配不同屏幕尺寸
- **现代化UI**: 圆角、阴影、渐变等现代设计元素
- **交互友好**: 悬停效果、过渡动画
- **一致性**: 统一的设计语言和交互模式

### 本地化特性
- **完全离线**: 所有资源都在本地，无需网络连接
- **独立运行**: 每个页面都可以独立打开和使用
- **快速加载**: 本地资源加载速度快

## 🔧 自定义和扩展

### 修改样式
每个HTML文件的 `<style>` 标签中包含了页面样式，可以直接修改：
- 颜色主题
- 布局尺寸
- 动画效果
- 响应式断点

### 添加功能
在每个页面的Vue实例中可以添加新的：
- 数据字段
- 方法函数
- 计算属性
- 生命周期钩子

### 数据对接
目前使用的是模拟数据，可以修改Vue实例中的methods来对接真实API：
- 替换模拟数据为API调用
- 添加错误处理
- 实现数据持久化

## 📱 浏览器兼容性

支持现代浏览器：
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 🤝 使用建议

1. **开发环境**: 建议使用本地服务器（如Live Server）来避免跨域问题
2. **生产部署**: 可以直接部署到任何静态文件服务器
3. **数据对接**: 根据实际需求修改数据源和API接口
4. **功能扩展**: 基于现有结构添加新的功能模块

## 📞 技术支持

如有问题或需要技术支持，请参考：
- Vue.js 官方文档: https://cn.vuejs.org/
- Element UI 官方文档: https://element.eleme.cn/
- 项目源码和原始设计

---

**注意**: 这是一个演示版本，实际使用时需要根据具体业务需求进行数据对接和功能完善。
