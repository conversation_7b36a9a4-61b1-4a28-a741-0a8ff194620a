<svg width="300" height="180" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#74b9ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0984e3;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="300" height="180" fill="url(#grad2)"/>
  <rect x="40" y="50" width="220" height="90" fill="#ffffff" opacity="0.9" rx="8"/>
  <rect x="50" y="70" width="25" height="35" fill="#e17055" rx="3"/>
  <rect x="80" y="70" width="25" height="35" fill="#e17055" rx="3"/>
  <rect x="110" y="70" width="25" height="35" fill="#e17055" rx="3"/>
  <rect x="140" y="70" width="25" height="35" fill="#e17055" rx="3"/>
  <rect x="170" y="70" width="25" height="35" fill="#e17055" rx="3"/>
  <rect x="200" y="70" width="25" height="35" fill="#e17055" rx="3"/>
  <rect x="230" y="70" width="20" height="35" fill="#8b4513" rx="3"/>
  <polygon points="40,50 150,25 260,50" fill="#e74c3c"/>
  <text x="150" y="100" font-family="Arial, sans-serif" font-size="12" fill="#333" text-anchor="middle">龙湖·椿山公租房</text>
  <text x="150" y="115" font-family="Arial, sans-serif" font-size="10" fill="#666" text-anchor="middle">两室一厅 | 60㎡</text>
</svg>
