<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 公租房智慧管理平台</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 200px;
            background-color: #001529;
            color: white;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: 1000;
            overflow-y: auto;
        }

        .header {
            background-color: #001529;
            color: white;
            padding: 12px 16px;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .logo i {
            margin-right: 8px;
            font-size: 24px;
            color: #1890ff;
        }

        .sidebar-nav {
            padding: 0;
        }

        .sidebar-nav .el-menu {
            background-color: #001529;
            border: none;
        }

        .sidebar-nav .el-menu-item {
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            line-height: 48px;
            border: none;
            margin: 2px 8px;
            border-radius: 6px;
            padding: 0 16px !important;
            transition: all 0.3s;
        }

        .sidebar-nav .el-menu-item i {
            margin-right: 8px;
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .sidebar-nav .el-menu-item:hover {
            background-color: rgba(24, 144, 255, 0.1);
            color: #1890ff;
        }

        .sidebar-nav .el-menu-item.is-active {
            background-color: #1890ff;
            color: white;
            position: relative;
        }

        .sidebar-nav .el-menu-item.is-active::before {
            content: '';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background-color: white;
            border-radius: 0 2px 2px 0;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 200px;
            display: flex;
            flex-direction: column;
            min-width: 0;
            overflow-x: hidden;
        }

        .top-header {
            background: white;
            padding: 8px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
        }

        .user-info {
            display: flex;
            align-items: center;
            color: #666;
        }

        .user-info i {
            margin-right: 8px;
            color: #1890ff;
        }

        .content-area {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }

        .tab-content {
            background: transparent;
        }

        /* 面包屑导航样式 */
        .breadcrumb-container {
            margin-bottom: 15px;
            padding: 10px 0;
        }

        .el-breadcrumb {
            font-size: 14px;
        }

        .el-breadcrumb__item {
            color: #909399;
        }

        .el-breadcrumb__item:last-child {
            color: #409EFF;
            font-weight: 500;
        }

        /* 统计卡片样式优化 */
        .el-card {
            transition: all 0.3s ease;
        }

        .el-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        /* 表格样式优化 */
        .el-table {
            border-radius: 8px;
            overflow: hidden;
        }

        .el-table th {
            background-color: #f8f9fa;
            color: #495057;
            font-weight: 600;
        }

        /* 模块网格样式 */
        .module-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }

        .module-card {
            text-align: center;
            padding: 24px 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .module-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        .module-icon {
            font-size: 48px;
            color: #409EFF;
            margin-bottom: 12px;
            display: block;
        }

        .module-card div {
            font-size: 16px;
            font-weight: 500;
            color: #262626;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="header">
                    <div class="logo">
                        <i class="el-icon-s-home"></i>
                        公租房智慧管理平台
                    </div>
                </div>
                <div class="sidebar-nav">
                    <el-menu mode="vertical" default-active="home" :collapse="false">
                        <el-menu-item index="home" @click="navigateTo('home.html')">
                            <i class="el-icon-s-home"></i>
                            <span>首页</span>
                        </el-menu-item>
                        <el-menu-item index="house" @click="navigateTo('house.html')">
                            <i class="el-icon-house"></i>
                            <span>房源管理</span>
                        </el-menu-item>
                        <el-menu-item index="person" @click="navigateTo('person.html')">
                            <i class="el-icon-user-solid"></i>
                            <span>人员管理</span>
                        </el-menu-item>
                        <el-menu-item index="apply" @click="navigateTo('apply.html')">
                            <i class="el-icon-document-add"></i>
                            <span>配租申请</span>
                        </el-menu-item>
                        <el-menu-item index="contract" @click="navigateTo('contract.html')">
                            <i class="el-icon-document"></i>
                            <span>合同管理</span>
                        </el-menu-item>
                        <el-menu-item index="payment" @click="navigateTo('payment.html')">
                            <i class="el-icon-money"></i>
                            <span>租金管理</span>
                        </el-menu-item>
                        <el-menu-item index="repair" @click="navigateTo('repair.html')">
                            <i class="el-icon-setting"></i>
                            <span>维修服务</span>
                        </el-menu-item>
                        <el-menu-item index="exit" @click="navigateTo('exit.html')">
                            <i class="el-icon-switch-button"></i>
                            <span>退租管理</span>
                        </el-menu-item>
                        <el-menu-item index="monitor" @click="navigateTo('monitor.html')">
                            <i class="el-icon-view"></i>
                            <span>动态监管</span>
                        </el-menu-item>
                    </el-menu>
                </div>
            </div>

            <!-- 主内容区域 -->
            <div class="main-content">
                <!-- 顶部导航 -->
                <div class="top-header">
                    <div class="breadcrumb-container">
                        <el-breadcrumb separator="/">
                            <el-breadcrumb-item>
                                <i class="el-icon-s-home"></i>
                                首页
                            </el-breadcrumb-item>
                            <el-breadcrumb-item>
                                首页
                            </el-breadcrumb-item>
                        </el-breadcrumb>
                    </div>
                    <div class="user-info">
                        <i class="el-icon-user"></i>
                        <span>张三</span>
                    </div>
                </div>

                <!-- 内容区域 -->
                <div class="content-area">
                    <!-- 首页内容 -->
                    <div class="tab-content">
                        
                        <el-row :gutter="16" style="margin-bottom: 20px;">
                            <el-col :span="6" v-for="item in statistics" :key="item.title">
                                <el-card shadow="hover" style="border-radius: 8px;">
                                    <div style="display: flex; align-items: center; padding: 8px 0;">
                                        <i :class="item.icon" style="font-size: 36px; margin-right: 12px; color: #409EFF;"></i>
                                        <div>
                                            <div style="font-size: 22px; font-weight: bold; color: #262626;">{{ item.value }}</div>
                                            <div style="color: #8c8c8c; font-size: 14px;">{{ item.title }}</div>
                                        </div>
                                    </div>
                                </el-card>
                            </el-col>
                        </el-row>
                        
                        <h3 style="margin: 20px 0 16px 0; font-size: 16px; font-weight: 600; color: #262626;">系统模块</h3>
                        
                        <div class="module-grid">
                            <el-card shadow="hover" class="module-card" v-for="module in modules" :key="module.name" @click.native="handleTabChange(module.tab)">
                                <i :class="module.icon" class="module-icon"></i>
                                <div>{{ module.name }}</div>
                            </el-card>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    statistics: [
                        { title: '总房源数', value: '1,234', icon: 'el-icon-house' },
                        { title: '已出租', value: '856', icon: 'el-icon-check' },
                        { title: '空置房源', value: '378', icon: 'el-icon-warning' },
                        { title: '维修中', value: '12', icon: 'el-icon-setting' }
                    ],
                    modules: [
                        { name: '房源管理', icon: 'el-icon-house', tab: 'house' },
                        { name: '人员管理', icon: 'el-icon-user-solid', tab: 'person' },
                        { name: '配租申请', icon: 'el-icon-document-add', tab: 'apply' },
                        { name: '合同管理', icon: 'el-icon-document', tab: 'contract' },
                        { name: '租金管理', icon: 'el-icon-money', tab: 'payment' },
                        { name: '维修服务', icon: 'el-icon-setting', tab: 'repair' },
                        { name: '退租管理', icon: 'el-icon-switch-button', tab: 'exit' },
                        { name: '动态监管', icon: 'el-icon-view', tab: 'monitor' }
                    ]
                }
            },
            methods: {
                navigateTo(page) {
                    window.location.href = page;
                },
                handleTabChange(tab) {
                    this.navigateTo(tab + '.html');
                }
            }
        });
    </script>
</body>
</html>
